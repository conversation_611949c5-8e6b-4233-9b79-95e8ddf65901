name: Deploy

on:
    workflow_dispatch:
        inputs:
            apps:
                type: choice
                description: 'Apps to deploy'
                required: true
                default: 'all'
                options:
                    - 'all'
                    - 'api'
                    - 'aws-services'
                    - 'web'
            environment:
                type: choice
                description: 'Environment'
                required: true
                default: 'development'
                options:
                    - 'development'
                    - 'staging'
                    - 'production'
            create-release-branch:
                type: boolean
                description: Create release branch ?

run-name: Deploy to ${{ inputs.environment }}

jobs:
    setup-environment-variables:
        name: Setup ${{ inputs.environment }} variables
        runs-on: ubuntu-latest
        outputs:
            aws-ecs-cluster-name: ${{ steps.setup-env-vars.outputs.AWS_ECS_CLUSTER_NAME }}
            aws-ecs-service-name: ${{ steps.setup-env-vars.outputs.AWS_ECS_SERVICE_NAME }}
            aws-s3-bucket-name: ${{ steps.setup-env-vars.outputs.AWS_S3_BUCKET_NAME }}
            aws-cloudfront-distribution-id: ${{ steps.setup-env-vars.outputs.AWS_CLOUDFRONT_DISTRIBUTION_ID }}
        steps:
            - uses: actions/checkout@v4

            - name: Setup ${{ inputs.environment }} variables
              id: setup-env-vars
              run: |
                  if [[ ${{ inputs.environment }} == 'development' ]]; then
                    echo "AWS_ECS_CLUSTER_NAME=malou-dev" >> $GITHUB_OUTPUT
                    echo "AWS_ECS_SERVICE_NAME=development" >> $GITHUB_OUTPUT
                    echo "AWS_S3_BUCKET_NAME=app-malou-web-development" >> $GITHUB_OUTPUT
                    echo "AWS_CLOUDFRONT_DISTRIBUTION_ID=E1E704RBSYUISM" >> $GITHUB_OUTPUT
                  elif [[ ${{ inputs.environment }} == 'staging' ]]; then
                    echo "AWS_ECS_CLUSTER_NAME=malou-staging" >> $GITHUB_OUTPUT
                    echo "AWS_ECS_SERVICE_NAME=staging-2" >> $GITHUB_OUTPUT
                    echo "AWS_S3_BUCKET_NAME=app-malou-web-staging" >> $GITHUB_OUTPUT
                    echo "AWS_CLOUDFRONT_DISTRIBUTION_ID=E24CJY44VVKCAW" >> $GITHUB_OUTPUT
                  elif [[ ${{ inputs.environment }} == 'production' ]]; then
                    echo "AWS_ECS_CLUSTER_NAME=malou-production" >> $GITHUB_OUTPUT
                    echo "AWS_ECS_SERVICE_NAME=production" >> $GITHUB_OUTPUT
                    echo "AWS_S3_BUCKET_NAME=app-malou-web-production" >> $GITHUB_OUTPUT
                    echo "AWS_CLOUDFRONT_DISTRIBUTION_ID=E1NX6HSPOZ6N7F" >> $GITHUB_OUTPUT
                  fi

            - name: Display notice
              run: |
                  echo "::notice title=Deploy to ${{ inputs.environment }} from branch ${{ github.ref_name }}::Deploying..."

    build-and-deploy:
        name: Deploy
        needs: [setup-environment-variables]
        uses: ./.github/workflows/reusable-deploy.yml
        with:
            environment: ${{ inputs.environment }}
            aws-ecs-cluster-name: ${{ needs.setup-environment-variables.outputs.aws-ecs-cluster-name }}
            aws-ecs-service-name: ${{ needs.setup-environment-variables.outputs.aws-ecs-service-name }}
            apps: ${{ inputs.apps }}
            aws-s3-bucket-name: ${{ needs.setup-environment-variables.outputs.aws-s3-bucket-name }}
            aws-cloudfront-distribution-id: ${{ needs.setup-environment-variables.outputs.aws-cloudfront-distribution-id }}
        # Secrets can be passed by with inherit if workflows belong to the same organization
        secrets: inherit

    slack-notification:
        needs: [build-and-deploy]
        if: ${{ always() && (needs.build-and-deploy.result == 'success' ) }}
        name: Send Slack notification
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@v4

            - name: Send Slack notification
              uses: ./.github/actions/send-slack-notification
              with:
                  notification-origin: malou-${{ inputs.environment }}-${{ inputs.apps }}
                  slack-webhook-url: ${{ secrets.SLACK_WEBHOOK_URL }}
                  github-token: ${{ secrets.GITHUB_TOKEN }}

    create-release-branch:
        if: ${{ inputs.create-release-branch && inputs.environment == 'staging' }}
        needs: [build-and-deploy]
        name: Create release branch
        runs-on: ubuntu-latest
        steps:
            - name: Checkout
              uses: actions/checkout@v4
              with:
                  fetch-depth: '0'

            - name: Create release branch
              run: |
                  # Fetch all branches
                  git fetch --all

                  # Get the latest branch starting with "release_v1."
                  latest_branch=$(git branch -r | grep -Eo 'origin/release_v1\.[0-9]+' | sort -V | tail -n 1)

                  # Check if a branch was found
                  if [ -z "$latest_branch" ]; then
                    echo "No branches found with the prefix release_v1."
                    exit 1
                  fi

                  # Extract the minor version number from the branch name
                  minor_version=$(echo $latest_branch | grep -Eo 'v1\.[0-9]+' | cut -d '.' -f 2)
                  echo "Current release branch: $latest_branch"

                  # Increment the minor version, and create branch name
                  next_minor_version=$((minor_version + 1))
                  new_branch="release_v1.$next_minor_version"
                  echo "Creating new release branch: $new_branch"

                  # Create the new branch
                  git checkout -b $new_branch
                  git push origin $new_branch

                  echo "Created new branch: $new_branch"

            - name: Create pull-request between latest release branches
              env:
                  GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
              run: |
                  git fetch --all

                  # Get the latest two branches starting with "release_v1."
                  branches=$(git branch -r | grep -Eo 'origin/release_v1\.[0-9]+' | sort -V | tail -n 2)
                  branch_array=($branches)

                  # Add refs/heads/ prefix and remove origin/
                  second_latest_branch="refs/heads/${branch_array[0]#origin/}"
                  latest_branch="refs/heads/${branch_array[1]#origin/}"

                  # Create pull-request
                  gh pr create --head $second_latest_branch --base $latest_branch --title "Pulling $second_latest_branch into $latest_branch" --body ":crown: *An automated PR*"
