name: Deploy Store Locator

on:
    workflow_dispatch:
        inputs:
            configurationId:
                description: 'Configuration ID'
                required: true
                type: string
            environment:
                type: choice
                description: 'Environment'
                required: true
                default: 'development'
                options:
                    - 'qa'
                    - 'qa-2'
                    - 'qa-3'
                    - 'development'
                    - 'staging'
                    - 'production'
            baseApiUrl:
                description: 'API Base URL'
                type: string
            jobId:
                description: 'Deployment job ID (optional)'
                type: string

run-name: Env ${{ inputs.environment }}, Config ${{ inputs.configurationId }}

concurrency:
    group: ${{ github.workflow }}-${{ github.ref }}-${{ inputs.environment }}-${{ inputs.configurationId }}
    cancel-in-progress: true

env:
    PNPM_STORE_PATH: ~/.pnpm-store

jobs:
    set-up-env-vars:
        name: Setup environment variables
        runs-on: ubuntu-latest
        outputs:
            api_base_url: ${{ steps.set-vars.outputs.api_base_url }}
            malou_api_key_env: ${{ steps.set-vars.outputs.malou_api_key_env }}
        steps:
            - id: set-vars
              name: Set environment variables depending on environment
              run: |
                  # Determine baseApiUrl
                  if [ -n "${{ inputs.baseApiUrl }}" ]; then
                    BASE_API_URL="${{ inputs.baseApiUrl }}"
                  elif [ "${{ inputs.environment }}" = "production" ] || [ "${{ inputs.environment }}" = "qa" ] || [ "${{ inputs.environment }}" = "qa-2" ] || [ "${{ inputs.environment }}" = "qa-3" ]; then
                    BASE_API_URL="https://app.api.malou.io/api/v1"
                  elif [ "${{ inputs.environment }}" = "staging" ]; then
                    BASE_API_URL="https://staging.api.malou.io/api/v1"
                  else
                    BASE_API_URL="https://development.api.malou.io/api/v1"
                  fi
                  echo "api_base_url=$BASE_API_URL" >> $GITHUB_OUTPUT

                  if [ "${{ inputs.environment }}" = "production" ] || [ "${{ inputs.environment }}" = "qa" ] || [ "${{ inputs.environment }}" = "qa-2" ] || [ "${{ inputs.environment }}" = "qa-3" ]; then
                    echo "malou_api_key_env=MALOU_API_KEY_PRODUCTION" >> $GITHUB_OUTPUT
                  elif [ "${{ inputs.environment }}" = "staging" ]; then
                    echo "malou_api_key_env=MALOU_API_KEY_STAGING" >> $GITHUB_OUTPUT
                  else
                    echo "malou_api_key_env=MALOU_API_KEY_DEVELOPMENT" >> $GITHUB_OUTPUT
                  fi

    fetch-api:
        name: Fetch configuration from API
        runs-on: ubuntu-latest
        needs: [set-up-env-vars]
        outputs:
            cloudfront_distribution_id: ${{ steps.get_api_data.outputs.cloudfront_distribution_id }}
            s3_bucket_name: ${{ steps.get_api_data.outputs.s3_bucket_name }}
            group_name: ${{ steps.get_api_data.outputs.group_name }}
            base_url: ${{ steps.get_api_data.outputs.base_url }}
        env:
            STORE_LOCATOR_CONFIGURATION_URL: ${{ needs.set-up-env-vars.outputs.api_base_url }}/store-locator/configuration/${{ inputs.configurationId }}
        steps:
            - name: Call API and parse response
              id: get_api_data
              run: |
                  # Call API
                  RESPONSE=$(curl -s "${{ env.STORE_LOCATOR_CONFIGURATION_URL }}?api_key=${{ secrets[needs.set-up-env-vars.outputs.malou_api_key_env] }}")

                  # Parse response
                  CLOUDFRONT_DISTRIBUTION_ID=$(echo "$RESPONSE" | jq -r '.data.cloudfrontDistributionId')
                  GROUP_NAME=$(echo "$RESPONSE" | jq -r '.data.groupName')
                  BASE_URL=$(echo "$RESPONSE" | jq -r '.data.baseUrl')
                  S3_BUCKET_NAME=$(echo "$RESPONSE" | jq -r '.data.s3BucketName')

                  # Override values if environment is not production
                  if [ "${{ inputs.environment }}" = "qa" ]; then
                    CLOUDFRONT_DISTRIBUTION_ID="E2H0RYJ95M33U7"
                    S3_BUCKET_NAME="s3://store-locator-qa"
                  elif [ "${{ inputs.environment }}" = "qa-2" ]; then
                    CLOUDFRONT_DISTRIBUTION_ID="E9MB450H28AQW"
                    S3_BUCKET_NAME="s3://store-locator-qa-2"
                  elif [ "${{ inputs.environment }}" = "qa-3" ]; then
                    CLOUDFRONT_DISTRIBUTION_ID="E21FSVBQNUHPJA"
                    S3_BUCKET_NAME="s3://store-locator-qa-3"
                  elif [ "${{ inputs.configurationId }}" = "690233fb8ba45114ce4b51bb" ] || [ "${{ inputs.environment }}" = "staging" ] || [ "${{ inputs.environment }}" = "development" ]; then
                    CLOUDFRONT_DISTRIBUTION_ID="EKW2DD8GQMQYE"
                    S3_BUCKET_NAME="s3://store-locator-all-env-qa"
                  fi

                  echo "Response from API: $RESPONSE"
                  echo "CLOUDFRONT_DISTRIBUTION_ID: $CLOUDFRONT_DISTRIBUTION_ID"
                  echo "GROUP_NAME: $GROUP_NAME"
                  echo "BASE_URL: $BASE_URL"
                  echo "S3_BUCKET_NAME: $S3_BUCKET_NAME"

                  # Export them as outputs
                  echo "cloudfront_distribution_id=$CLOUDFRONT_DISTRIBUTION_ID" >> $GITHUB_OUTPUT
                  echo "group_name=$GROUP_NAME" >> $GITHUB_OUTPUT
                  echo "base_url=$BASE_URL" >> $GITHUB_OUTPUT
                  echo "s3_bucket_name=$S3_BUCKET_NAME" >> $GITHUB_OUTPUT

    deploy:
        runs-on: runs-on=${{ github.run_id }}/runner=16cpu-custom/extras=s3-cache
        name: Deploy Store Locator
        needs: [set-up-env-vars, fetch-api]
        outputs:
            status: ${{ job.status }}
        steps:
            # Necessary to fetch runsOn S3 cache
            - uses: runs-on/action@v1

            - name: Checkout repository
              uses: actions/checkout@v4

            - name: Setup Node and PNPM
              uses: ./.github/actions/setup-node-and-pnpm

            - name: Turbo caching
              uses: actions/cache/restore@v4
              with:
                  path: .turbo
                  key: ${{ runner.os }}-turbo-${{ github.ref_name }}
                  restore-keys: |
                      ${{ runner.os }}-turbo-main
                      ${{ runner.os }}-turbo-

            - name: Install dependencies
              run: pnpm install --filter @malou-io/store-locator...

            - name: Build dependencies
              run: pnpm run build --filter=@malou-io/store-locator... --cache-dir=.turbo

            - name: Astro cache restore
              uses: actions/cache/restore@v4
              with:
                  path: apps/store-locator/node_modules/.astro
                  key: ${{ runner.os }}-astro-${{ inputs.configurationId }}-${{ inputs.environment }}
                  restore-keys: |
                      ${{ runner.os }}-astro-${{ inputs.configurationId }}-

            - name: Build Astro pages
              run: pnpm run deploy
              working-directory: apps/store-locator
              env:
                  CONFIGURATION_ID: ${{ inputs.configurationId }}
                  API_BASE_URL: ${{ needs.set-up-env-vars.outputs.api_base_url }}
                  API_KEY: ${{ secrets[needs.set-up-env-vars.outputs.malou_api_key_env] }}
                  CONFIGURATION_BASE_URL: ${{ needs.fetch-api.outputs.base_url }}
                  GROUP_NAME: ${{ needs.fetch-api.outputs.group_name }}
                  ENVIRONMENT: ${{ inputs.environment }}

            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v4
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: eu-west-3

            - name: Deploy to S3
              working-directory: apps/store-locator
              run: |
                  # Sync fingerprinted assets with long-term cache
                  aws s3 sync ./dist ${{ needs.fetch-api.outputs.s3_bucket_name }} \
                    --follow-symlinks \
                    --cache-control "public, max-age=31536000, immutable" \
                    --exclude "*" \
                    --include "_astro/*" \
                    --include "chunks/*" \
                    --delete

                  # Sync the rest with no cache
                  aws s3 sync ./dist ${{ needs.fetch-api.outputs.s3_bucket_name }} \
                    --follow-symlinks \
                    --cache-control "public, no-cache, must-revalidate" \
                    --exclude "_astro/*" \
                    --exclude "chunks/*" \
                    --delete

            - name: Create AWS Cloudfront invalidation
              run: |
                  aws cloudfront create-invalidation --distribution-id ${{ needs.fetch-api.outputs.cloudfront_distribution_id }} --paths "/*"

            - name: Clean unused cached images
              working-directory: apps/store-locator
              run: pnpm run clean-cache

            # actions/cache@v4 won't save the cache afterwards if there's a cache hit
            # But we want to save it no matter what, so we have to do it manually, by using restore then save
            - name: Astro cache save
              uses: actions/cache/save@v4
              with:
                  path: apps/store-locator/node_modules/.astro
                  key: ${{ runner.os }}-astro-${{ inputs.configurationId }}-${{ inputs.environment }}

    update-job-status:
        name: Update job status
        runs-on: ubuntu-latest
        needs: [set-up-env-vars, deploy]
        if: ${{ always() && inputs.jobId != '' }}
        steps:
            - name: Update job status
              run: |
                  curl -X POST "${{ needs.set-up-env-vars.outputs.api_base_url }}/store-locator/deployment/update-status?api_key=${{ secrets[needs.set-up-env-vars.outputs.malou_api_key_env] }}" \
                    -H "Content-Type: application/json" \
                    -d '{"jobId": "${{ inputs.jobId }}", "status": "${{ needs.deploy.result }}", "workflowRunId": "${{ github.run_id }}"}'

    slack-notification:
        needs: [fetch-api, deploy]
        if: ${{ always() && (needs.deploy.result == 'success' || needs.deploy.result == 'failure') }}
        name: Send Slack notification
        runs-on: ubuntu-latest
        steps:
            - name: Set Slack channel ID
              id: get-slack-channel-id
              run: |
                  if [ "${{ inputs.environment }}" = "production" ]; then
                    echo "SLACK_CHANNEL_ID=${{ secrets.STORE_LOCATOR_SLACK_CHANNEL_ID_PROD }}" >> $GITHUB_OUTPUT
                  else
                    echo "SLACK_CHANNEL_ID=${{ secrets.STORE_LOCATOR_SLACK_CHANNEL_ID }}" >> $GITHUB_OUTPUT
                  fi

            - name: Send notification
              uses: slackapi/slack-github-action@v2.0.0
              with:
                  method: chat.postMessage
                  token: ${{ secrets.SLACK_BOT_TOKEN }}
                  payload: |
                      channel: ${{ steps.get-slack-channel-id.outputs.SLACK_CHANNEL_ID }}
                      attachments:
                        - color: ${{ needs.deploy.result == 'success' && '"#2eb886"' || (needs.deploy.result == 'failure' && '"#e01e5a"' || '"#cccccc"') }}
                          blocks:
                            - type: section
                              text:
                                type: mrkdwn
                                text: "*🚀 Deploy status: ${{ needs.deploy.result == 'success' && '✅ Success' || needs.deploy.result == 'failure' && '❌ Failure' || needs.deploy.result == 'cancelled' && '⚪ Cancelled' || 'Unknown' }}*"

                            - type: context
                              elements:
                                - type: mrkdwn
                                  text: "Triggered for: Configuration `${{ needs.fetch-api.outputs.group_name }}` | ID `${{ inputs.configurationId }}` | Environment: `${{ inputs.environment }}` | Branch: `${{ github.ref_name }}`"

                                - type: mrkdwn
                                  text: "<${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|View Workflow Run>"
