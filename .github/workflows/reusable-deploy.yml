name: Deploy (reusable workflow)

on:
    workflow_call:
        inputs:
            environment:
                required: true
                type: string
            apps:
                required: true
                type: string
            aws-ecs-cluster-name:
                required: true
                type: string
            aws-ecs-service-name:
                required: true
                type: string
            aws-s3-bucket-name:
                required: true
                type: string
            aws-cloudfront-distribution-id:
                required: true
                type: string

env:
    PNPM_STORE_PATH: ~/.pnpm-store
    PQINA_NPM_TOKEN: ${{ secrets.PQINA_NPM_TOKEN }}

jobs:
    build-api-base-image:
        if: ${{ inputs.apps == 'all' || inputs.apps == 'api' }}
        name: Build image
        runs-on: runs-on=${{ github.run_id }}/runner=16cpu-custom/extras=s3-cache
        steps:
            # Necessary to fetch runsOn S3 cache
            - uses: runs-on/action@v1

            - uses: actions/checkout@v4

            - name: Build and push image to ECR
              id: build-api-base-image
              uses: ./.github/actions/build-docker-image-and-push-to-ecr
              with:
                  sentry-auth-token: ${{ secrets.SENTRY_AUTH_TOKEN }}
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: eu-west-3
                  ecr-repository: app-malou-api
                  image-tag: ${{ inputs.environment }}
                  environment: ${{ inputs.environment }}

    deploy-api:
        if: ${{ inputs.apps == 'all' || inputs.apps == 'api' }}
        name: Deploy API
        needs: [build-api-base-image]
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@v4

            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v4
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: eu-west-3

            - name: Deploy Amazon ECS api task definition
              uses: aws-actions/amazon-ecs-deploy-task-definition@v2
              with:
                  task-definition: ./devops/app-malou-backend-${{ inputs.environment }}-task-definition.json
                  service: app-malou-backend-service-${{ inputs.aws-ecs-service-name }}
                  cluster: ${{ inputs.aws-ecs-cluster-name }}
                  wait-for-service-stability: true

            - name: Create Sentry api release
              if: ${{ inputs.environment == 'production' }}
              uses: ./.github/actions/create-api-sentry-release
              with:
                  sentry-auth-token: ${{ secrets.SENTRY_AUTH_TOKEN }}
                  sentry-project: malouapp-api
                  environment: ${{ inputs.environment }}

    deploy-worker:
        if: ${{ inputs.apps == 'all' || inputs.apps == 'api' }}
        name: Deploy Worker
        needs: [build-api-base-image]
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@v4

            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v4
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: eu-west-3

            - name: Deploy Amazon ECS worker task definition
              uses: aws-actions/amazon-ecs-deploy-task-definition@v2
              with:
                  task-definition: ./devops/app-malou-backend-${{ inputs.environment }}-worker-task-definition.json
                  service: app-malou-backend-worker-service-${{ inputs.aws-ecs-service-name }}
                  cluster: ${{ inputs.aws-ecs-cluster-name }}
                  wait-for-service-stability: true

    setup-web-runner:
        if: ${{ inputs.apps == 'all' || inputs.apps == 'web' }}
        name: Setup WEB runner
        outputs:
            web-runner: ${{ steps.setup-web-runner.outputs.WEB_RUNNER }}
        runs-on: ubuntu-latest
        steps:
            - name: Setup web runner
              id: setup-web-runner
              run: |
                  if [[ ${{ inputs.apps }} == 'web' ]]; then
                    echo "WEB_RUNNER=runs-on=${{ github.run_id }}/runner=16cpu-custom/extras=s3-cache" >> $GITHUB_OUTPUT
                  else
                    echo "WEB_RUNNER=ubuntu-latest" >> $GITHUB_OUTPUT
                  fi

    build-web:
        if: ${{ inputs.apps == 'all' || inputs.apps == 'web' }}
        name: Build WEB app
        needs: [setup-web-runner]
        runs-on: ${{ needs.setup-web-runner.outputs.web-runner }}
        steps:
            # Necessary to fetch runsOn S3 cache
            - uses: runs-on/action@v1

            - name: Checkout
              uses: actions/checkout@v4

            - name: Setup Node and PNPM
              uses: ./.github/actions/setup-node-and-pnpm

            - name: Change version name in sentry config
              if: ${{ inputs.environment == 'production' }}
              env:
                  SENTRY_RELEASE: malouapp-web-production@${{ github.sha }}
              run: |
                  sed -i 's/malouapp-v3@latest/${{ env.SENTRY_RELEASE }}/' apps/app-malou-web/src/sentry.ts

            - name: Turbo caching
              uses: actions/cache/restore@v4
              with:
                  path: .turbo
                  key: ${{ runner.os }}-turbo-${{ github.ref_name }}
                  restore-keys: |
                      ${{ runner.os }}-turbo-main
                      ${{ runner.os }}-turbo-

            - name: Install
              env:
                  PQINA_NPM_TOKEN: ${{ secrets.PQINA_NPM_TOKEN }}
              run: pnpm install --filter @malou-io/app-web...

            - name: Build
              run: pnpm run build-${{ inputs.environment }} --filter=@malou-io/app-web --cache-dir=.turbo

            - name: Upload Sentry sourcemaps
              if: ${{ inputs.environment == 'production' }}
              env:
                  SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
                  SENTRY_ORG: malou
                  SENTRY_PROJECT: malouapp-web
                  SENTRY_RELEASE: malouapp-web-production@${{ github.sha }}
              run: |
                  SENTRY_AUTH_TOKEN=${{ env.SENTRY_AUTH_TOKEN }} SENTRY_ORG=${{ env.SENTRY_ORG }} SENTRY_PROJECT=${{ env.SENTRY_PROJECT }} SENTRY_RELEASE=${{ env.SENTRY_RELEASE }} pnpm run --filter=@malou-io/app-web... sentry:sourcemaps
                  rm -rf apps/app-malou-web/dist/**/*.js.map apps/app-malou-web/dist/**/*.css.map

            - name: Regenerate ngsw.json
              # We need to regenerate the hash of main-<id>.js contained in ngsw.json because 'sentry-cli sourcemaps inject' command modify it
              if: ${{ inputs.environment == 'production' }}
              run: |
                  cd apps/app-malou-web
                  node_modules/.bin/ngsw-config dist ngsw-config.json

            - name: Upload dist folder as artifact
              uses: actions/upload-artifact@v4
              with:
                  name: dist-${{ github.sha }}-${{ inputs.environment }}
                  path: apps/app-malou-web/dist

    deploy-web:
        needs: [build-web, deploy-api]
        if: ${{ always() && (inputs.apps == 'all' || inputs.apps == 'web') && needs.build-web.result == 'success' && (needs.deploy-api.result == 'success' || needs.deploy-api.result == 'skipped') }}
        runs-on: runs-on=${{ github.run_id }}/runner=4cpu-custom
        name: Deploy WEB app
        steps:
            # Necessary to fetch runsOn S3 cache
            - uses: runs-on/action@v1

            - name: Checkout
              uses: actions/checkout@v4

            - name: Download dist folder artifact
              uses: actions/download-artifact@v4
              with:
                  name: dist-${{ github.sha }}-${{ inputs.environment }}
                  path: apps/app-malou-web/dist

            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v4
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: eu-west-3

            - name: Deploy to S3
              run: |
                  aws s3 sync ./apps/app-malou-web/dist s3://${{ inputs.aws-s3-bucket-name }} --acl public-read --follow-symlinks --delete

            - name: Create AWS Cloudfront invalidation
              run: |
                  aws cloudfront create-invalidation --distribution-id ${{ inputs.aws-cloudfront-distribution-id }} --paths "/*"

            - name: Create Sentry web release
              if: ${{ inputs.environment == 'production' }}
              uses: ./.github/actions/create-api-sentry-release
              with:
                  sentry-auth-token: ${{ secrets.SENTRY_AUTH_TOKEN }}
                  sentry-project: malouapp-web
                  environment: ${{ inputs.environment }}

    deploy-aws-services:
        needs: [build-api-base-image]
        if: ${{ always() && (inputs.apps == 'all' || inputs.apps == 'aws-services') && (needs.build-api-base-image.result == 'success' || needs.build-api-base-image.result == 'skipped') }}
        runs-on: runs-on=${{ github.run_id }}/runner=16cpu-custom/extras=s3-cache

        name: Deploy AWS Services
        steps:
            # Necessary to fetch runsOn S3 cache
            - uses: runs-on/action@v1

            - name: Checkout repository
              uses: actions/checkout@v4

            - name: Setup Node and PNPM
              uses: ./.github/actions/setup-node-and-pnpm

            - name: Install dependencies
              run: pnpm install

            - name: Install Docker
              uses: docker/setup-buildx-action@v3

            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v4
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: eu-west-3

            - name: Set up env variables
              working-directory: services/aws
              run: |
                  echo "Creating .env.${{ inputs.environment }} file"

                  # Conditionally select the secret based on the environment
                  if [[ "${{ inputs.environment }}" == "development" ]]; then
                    echo "Using development environment variables"
                    ENV_VARIABLES="${{ secrets.AWS_SERVICES_ENV_VARIABLES_DEVELOPMENT }}"
                  elif [[ "${{ inputs.environment }}" == "staging" ]]; then
                    echo "Using staging environment variables"
                    ENV_VARIABLES="${{ secrets.AWS_SERVICES_ENV_VARIABLES_STAGING }}"
                  elif [[ "${{ inputs.environment }}" == "production" ]]; then
                    echo "Using production environment variables"
                    ENV_VARIABLES="${{ secrets.AWS_SERVICES_ENV_VARIABLES_PRODUCTION }}"
                  else
                    echo "Invalid environment specified"
                    exit 1
                  fi

                  # Write the environment variables into the .env file
                  printf "%s" "$ENV_VARIABLES" > .env.${{ inputs.environment }}

            - name: Deploy AWS services
              working-directory: services/aws
              env:
                  NODE_ENV: ${{ inputs.environment }}
              run: pnpm run deploy --all --require-approval never
