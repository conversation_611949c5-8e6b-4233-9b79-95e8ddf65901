#!/bin/bash

mongo malou --eval "db.dropDatabase()"
mongoimport --host mongodb --db malou --collection organizations --type json --file /mongo-seed/organizations.json --jsonArray --drop
mongoimport --host mongodb --db malou --collection users --type json --file /mongo-seed/users.json --jsonArray --drop
mongoimport --host mongodb --db malou --collection calendarevents --type json --file /mongo-seed/calendarevents.json --jsonArray --drop
mongoimport --host mongodb --db malou --collection attributes --type json --file /mongo-seed/attributes.json --jsonArray --drop
mongoimport --host mongodb --db malou --collection bricks --type json --file /mongo-seed/bricks.json --jsonArray --drop
mongoimport --host mongodb --db malou --collection media --type json --file /mongo-seed/media.json --jsonArray --drop
mongoimport --host mongodb --db malou --collection brickgenerators --type json --file /mongo-seed/brickgenerators.json --jsonArray --drop
mongoimport --host mongodb --db malou --collection hashtagcategories --type json --file /mongo-seed/hashtagcategories.json --jsonArray --drop
