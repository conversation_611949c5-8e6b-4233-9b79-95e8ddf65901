import { KeyValuePipe, LowerCasePipe, NgTemplateOutlet } from '@angular/common';
import { Component, computed, effect, inject, input, OnInit, output, signal } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { groupBy } from 'lodash';

import { isNotNil, PartialRecord, ReviewAnalysisSentiment, ReviewAnalysisTag } from '@malou-io/package-utils';

import { AggregatedStatisticsFiltersContext } from ':modules/aggregated-statistics/filters/filters.context';
import { NavBarTab, TabNavBarComponent } from ':shared/components/tab-nav-bar/tab-nav-bar.component';
import { ChartSortBy } from ':shared/enums/sort.enum';
import { ViewBy } from ':shared/enums/view-by.enum';
import { ChartDataArray } from ':shared/helpers';
import { Restaurant, ReviewWithAnalysis } from ':shared/models';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { ApplyPurePipe } from ':shared/pipes/apply-fn.pipe';
import { EnumTranslatePipe } from ':shared/pipes/enum-translate.pipe';
import { IllustrationPathResolverPipe } from ':shared/pipes/illustration-path-resolver.pipe';

import { SelectComponent } from '../../select/select.component';
import { TagEvolutionData, TagsEvolutionChartComponent } from './tags-evolution-chart/tags-evolution-chart.component';

const analysisTagsSorted: ReviewAnalysisTag[] = [
    ReviewAnalysisTag.FOOD,
    ReviewAnalysisTag.SERVICE,
    ReviewAnalysisTag.ATMOSPHERE,
    ReviewAnalysisTag.PRICE,
    ReviewAnalysisTag.EXPEDITIOUSNESS,
    ReviewAnalysisTag.HYGIENE,
];

interface SegmentTagEvolution {
    sentiment: ReviewAnalysisSentiment;
    date: Date;
    tag: ReviewAnalysisTag;
}

@Component({
    selector: 'app-tags-evolution',
    templateUrl: './tags-evolution.component.html',
    styleUrls: ['./tags-evolution.component.scss'],
    imports: [
        NgTemplateOutlet,
        MatTooltipModule,
        MatIconModule,
        TabNavBarComponent,
        TagsEvolutionChartComponent,
        IllustrationPathResolverPipe,
        TranslateModule,
        SelectComponent,
        FormsModule,
        ReactiveFormsModule,
        ApplyPurePipe,
        LowerCasePipe,
        KeyValuePipe,
    ],
    providers: [EnumTranslatePipe],
})
export class TagsEvolutionComponent implements OnInit {
    readonly reviewsByRestaurantId = input.required<Record<string, ReviewWithAnalysis[]>>();
    readonly shouldDetailTagsEvolutionCharts = input.required<boolean>();
    readonly warningTooltip = input<string>();
    readonly sortBy = input<ChartSortBy>();
    readonly sortByChange = output<ChartSortBy>();

    private readonly _translate = inject(TranslateService);
    private readonly _enumTranslate = inject(EnumTranslatePipe);
    private readonly _aggregatedStatisticsFiltersContext = inject(AggregatedStatisticsFiltersContext);

    readonly SvgIcon = SvgIcon;
    readonly CHART_SORT_BY_VALUES = Object.values(ChartSortBy);

    readonly selectedRestaurants = toSignal(this._aggregatedStatisticsFiltersContext.selectedRestaurants$, { initialValue: [] });

    readonly cleanedReviewsByRestaurantId = computed(() => this._cleanReviewByRestaurantsId(this.reviewsByRestaurantId()));
    readonly restaurantsIds = computed(() => Object.keys(this.cleanedReviewsByRestaurantId()));
    readonly labels = computed(() => this._computeLabels(this.restaurantsIds(), this.selectedRestaurants()));
    readonly segmentsByTagByRestaurantId = computed<Record<string, Record<string, SegmentTagEvolution[]>>>(() =>
        this._computeSegmentsByTagByRestaurantId(this.cleanedReviewsByRestaurantId())
    );
    readonly tagsWithCount = computed(() => this._computeTagsWithCount(this.segmentsByTagByRestaurantId()));
    readonly allTags = computed(() => Object.keys(this.tagsWithCount()));
    readonly tabs = computed(() => this._computeTabs(this.tagsWithCount()));
    readonly hasData = computed(() => this.tabs().length > 0);

    readonly currentTag = signal<string>('');
    readonly tagEvolutionDataByTag = computed<Record<string, TagEvolutionData>>(() => this._computeTagEvolutionDataByTag(this.allTags()));
    readonly tagEvolutionDataForCurrentTag = computed<TagEvolutionData>(() => {
        const currentTag = this.currentTag();
        const tagEvolutionData = this.tagEvolutionDataByTag();

        if (currentTag && tagEvolutionData[currentTag]) {
            return tagEvolutionData[currentTag];
        }
        return {
            [ReviewAnalysisSentiment.POSITIVE]: [],
            [ReviewAnalysisSentiment.NEGATIVE]: [],
        };
    });

    readonly sortByControl: FormControl<ChartSortBy> = new FormControl<ChartSortBy>(ChartSortBy.ALPHABETICAL) as FormControl<ChartSortBy>;

    constructor() {
        effect(() => {
            const tabsData = this.tabs()[0]?.data || null;
            this.currentTag.set(tabsData);
        });
    }

    ngOnInit(): void {
        if (this.sortBy()) {
            this.sortByControl.setValue(this.sortBy()!);
        }
    }

    onTabChange(tab: NavBarTab): void {
        this.currentTag.set(tab.data);
    }

    viewByDisplayWith = (option: ViewBy): string => this._enumTranslate.transform(option, 'view_by');

    sortByDisplayWith = (option: ChartSortBy): string => this._enumTranslate.transform(option, 'chart_sort_by');

    getChartTitleByKey = (key: string): string => this._translate.instant(`reviews.review_analyses.${key}`);

    onSortByChange(sortBy: ChartSortBy): void {
        this.sortByControl.setValue(sortBy);
        this.sortByChange.emit(sortBy);
    }

    private _cleanReviewByRestaurantsId(reviewsByRestaurantId: Record<string, ReviewWithAnalysis[]>): Record<string, ReviewWithAnalysis[]> {
        return Object.entries(reviewsByRestaurantId).reduce(
            (acc, [restaurantId, review]) => ({
                ...acc,
                [restaurantId]: this._getReviewWithAnalysisWithPositiveAndNegativeSentiments(review),
            }),
            {}
        );
    }

    private _computeLabels(restaurantsIds: string[], restaurants: Restaurant[]): string[] {
        return restaurantsIds
            .map((id) => restaurants.find((r) => r._id === id)?.internalName)
            .filter(isNotNil)
            .sort((a, b) => a.localeCompare(b));
    }

    private _computeSegmentsByTagByRestaurantId(
        reviewsByRestaurantId: Record<string, ReviewWithAnalysis[]>
    ): Record<string, Record<string, SegmentTagEvolution[]>> {
        return Object.entries(reviewsByRestaurantId).reduce(
            (acc, [restaurantId, reviews]) => ({
                ...acc,
                [restaurantId]: this._getSegmentsByTag(reviews),
            }),
            {}
        );
    }

    private _getSegmentsByTag(reviews: ReviewWithAnalysis[]): Record<string, SegmentTagEvolution[]> {
        const segmentsWithReview = reviews.flatMap((review) => {
            const oldSegments = review.semanticAnalysis?.segmentAnalyses ?? [];
            const newSegments = review.semanticAnalysisSegments ?? [];
            return [
                ...oldSegments.map((segment) => ({
                    sentiment: segment.sentiment,
                    date: new Date(review.socialCreatedAt),
                    tag: segment.tag,
                })),
                ...newSegments.map((segment) => ({
                    sentiment: segment.sentiment,
                    date: new Date(review.socialCreatedAt),
                    tag: segment.category,
                })),
            ];
        });
        return groupBy(segmentsWithReview, 'tag');
    }

    private _getReviewWithAnalysisWithPositiveAndNegativeSentiments(reviews: ReviewWithAnalysis[]): ReviewWithAnalysis[] {
        return reviews.map((review) => ({
            ...review,
            semanticAnalysis: {
                ...review.semanticAnalysis,
                segmentAnalyses: review?.semanticAnalysis?.segmentAnalyses?.filter((segment) =>
                    [ReviewAnalysisSentiment.POSITIVE, ReviewAnalysisSentiment.NEGATIVE].includes(segment.sentiment)
                ),
            },
            semanticAnalysisSegments: review?.semanticAnalysisSegments?.filter((segment) =>
                [ReviewAnalysisSentiment.POSITIVE, ReviewAnalysisSentiment.NEGATIVE].includes(segment.sentiment)
            ),
        })) as ReviewWithAnalysis[];
    }

    private _computeTagsWithCount(
        segmentsByTagByRestaurantId: Record<string, Record<string, SegmentTagEvolution[]>>
    ): PartialRecord<ReviewAnalysisTag, number> {
        const tagsCount: PartialRecord<ReviewAnalysisTag, number> = {};
        for (const segmentsByTag of Object.values(segmentsByTagByRestaurantId)) {
            for (const [tag, segments] of Object.entries(segmentsByTag)) {
                tagsCount[tag] = (tagsCount[tag] ?? 0) + segments.length;
            }
        }
        return tagsCount;
    }

    private _computeTabs(tagsWithCount: PartialRecord<ReviewAnalysisTag, number>): NavBarTab[] {
        const tabs = Object.entries(tagsWithCount).map(([key, value]: [ReviewAnalysisTag, number]) => ({
            title: this._translate.instant(`reviews.review_analyses.${key}`),
            subtitle: `(${value})`,
            data: key,
        }));
        return tabs.sort((a, b) => analysisTagsSorted.indexOf(a.data) - analysisTagsSorted.indexOf(b.data));
    }

    private _computeTagEvolutionDataByTag(tags: string[]): Record<string, TagEvolutionData> {
        const tagEvolutionDataByTag: Record<string, TagEvolutionData> = {};

        for (const tag of tags) {
            const chartDataNegative: ChartDataArray = [];
            const chartDataPositive: ChartDataArray = [];
            for (const restaurantId of this.restaurantsIds()) {
                const segments = this.segmentsByTagByRestaurantId()[restaurantId][tag] ?? [];
                const negativeSentimentValue = segments.filter((segment) => segment.sentiment === ReviewAnalysisSentiment.NEGATIVE).length;
                const positiveSentimentValue = segments.filter((segment) => segment.sentiment === ReviewAnalysisSentiment.POSITIVE).length;

                chartDataNegative.push(negativeSentimentValue);
                chartDataPositive.push(positiveSentimentValue);
            }
            tagEvolutionDataByTag[tag] = {
                [ReviewAnalysisSentiment.NEGATIVE]: chartDataNegative,
                [ReviewAnalysisSentiment.POSITIVE]: chartDataPositive,
            };
        }
        return tagEvolutionDataByTag;
    }
}
