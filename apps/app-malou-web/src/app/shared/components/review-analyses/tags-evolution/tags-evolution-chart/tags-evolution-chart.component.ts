import { Component, computed, inject, input } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { ChartDataset, ChartOptions, ChartType } from 'chart.js';
import { Tick } from 'chart.js/dist/types';
import { NgChartsModule } from 'ng2-charts';

import { ReviewAnalysisSentiment } from '@malou-io/package-utils';

import { ChartSortBy } from ':shared/enums/sort.enum';
import { ChartDataArray, ChartDataElement, malouChartColorGreen, malouChartColorRed } from ':shared/helpers';
import { ShortNumberPipe } from ':shared/pipes/short-number.pipe';
import { ShortTextPipe } from ':shared/pipes/short-text.pipe';

const RESTAURANT_NAME_MAX_LENGTH = 20;

type BarChartType = Extract<ChartType, 'bar'>;

export interface TagEvolutionData {
    [ReviewAnalysisSentiment.POSITIVE]: ChartDataArray;
    [ReviewAnalysisSentiment.NEGATIVE]: ChartDataArray;
}

@Component({
    selector: 'app-tags-evolution-chart',
    templateUrl: './tags-evolution-chart.component.html',
    styleUrls: ['./tags-evolution-chart.component.scss'],
    imports: [NgChartsModule],
    providers: [ShortNumberPipe, ShortTextPipe],
})
export class TagsEvolutionChartComponent {
    readonly tagEvolutionData = input.required<TagEvolutionData>();
    readonly sortBy = input.required<ChartSortBy>();
    readonly labels = input<string[]>([]);

    private readonly _shortNumberPipe = inject(ShortNumberPipe);
    private readonly _translateService = inject(TranslateService);
    private readonly _shortTextPipe = inject(ShortTextPipe);

    readonly CHART_TYPE: BarChartType = 'bar';

    readonly chartDataSets = computed(() => this._computeChartData(this.sortedData().evolutionData));
    readonly chartLabels = computed(() => this._computeChartLabels(this.sortedData().labels));
    readonly chartOptions = computed(() => this._computeChartOptions(this.sortedData().labels));

    readonly sortedData = computed(() => {
        const { evolutionData, labels } = this._computeSortedData(this.sortBy(), this.tagEvolutionData(), this.labels());
        return { evolutionData, labels };
    });

    private _computeChartData(tagEvolutionData: TagEvolutionData): ChartDataset<BarChartType, ChartDataArray>[] {
        return [
            {
                label: this._translateService.instant('aggregated_statistics.e_reputation.reviews_analysis.negative_sentiments'),
                borderColor: malouChartColorRed,
                backgroundColor: malouChartColorRed,
                xAxisID: 'xAxis',
                yAxisID: 'yAxis',
                data: tagEvolutionData[ReviewAnalysisSentiment.NEGATIVE],
            },
            {
                label: this._translateService.instant('aggregated_statistics.e_reputation.reviews_analysis.positive_sentiments'),
                borderColor: malouChartColorGreen,
                backgroundColor: malouChartColorGreen,
                xAxisID: 'xAxis',
                yAxisID: 'yAxis',
                data: tagEvolutionData[ReviewAnalysisSentiment.POSITIVE],
            },
        ];
    }

    private _computeSortedData(
        sortBy: ChartSortBy,
        tagEvolutionData: TagEvolutionData,
        labels: string[]
    ): { evolutionData: TagEvolutionData; labels: string[] } {
        const data = (labels ?? []).map((label, index) => ({
            label,
            positive: tagEvolutionData[ReviewAnalysisSentiment.POSITIVE][index],
            negative: tagEvolutionData[ReviewAnalysisSentiment.NEGATIVE][index],
            total:
                (tagEvolutionData[ReviewAnalysisSentiment.POSITIVE][index] ?? 0) +
                (tagEvolutionData[ReviewAnalysisSentiment.NEGATIVE][index] ?? 0),
        }));
        switch (sortBy) {
            case ChartSortBy.ALPHABETICAL:
                const sortedData = data.sort((a, b) => a.label.localeCompare(b.label));
                return this._reduceSortedData(sortedData);
            case ChartSortBy.ASC:
                const sortedDataAsc = data.sort((a, b) => a.total - b.total);
                return this._reduceSortedData(sortedDataAsc);
            case ChartSortBy.DESC:
                const sortedDataDesc = data.sort((a, b) => b.total - a.total);
                return this._reduceSortedData(sortedDataDesc);
            default:
                return { evolutionData: tagEvolutionData, labels: labels };
        }
    }

    private _reduceSortedData(data: { label: string; positive: ChartDataElement; negative: ChartDataElement; total: number }[]): {
        evolutionData: TagEvolutionData;
        labels: string[];
    } {
        return data.reduce(
            (acc, { label, positive, negative }) => ({
                evolutionData: {
                    [ReviewAnalysisSentiment.POSITIVE]: [...acc.evolutionData[ReviewAnalysisSentiment.POSITIVE], positive],
                    [ReviewAnalysisSentiment.NEGATIVE]: [...acc.evolutionData[ReviewAnalysisSentiment.NEGATIVE], negative],
                },
                labels: [...acc.labels, label],
            }),
            { evolutionData: { [ReviewAnalysisSentiment.POSITIVE]: [], [ReviewAnalysisSentiment.NEGATIVE]: [] }, labels: [] }
        );
    }

    private _computeChartLabels(labels: string[]): string[] {
        return labels;
    }

    private _computeChartOptions(labels: string[]): ChartOptions<BarChartType> {
        return {
            plugins: {
                legend: {
                    display: false,
                },
            },
            scales: {
                xAxis: {
                    axis: 'x',
                    type: 'category',
                    stacked: true,
                    ticks: {
                        callback: (tickValue: number, index: number, _ticks: Tick[]): string => {
                            const label = labels[index];
                            return this._shortTextPipe.transform(label, RESTAURANT_NAME_MAX_LENGTH);
                        },
                    },
                },
                yAxis: {
                    axis: 'y',
                    type: 'linear',
                    stacked: true,
                    ticks: {
                        callback: (tickValue: number | string, _index: number, _ticks: Tick[]) =>
                            this._shortNumberPipe.transform(tickValue as number),
                    },
                },
            },
        };
    }
}
