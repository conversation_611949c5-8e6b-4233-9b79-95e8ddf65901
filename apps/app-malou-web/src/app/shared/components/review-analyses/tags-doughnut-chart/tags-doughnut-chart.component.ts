import { Component, inject, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Chart, ChartDataset, ChartOptions, ChartType, Plugin, TooltipItem } from 'chart.js';
import { EmptyObject } from 'chart.js/dist/types/basic';
import { NgChartsModule } from 'ng2-charts';

import { getPositiveAndNegativeStatsForSemanticAnalysis, getSegmentsForSemanticAnalysisStats } from '@malou-io/package-utils';

import { ExperimentationService } from ':core/services/experimentation.service';
import { ChartDataArray, malouChartColorGreen, malouChartColorRed, malouChartColorText1, malouChartColorText2 } from ':shared/helpers';
import { hasSimpleChangesAtLeastOneProperty } from ':shared/helpers/on-changes';
import { ReviewWithAnalysis } from ':shared/models';

type DoughnutChartType = Extract<ChartType, 'doughnut'>;

const colors = [malouChartColorGreen, malouChartColorRed];

enum Quarter {
    TOP_RIGHT,
    TOP_LEFT,
    BOTTOM_RIGHT,
    BOTTOM_LEFT,
}

enum ChartDataIndex {
    POSITIVE,
    NEGATIVE,
}

interface CustomChartLabel {
    value: number;
    percentageValue: number;
    subText: string[];
}

interface SegmentAnalyse {
    sentiment: string;
}

@Component({
    selector: 'app-tags-doughnut-chart',
    templateUrl: './tags-doughnut-chart.component.html',
    styleUrls: ['./tags-doughnut-chart.component.scss'],
    imports: [NgChartsModule],
})
export class TagsDoughnutChartComponent implements OnInit, OnChanges {
    @Input() reviewsByRestaurantId: Record<string, ReviewWithAnalysis[]>;

    private readonly _translateService = inject(TranslateService);
    private readonly _experimentationService = inject(ExperimentationService);

    readonly CHART_TYPE: DoughnutChartType = 'doughnut';
    chartDataSets: ChartDataset<DoughnutChartType, ChartDataArray>[];
    chartLabels: CustomChartLabel[];
    chartOption: ChartOptions<DoughnutChartType>;
    readonly CENTER_TEXT_PLUGIN: Plugin = this._getCenterTextPlugin();
    readonly DOUGHNUT_LABEL_LINE: Plugin = this._getDoughnutLabelLinePlugin();

    allSegmentAnalyses: SegmentAnalyse[];

    ngOnInit(): void {
        this.chartOption = this._computeChartOptions();
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (hasSimpleChangesAtLeastOneProperty(changes, 'reviewsByRestaurantId')) {
            const reviews: ReviewWithAnalysis[] = Object.values(this.reviewsByRestaurantId).flat();
            const isSemanticAnalysisFeatureEnabled =
                this._experimentationService.isFeatureEnabledForRestaurant('release-new-semantic-analysis');
            this.allSegmentAnalyses = getSegmentsForSemanticAnalysisStats(reviews, isSemanticAnalysisFeatureEnabled);

            this.chartDataSets = this._computeChartData();
            this.chartLabels = this._computeChartLabels();
        }
    }

    private _computeChartData(): ChartDataset<DoughnutChartType, ChartDataArray>[] {
        const { positiveSentimentsPercentage, negativeSentimentsPercentage } = getPositiveAndNegativeStatsForSemanticAnalysis(
            this.allSegmentAnalyses
        );

        return [
            {
                backgroundColor: colors,
                borderColor: colors,
                data: [positiveSentimentsPercentage, negativeSentimentsPercentage],
                borderWidth: 0,
            },
        ];
    }

    private _computeChartLabels(): CustomChartLabel[] {
        const { positiveSentimentsCount, positiveSentimentsPercentage, negativeSentimentsCount, negativeSentimentsPercentage } =
            getPositiveAndNegativeStatsForSemanticAnalysis(this.allSegmentAnalyses);

        return [
            {
                value: positiveSentimentsCount,
                percentageValue: positiveSentimentsPercentage,
                subText: [
                    this._translateService.instant('aggregated_statistics.e_reputation.reviews_analysis.feelings'),
                    this._translateService.instant('aggregated_statistics.e_reputation.reviews_analysis.positive'),
                ],
            },
            {
                value: negativeSentimentsCount,
                percentageValue: negativeSentimentsPercentage,
                subText: [
                    this._translateService.instant('aggregated_statistics.e_reputation.reviews_analysis.feelings'),
                    this._translateService.instant('aggregated_statistics.e_reputation.reviews_analysis.negative'),
                ],
            },
        ];
    }

    private _computeChartOptions(): ChartOptions<DoughnutChartType> {
        return {
            plugins: {
                tooltip: {
                    callbacks: {
                        title: () => '',
                        label: (tooltipItem: TooltipItem<any>): string => {
                            const labels = tooltipItem.chart.data.labels as CustomChartLabel[];
                            const { value } = labels[tooltipItem.dataIndex];
                            if (tooltipItem.dataIndex === ChartDataIndex.POSITIVE) {
                                return ` ${this._translateService.instant(
                                    'statistics.e_reputation.reviews_analysis.positive_sentiments'
                                )}: ${value}`;
                            }
                            return ` ${this._translateService.instant('statistics.e_reputation.reviews_analysis.negative_sentiments')}: ${value}`;
                        },
                    },
                },
                legend: {
                    display: false,
                },
            },
            cutout: '80%',
            layout: {
                padding: {
                    top: 40,
                    bottom: 50,
                    left: 150,
                    right: 150,
                },
            },
        };
    }

    private _getCenterTextPlugin(): Plugin {
        return {
            id: 'centerText',
            afterDraw: (chart: Chart, _args: EmptyObject): void => {
                const { ctx } = chart;
                ctx.save();
                const x = chart.getDatasetMeta(0).data[0].x;
                const y = chart.getDatasetMeta(0).data[0].y;

                ctx.font = '600 16px Poppins';
                ctx.fillStyle = malouChartColorText2;
                const textWidth = ctx.measureText(
                    `${this.allSegmentAnalyses.length} ${this._translateService.instant(
                        'aggregated_statistics.e_reputation.reviews_analysis.feelings'
                    )}`
                );
                ctx.fillText(
                    `${this.allSegmentAnalyses.length} ${this._translateService.instant(
                        'aggregated_statistics.e_reputation.reviews_analysis.feelings'
                    )}`,
                    x - textWidth.width / 2,
                    y + 10
                );
                ctx.restore();
            },
        };
    }

    private _getDoughnutLabelLinePlugin(): Plugin {
        return {
            id: 'doughnutLabelLine',
            afterDraw: (chart: Chart, _args: EmptyObject): void => {
                const { ctx } = chart;
                const centerX = chart.getDatasetMeta(0).data[0].x;
                const centerY = chart.getDatasetMeta(0).data[0].y;
                chart.data.datasets.forEach((dataset, i) => {
                    chart.getDatasetMeta(i).data.forEach((dataPoint, index) => {
                        ctx.save();
                        const { x, y } = dataPoint.tooltipPosition(true);
                        if (dataset.borderColor) {
                            ctx.strokeStyle = dataset.borderColor[index];
                        }

                        const quarter = this._getQuarter(centerX, centerY, x, y);
                        ctx.beginPath();
                        ctx.moveTo(x, y);

                        const gap = 30;
                        const gap_2x = 50;
                        let currentXPos;
                        let currentYPos;

                        switch (quarter) {
                            case Quarter.TOP_RIGHT:
                                currentXPos = x + gap_2x;
                                currentYPos = y + gap;
                                ctx.lineTo(x + gap, y + gap);
                                ctx.lineTo(x + gap_2x, y + gap);
                                break;
                            case Quarter.BOTTOM_RIGHT:
                                currentXPos = x + gap_2x;
                                currentYPos = y - gap;
                                ctx.lineTo(x + gap, y - gap);
                                ctx.lineTo(x + gap_2x, y - gap);
                                break;
                            case Quarter.BOTTOM_LEFT:
                                currentXPos = x - gap_2x;
                                currentYPos = y - gap;
                                ctx.lineTo(x - gap, y - gap);
                                ctx.lineTo(x - gap_2x, y - gap);
                                ctx.textAlign = 'end';
                                break;
                            case Quarter.TOP_LEFT:
                                currentXPos = x - gap_2x;
                                currentYPos = y + gap;
                                ctx.lineTo(x - gap, y + gap);
                                ctx.lineTo(x - gap_2x, y + gap);
                                ctx.textAlign = 'end';
                                break;
                            default:
                                break;
                        }
                        ctx.stroke();
                        const labels = chart.data.labels as CustomChartLabel[];
                        const label = labels[index];

                        ctx.textBaseline = 'middle';

                        ctx.font = '600 14px Poppins';
                        ctx.fillStyle = malouChartColorText1;
                        ctx.fillText(`${Math.round(label.percentageValue)} %`, currentXPos, currentYPos - 8);

                        ctx.font = 'italic 400 12px Poppins';
                        ctx.fillStyle = malouChartColorText2;
                        ctx.fillText(`${label.subText[0]}`, currentXPos, currentYPos + 8);
                        ctx.fillText(`${label.subText[1]}`, currentXPos, currentYPos + 20);

                        ctx.restore();
                    });
                });
            },
        };
    }

    private _getQuarter(centerX: number, centerY: number, x: number, y: number): Quarter {
        if (x > centerX) {
            if (y > centerY) {
                return Quarter.TOP_RIGHT;
            } else {
                return Quarter.BOTTOM_RIGHT;
            }
        } else if (y > centerY) {
            return Quarter.TOP_LEFT;
        } else {
            return Quarter.BOTTOM_LEFT;
        }
    }
}
