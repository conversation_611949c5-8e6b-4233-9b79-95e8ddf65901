@if (isLoading()) {
    <ng-container [ngTemplateOutlet]="skeletonTemplate"></ng-container>
} @else {
    <ng-container [ngTemplateOutlet]="pageTemplate"></ng-container>
}

<ng-template #skeletonTemplate>
    <div class="flex flex-col gap-4">
        <app-skeleton skeletonClass="!h-[403px] secondary-bg"></app-skeleton>
        <app-skeleton skeletonClass="!h-[403px] secondary-bg"></app-skeleton>
    </div>
</ng-template>

<ng-template let-text="text" let-subtext="subtext" #emptyTemplate>
    <div class="my-4 flex flex-col items-center py-4">
        <img class="mb-6 h-20 w-20" alt="Taster illustration" [src]="Illustration.Taster | illustrationPathResolver" />
        <span class="malou-text-14--bold mb-2">
            {{ text }}
        </span>
        <span class="malou-text-10--regular">
            {{ subtext }}
        </span>
    </div>
</ng-template>

<ng-template #pageTemplate>
    @if (isAnalysisDisabledOnAllRestaurants()) {
        <ng-container
            [ngTemplateOutlet]="emptyTemplate"
            [ngTemplateOutletContext]="{
                text: 'aggregated_statistics.e_reputation.reviews_analysis.semantic_analysis_disabled' | translate,
                subtext: 'aggregated_statistics.e_reputation.reviews_analysis.activate_semantic_analysis' | translate,
            }">
        </ng-container>
    } @else {
        @if (noData()) {
            <ng-container
                [ngTemplateOutlet]="emptyTemplate"
                [ngTemplateOutletContext]="{
                    text: 'aggregated_statistics.e_reputation.reviews_analysis.semantic_analysis_not_available' | translate,
                    subtext: 'aggregated_statistics.e_reputation.reviews_analysis.edit_filters' | translate,
                }">
            </ng-container>
        } @else {
            <div class="flex flex-col gap-6">
                @if (shouldDisplayAnalysesTagCharts()) {
                    <ng-container [ngTemplateOutlet]="analysesTagChartsTemplate"></ng-container>
                }
                @if (shouldDisplayAnalysesTagEvolution()) {
                    <ng-container [ngTemplateOutlet]="analysesTagEvolutionTemplate"></ng-container>
                }
            </div>
        }
    }
</ng-template>

<ng-template #analysesTagChartsTemplate>
    <div class="malou-simple-card flex break-inside-avoid flex-col gap-3 px-6 py-3 pb-12 md:px-2">
        <div class="flex items-center gap-2">
            <div class="malou-text-section-title malou-color-text-1">
                {{ 'statistics.e_reputation.reviews_analysis.semantic_analysis' | translate }}
            </div>
            @if (warningTooltip()) {
                <div class="malou-status--waiting pdf-hidden" [matTooltip]="warningTooltip()!">
                    <mat-icon [svgIcon]="SvgIcon.EXCLAMATION_MARK"></mat-icon>
                </div>
            }
        </div>
        <div class="justify-content-between flex lg:flex-col-reverse">
            <div class="h-60 min-w-0 flex-1">
                <app-tags-bar-chart
                    [reviews]="allReviews()"
                    [shouldDisplayAnalysesTagClickableLabels]="shouldDisplayAnalysesTagClickableLabels()"
                    [isFromAggregatedStatistics]="true">
                </app-tags-bar-chart>
            </div>
            <div class="h-60 min-w-0 flex-1">
                <app-tags-doughnut-chart [reviewsByRestaurantId]="reviewsByRestaurantId()"></app-tags-doughnut-chart>
            </div>
        </div>
        @if (showSeeMoreButton()) {
            <div class="mt-12 self-center">
                <a class="malou-text-13--semibold rounded-md bg-malou-color-primary p-2" style="color: white" [href]="detailHref">
                    {{ 'semantic_analyses.go_to_detail_button' | translate }}
                </a>
            </div>
        }
    </div>
</ng-template>

<ng-template #analysesTagEvolutionTemplate>
    <div>
        @if (showSplitByRestaurant()) {
            <app-tags-evolution
                [reviewsByRestaurantId]="reviewsByRestaurantId()"
                [warningTooltip]="warningTooltip()"
                [shouldDetailTagsEvolutionCharts]="shouldDetailTagsEvolutionCharts()"
                [sortBy]="tagsEvolutionSortBy()"
                (sortByChange)="tagsEvolutionSortByChange.emit($event)">
            </app-tags-evolution>
        }
    </div>
</ng-template>
