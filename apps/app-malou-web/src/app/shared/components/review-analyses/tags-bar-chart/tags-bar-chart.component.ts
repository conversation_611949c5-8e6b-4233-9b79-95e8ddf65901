import { Component, computed, effect, inject, input, NgZone } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import {
    BubbleDataPoint,
    Chart,
    ChartDataset,
    ChartEvent,
    ChartOptions,
    ChartType,
    ChartTypeRegistry,
    Plugin,
    Point,
    Tick,
    TooltipItem,
} from 'chart.js';
import { groupBy } from 'lodash';
import { NgChartsModule } from 'ng2-charts';

import { isNotNil, PartialRecord, ReviewAnalysisSentiment, ReviewAnalysisTag } from '@malou-io/package-utils';

import { SegmentAnalysisModalComponent } from ':shared/components/review-analyses/tags-bar-chart/segment-analysis-modal/segment-analysis-modal.component';
import { ChartDataArray, malouChartColorBluePurple, malouChartColorGreen, malouChartColorRed, malouChartColorText2 } from ':shared/helpers';
import { ReviewWithAnalysis, SegmentWithReview } from ':shared/models';
import { EnumTranslatePipe } from ':shared/pipes/enum-translate.pipe';
import { PluralTranslatePipe } from ':shared/pipes/plural-translate.pipe';
import { ShortNumberPipe } from ':shared/pipes/short-number.pipe';
import { CustomDialogService } from ':shared/services/custom-dialog.service';

const DEFAULT_DISPLAYED_TAGS: ReviewAnalysisTag[] = [
    ReviewAnalysisTag.FOOD,
    ReviewAnalysisTag.SERVICE,
    ReviewAnalysisTag.ATMOSPHERE,
    ReviewAnalysisTag.PRICE,
    ReviewAnalysisTag.EXPEDITIOUSNESS,
    ReviewAnalysisTag.HYGIENE,
];

type BarChartType = Extract<ChartType, 'bar'>;

@Component({
    selector: 'app-tags-bar-chart',
    templateUrl: './tags-bar-chart.component.html',
    imports: [NgChartsModule],
    providers: [ShortNumberPipe],
})
export class TagsBarChartComponent {
    readonly reviews = input<ReviewWithAnalysis[]>([]);
    readonly shouldDisplayAnalysesTagClickableLabels = input.required<boolean>();
    readonly isFromAggregatedStatistics = input.required<boolean>();

    private readonly _enumTranslatePipe = inject(EnumTranslatePipe);

    readonly CHART_TYPE: BarChartType = 'bar';
    readonly LABEL_CLICK_PLUGIN: Plugin = this._getLabelClickPlugin();

    displayedTags: ReviewAnalysisTag[] = [];

    readonly segments = computed(() => this._buildSegmentsWithReview(this.reviews()));
    readonly chartDataSets = computed(() => this._computeChartData(this.segments()));
    readonly chartLabels = computed(() => this._computeChartLabels(this.segments()));
    readonly chartOption = computed(() => this._computeChartOptions());

    constructor(
        private readonly _customDialogService: CustomDialogService,
        private readonly _zone: NgZone,
        private readonly _translate: TranslateService,
        private readonly _shortNumberPipe: ShortNumberPipe,
        private readonly _pluralTranslatePipe: PluralTranslatePipe
    ) {
        this.displayedTags = DEFAULT_DISPLAYED_TAGS;

        effect(() => {
            this._updateDisplayedTags(this.segments());
        });
    }

    private _updateDisplayedTags(segments: SegmentWithReview[]): void {
        const positiveSegments = segments.filter((s) => s?.sentiment === ReviewAnalysisSentiment.POSITIVE);
        const negativeSegments = segments.filter((s) => s?.sentiment === ReviewAnalysisSentiment.NEGATIVE);

        const positiveTagsMap = this._buildTagsMap(positiveSegments);
        const negativeTagsMap = this._buildTagsMap(negativeSegments);

        this.displayedTags = DEFAULT_DISPLAYED_TAGS.filter((tag) => positiveTagsMap[tag]?.length || negativeTagsMap[tag]?.length);
    }

    private _buildSegmentsWithReview(reviews: ReviewWithAnalysis[]): SegmentWithReview[] {
        return reviews
            .flatMap((review) => {
                const oldSegments = review?.semanticAnalysis?.segmentAnalyses ?? [];
                const oldSegmentsWithReview: SegmentWithReview[] = oldSegments.map((segment) => ({
                    ...segment,
                    review,
                }));

                const newSegments = review?.semanticAnalysisSegments ?? [];
                const newSegmentsWithReview: SegmentWithReview[] = newSegments.map((segment) => ({
                    tag: segment.category,
                    segment: segment.segment ?? '',
                    sentiment: segment.sentiment,
                    position: segment.position,
                    review,
                }));
                const allSegmentsWithReview: SegmentWithReview[] = [...oldSegmentsWithReview, ...newSegmentsWithReview];
                return allSegmentsWithReview;
            })
            .filter(isNotNil)
            .filter((el) => el?.tag !== ReviewAnalysisTag.OVERALL_EXPERIENCE);
    }

    private _computeChartLabels(segments: SegmentWithReview[]): string[][] {
        const tagsMap = this._buildTagsMap(segments);
        return this.shouldDisplayAnalysesTagClickableLabels()
            ? this.displayedTags.map((tag) => [
                  `${this._enumTranslatePipe.transform(tag, 'review_analysis_tags')}  (${tagsMap[tag]?.length})`,
                  this._translate.instant('statistics.e_reputation.reviews_analysis.see_detail'),
              ])
            : this.displayedTags.map((tag) => [
                  `${this._enumTranslatePipe.transform(tag, 'review_analysis_tags')}  (${tagsMap[tag]?.length})`,
              ]);
    }

    private _computeChartData(data: SegmentWithReview[]): ChartDataset<BarChartType, ChartDataArray>[] {
        const positiveSegments = data.filter((s) => s?.sentiment === ReviewAnalysisSentiment.POSITIVE);
        const negativeSegments = data.filter((s) => s?.sentiment === ReviewAnalysisSentiment.NEGATIVE);

        const positiveTagsMap = this._buildTagsMap(positiveSegments);
        const negativeTagsMap = this._buildTagsMap(negativeSegments);

        return [
            {
                borderColor: malouChartColorGreen,
                backgroundColor: malouChartColorGreen,
                xAxisID: 'xAxis',
                yAxisID: 'yAxis',
                barThickness: 7,
                data: this.displayedTags.map((label) => positiveTagsMap[label]?.length || 0),
            },
            {
                borderColor: malouChartColorRed,
                backgroundColor: malouChartColorRed,
                xAxisID: 'xAxis',
                yAxisID: 'yAxis',
                barThickness: 7,
                data: this.displayedTags.map((label) => negativeTagsMap[label]?.length || 0),
            },
        ];
    }

    private _computeChartOptions(): ChartOptions<BarChartType> {
        return {
            plugins: {
                tooltip: {
                    callbacks: {
                        title: () => '',
                        label: (tooltipItems: TooltipItem<any>) => this._computeTooltipLabel(tooltipItems),
                    },
                },
                legend: {
                    display: false,
                },
            },
            scales: {
                xAxis: {
                    stacked: true,
                    axis: 'x',
                    type: 'category',
                    ticks: {
                        crossAlign: 'center',
                        color: this.shouldDisplayAnalysesTagClickableLabels() ? malouChartColorBluePurple : malouChartColorText2,
                    },
                },
                yAxis: {
                    stacked: true,
                    axis: 'y',
                    type: 'linear',
                    ticks: {
                        callback: (tickValue: number | string, _index: number, _ticks: Tick[]) =>
                            this._shortNumberPipe.transform(tickValue as number),
                    },
                },
            },
        };
    }

    private _computeTooltipLabel(tooltipItems: TooltipItem<any>): string {
        const value = parseInt(tooltipItems.formattedValue, 10);
        const sentiment =
            tooltipItems.datasetIndex === 0
                ? this._pluralTranslatePipe.transform('statistics.e_reputation.reviews_analysis.positive', value).toLowerCase()
                : this._pluralTranslatePipe.transform('statistics.e_reputation.reviews_analysis.negative', value).toLowerCase();
        return this._pluralTranslatePipe.transform('statistics.e_reputation.reviews_analysis.feeling', value, {
            sentiment,
        });
    }

    private _buildTagsMap(segmentsWithReview: SegmentWithReview[]): PartialRecord<ReviewAnalysisTag, SegmentWithReview[]> {
        const object = groupBy(segmentsWithReview, 'tag');
        Object.values(ReviewAnalysisTag)
            .filter((tag) => tag !== ReviewAnalysisTag.OVERALL_EXPERIENCE)
            .forEach((tag) => (object[tag] = object[tag] || []));
        return object;
    }

    private _openSegmentAnalysisModal(tag: ReviewAnalysisTag): void {
        const tagsMap = this._buildTagsMap(this.segments());
        this._customDialogService.open(SegmentAnalysisModalComponent, {
            width: '80vw',
            height: '80vh',
            disableClose: true,
            data: {
                segments: tagsMap[tag],
                isFromAggregatedStatistics: this.isFromAggregatedStatistics(),
            },
        });
    }

    private _getLabelClickPlugin(): Plugin {
        return {
            id: 'labelClick',
            afterEvent: (chart: Chart, args: { event: ChartEvent }): void => {
                const { event } = args;
                const detailsBtnOffset = 27;
                const datasets = chart.data.datasets;
                if (event.type !== 'click') {
                    return;
                }
                const { x, y } = args.event;

                if (
                    !x ||
                    x < chart.scales.xAxis.left ||
                    x > chart.scales.xAxis.right ||
                    !y ||
                    y < chart.scales.xAxis.top - detailsBtnOffset
                ) {
                    return;
                }

                const index = chart.scales.xAxis.getValueForPixel(x);

                if (index !== undefined && !this._isDataPointEqualToZero(datasets, index)) {
                    const clickedTag = this.displayedTags[index];
                    this._zone.run(() => this._openSegmentAnalysisModal(clickedTag));
                }
            },
        };
    }

    private _isDataPointEqualToZero(
        datasets: ChartDataset<keyof ChartTypeRegistry, (number | Point | [number, number] | BubbleDataPoint | null)[]>[],
        index: number
    ): boolean {
        if (index < 0) {
            return false;
        }
        return datasets.reduce((acc, next) => (next.data[index] as number) + acc, 0) === 0;
    }
}
