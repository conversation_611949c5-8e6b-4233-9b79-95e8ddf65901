import { NgClass, NgTemplateOutlet } from '@angular/common';
import { Component, computed, effect, inject, signal } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { TranslateModule } from '@ngx-translate/core';
import { groupBy } from 'lodash';
import { DateTime } from 'luxon';
import { LazyLoadImageModule } from 'ng-lazyload-image';

import { MalouPeriod, ReviewAnalysisSentiment } from '@malou-io/package-utils';

import { RestaurantsService } from ':core/services/restaurants.service';
import { RestaurantHeaderForReviewPreviewComponent } from ':modules/reviews/review-preview/restaurant-header-for-review-preview/restaurant-header-for-review-preview.component';
import * as ReviewsActions from ':modules/reviews/store/reviews.actions';
import { PlatformLogoComponent } from ':shared/components/platform-logo/platform-logo.component';
import { StarGaugeComponent } from ':shared/components/star-gauge/star-gauge.component';
import { Restaurant, ReviewWithAnalysis, SegmentWithReview } from ':shared/models';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { AvatarPipe } from ':shared/pipes/avatar.pipe';
import { DateToStringPipe } from ':shared/pipes/date.pipe';
import { EnumTranslatePipe } from ':shared/pipes/enum-translate.pipe';
import { PluralCategory, PluralTranslatePipe } from ':shared/pipes/plural-translate.pipe';

enum TabIndex {
    POSITIVE,
    NEGATIVE,
}

@Component({
    selector: 'app-segment-analysis-modal',
    templateUrl: './segment-analysis-modal.component.html',
    styleUrls: ['./segment-analysis-modal.component.scss'],
    imports: [
        NgClass,
        NgTemplateOutlet,
        MatDividerModule,
        MatIconModule,
        MatButtonModule,
        MatTabsModule,
        LazyLoadImageModule,
        TranslateModule,
        PlatformLogoComponent,
        RestaurantHeaderForReviewPreviewComponent,
        StarGaugeComponent,
        AvatarPipe,
        DateToStringPipe,
        PluralTranslatePipe,
        EnumTranslatePipe,
    ],
})
export class SegmentAnalysisModalComponent {
    public readonly data = inject<{
        segments: SegmentWithReview[];
        isFromAggregatedStatistics: boolean;
    }>(MAT_DIALOG_DATA);
    private readonly _dialogRef = inject(MatDialogRef<SegmentAnalysisModalComponent>);
    private readonly _sanitizer = inject(DomSanitizer);
    private readonly _router = inject(Router);
    private readonly _restaurantsService = inject(RestaurantsService);
    private readonly _store = inject(Store);

    readonly SvgIcon = SvgIcon;
    readonly PluralCategory = PluralCategory;
    readonly semanticAnalysisSentiment = ReviewAnalysisSentiment;

    readonly segments = signal<SegmentWithReview[]>([]);
    readonly selectedSegment = signal<string | null>(null);
    readonly showReviewsTemplate = signal<boolean>(false);
    readonly selectedTabIndex = signal<number>(0);
    readonly segmentReviews = signal<ReviewWithAnalysis[]>([]);
    readonly restaurantsById = signal<Record<string, Restaurant>>({});
    readonly isFromAggregatedStatistics = signal<boolean>(false);

    readonly positiveSegments = computed(() => {
        const segments = this.segments();
        return groupBy(
            segments?.filter((segment) => segment.sentiment === ReviewAnalysisSentiment.POSITIVE),
            (val) => val.segment.toLowerCase()
        );
    });
    readonly negativeSegments = computed(() => {
        const segments = this.segments();
        return groupBy(
            segments?.filter((segment) => segment.sentiment === ReviewAnalysisSentiment.NEGATIVE),
            (val) => val.segment.toLowerCase()
        );
    });

    readonly sortedPositiveSegments = computed(() => {
        const positive = this.positiveSegments();
        return Object.keys(positive).sort((a, b) => positive[b].length - positive[a].length);
    });

    readonly sortedNegativeSegments = computed(() => {
        const negative = this.negativeSegments();
        return Object.keys(negative).sort((a, b) => negative[b].length - negative[a].length);
    });
    readonly positiveSegmentsLength = computed(() => Object.values(this.positiveSegments()).flat().length);
    readonly negativeSegmentsLength = computed(() => Object.values(this.negativeSegments()).flat().length);

    constructor() {
        // Initialize data from injected values
        this.isFromAggregatedStatistics.set(this.data.isFromAggregatedStatistics);
        this.segments.set(this.data.segments);

        // Setup restaurants data if needed
        if (this.data.isFromAggregatedStatistics) {
            const restaurantsMap = this._restaurantsService.restaurants().reduce(
                (acc, restaurant) => {
                    acc[restaurant._id] = restaurant;
                    return acc;
                },
                {} as Record<string, Restaurant>
            );
            this.restaurantsById.set(restaurantsMap);
        }

        // Auto-select first segment when data is ready
        effect(() => {
            const positiveLength = this.positiveSegmentsLength();
            if (positiveLength > 0) {
                this.selectFirstSegment(TabIndex.POSITIVE);
            } else {
                this.selectFirstSegment(TabIndex.NEGATIVE);
            }
        });
    }

    selectSegment(segmentText: string, sentiment: ReviewAnalysisSentiment): void {
        this.selectedSegment.set(segmentText);
        const color = sentiment === ReviewAnalysisSentiment.POSITIVE ? '#34B467' : '#EE116E';
        const highlightSegmentText = `<span style="color: ${color}; font-weight: 500;">${segmentText}</span>`;

        if (sentiment === ReviewAnalysisSentiment.NEGATIVE) {
            const negativeReviews =
                this.negativeSegments()[segmentText]?.map((s) => this._getSegmentsWithHighlightedText(s, highlightSegmentText)) || [];
            this.segmentReviews.set(negativeReviews);
            this.showReviewsTemplate.set(true);
            return;
        }

        const positiveReviews =
            this.positiveSegments()[segmentText]?.map((s) => this._getSegmentsWithHighlightedText(s, highlightSegmentText)) || [];
        this.segmentReviews.set(positiveReviews);
        this.showReviewsTemplate.set(true);
    }

    selectFirstSegment(tabIndex: TabIndex = TabIndex.POSITIVE): void {
        switch (tabIndex) {
            case TabIndex.POSITIVE:
                this.selectedTabIndex.set(0);
                const firstPositiveSegment = this.sortedPositiveSegments()[0];
                if (firstPositiveSegment) {
                    this.selectSegment(firstPositiveSegment, ReviewAnalysisSentiment.POSITIVE);
                }
                break;
            case TabIndex.NEGATIVE:
                this.selectedTabIndex.set(1);
                const firstNegativeSegment = this.sortedNegativeSegments()[0];
                if (firstNegativeSegment) {
                    this.selectSegment(firstNegativeSegment, ReviewAnalysisSentiment.NEGATIVE);
                }
                break;
            default:
                break;
        }
    }

    close(): void {
        this._dialogRef.close();
    }

    handleTabChange(tabIndex: number): void {
        this.segmentReviews.set([]);
        this.selectedSegment.set(null);
        this.selectFirstSegment(tabIndex);
    }

    goToReview(review: ReviewWithAnalysis): void {
        this.close();
        const startDate = DateTime.fromJSDate(review.socialCreatedAt).startOf('day').toJSDate();
        const endDate = DateTime.fromJSDate(review.socialCreatedAt).endOf('day').plus({ day: 7 }).toJSDate();
        this._store.dispatch(
            ReviewsActions.editReviewsFiltersDates({
                datesFilters: {
                    period: MalouPeriod.CUSTOM,
                    startDate,
                    endDate,
                },
            })
        );
        if (this.isFromAggregatedStatistics()) {
            this._router.navigate(['/groups', 'reputation', 'reviews'], {
                queryParams: { reviewId: review._id, resetDatesFilters: true },
            });
            return;
        }
        this._router.navigate(['/restaurants', review.restaurantId, 'reputation', 'reviews'], {
            queryParams: { reviewId: review._id, resetDatesFilters: true },
        });
    }

    private _getSegmentsWithHighlightedText(
        segmentsWithReview: SegmentWithReview,
        highlightSegmentText: string
    ): ReviewWithAnalysis & { highlightedText: SafeHtml } {
        const reviewSegmentText = segmentsWithReview.segment;
        return {
            ...segmentsWithReview.review,
            highlightedText: this._sanitizer.bypassSecurityTrustHtml(
                segmentsWithReview.review.text?.replace(reviewSegmentText, highlightSegmentText) ?? ''
            ),
        };
    }
}
