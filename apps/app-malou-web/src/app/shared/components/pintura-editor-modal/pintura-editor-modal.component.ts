import { DIALOG_DATA } from '@angular/cdk/dialog';
import { ChangeDetectionStrategy, Component, inject, viewChild } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { AngularPinturaModule, PinturaEditorComponent } from '@pqina/angular-pintura';
import {
    createDefaultColorOptions,
    createDefaultFontScaleOptions,
    createMarkupEditorBackgroundColorControl,
    createMarkupEditorColorOptions,
    createMarkupEditorFontScaleOptions,
    createMarkupEditorFontSizeControl,
    createMarkupEditorShapeStyleControls,
    createMarkupEditorToolbar,
    createMarkupEditorToolStyles,
    getEditorDefaults,
    locale_en_gb,
    PinturaDefaultImageWriterResult,
    PinturaEditorOptions,
    PinturaImageState,
    plugin_frame_defaults,
} from '@pqina/pintura';
import locale_es_es from '@pqina/pintura/locale/es_ES';
import locale_fr_fr from '@pqina/pintura/locale/fr_FR';
import locale_it_it from '@pqina/pintura/locale/it_IT';
import { omit } from 'lodash';
import { v4 } from 'uuid';

import { ApplicationLanguage } from '@malou-io/package-utils';

import { ButtonStyle, ModalStructureComponent } from '../modal-structure/modal-structure.component';

type AspectRatio = '9:16' | '4:5' | '1:1' | '16:9';

/**
 * Priority for selecting the crop aspect ratio:
 * 1. cropAspectRatioOverride
 * 2. serializedPinturaEditorOptions (this object can contains a cropAspectRatio)
 * 3. cropAspectRatioDefault
 */
export interface PinturaEditorModalData {
    url: string;
    cropAspectRatioDefault?: AspectRatio;
    cropAspectRatioOverride?: AspectRatio;
    cropAspectRatioPresets?: AspectRatio[];
    serializedPinturaEditorOptions?: string;
}

export interface PinturaEditorModalResult {
    file: File;
    cropAspectRatioSelected?: AspectRatio;
    serializedPinturaEditorOptions: string;
}

function aspectRatioToNumber(aspectRatio: AspectRatio): number {
    switch (aspectRatio) {
        case '9:16':
            return 9 / 16;
        case '4:5':
            return 4 / 5;
        case '1:1':
            return 1;
        case '16:9':
            return 16 / 9;
    }
}

function numberToAspectRatio(number: number): AspectRatio | undefined {
    switch (number) {
        case 9 / 16:
            return '9:16';
        case 4 / 5:
            return '4:5';
        case 1:
            return '1:1';
        case 16 / 9:
            return '16:9';
        default:
            return undefined;
    }
}

@Component({
    selector: 'app-pintura-editor-modal',
    templateUrl: './pintura-editor-modal.component.html',
    imports: [AngularPinturaModule, ModalStructureComponent, TranslateModule],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PinturaEditorModalComponent {
    private readonly _dialogRef = inject<MatDialogRef<PinturaEditorModalComponent, PinturaEditorModalResult>>(MatDialogRef);
    private readonly _dialogData = inject<PinturaEditorModalData>(DIALOG_DATA);
    private readonly _translateService = inject(TranslateService);

    private readonly _pinturaEditorComponent = viewChild.required(PinturaEditorComponent);

    private readonly _imageState: PinturaImageState = JSON.parse(this._dialogData.serializedPinturaEditorOptions ?? '{}');
    private readonly _imageStateComputed = this._dialogData.cropAspectRatioOverride
        ? omit(this._imageState, 'cropAspectRatio', 'crop')
        : this._imageState;
    private readonly _cropAspectRatioComputed = this._dialogData.cropAspectRatioOverride ?? this._dialogData.cropAspectRatioDefault;
    readonly editorOptions = getEditorDefaults({
        imageState: this._imageStateComputed,
        src: this._addRandomQueryParam(this._dialogData.url),
        utils: ['crop', 'filter', 'finetune', 'annotate', 'frame'],
        // We exclude the 'polaroid' frame option, as this frame is added outside of the image and can change the aspect ratio.
        frameOptions: plugin_frame_defaults.frameOptions?.filter((o) => o[0] !== 'polaroid'),
        // We use a custom button to validate the edition, so we disable the default one
        enableButtonExport: false,
        imageCropAspectRatio: this._cropAspectRatioComputed ? aspectRatioToNumber(this._cropAspectRatioComputed) : undefined,
        cropSelectPresetOptions: this._dialogData.cropAspectRatioPresets?.map((e) => [aspectRatioToNumber(e), e]),
        // By default flip horizontal is enabled, but not flip vertical
        cropEnableButtonFlipVertical: true,
        enableMoveTool: true,
        enableAutoSelectMoveTool: false,
        // By default the scroll direction on MacOS is incorrectly inverted.
        // This will be addressed in the next major release.
        fixScrollDirection: true,
        markupEditorToolbar: createMarkupEditorToolbar([
            'move',
            'text',
            'sharpie',
            'eraser',
            'path',
            'line',
            'arrow',
            'rectangle',
            'ellipse',
        ]),
        markupEditorToolStyles: createMarkupEditorToolStyles({
            text: {
                // This activate the background color picker (and set the default color to transparent)
                backgroundColor: createDefaultColorOptions().transparent,
                fontSize: createDefaultFontScaleOptions().medium,
                disableFlip: true,
            },
        }),
        markupEditorShapeStyleControls: createMarkupEditorShapeStyleControls({
            // First element is the font name (may be multiple separated with comma), second element is the label
            fontFamilyOptions: [
                ['Protest Revolution', 'Rosalia'],
                ['Playwrite IT Moderna', 'Signature'],
                ['JetBrains Mono', 'Editor'],
                ['Cherry Bomb One', 'Bubble'],
                ['Quicksand', 'Deco'],
                ['Coiny', 'Poster'],
                ['Fjalla One', 'Squeeze'],
                ['Gabarito', 'Modern'],
                ['Instrument Sans', 'Classic'],
                ['Courier Prime', 'Typewriter'],
                ['Racing Sans One', 'Strong'],
                ['Nanum Myeongjo', 'Elegant'],
                ['Lexend', 'Directional'],
                ['Cormorant', 'Literature'],
            ],
            backgroundColor: createMarkupEditorBackgroundColorControl(createMarkupEditorColorOptions(createDefaultColorOptions()), {
                enableEyeDropper: true,
            }),
            fontSize: createMarkupEditorFontSizeControl(createMarkupEditorFontScaleOptions(createDefaultFontScaleOptions()), {
                enableInput: true,
            }),
        }),
        enableTapToAddText: true,
        locale: this._getPinturaLocale(),
        cropActiveTransformTool: 'zoom',
    }) as PinturaEditorOptions;

    readonly ButtonStyle = ButtonStyle;

    onPrimaryButtonClick(): void {
        // This will trigger the onProcess fn via the (process) output of <pintura-editor>
        this._pinturaEditorComponent().editor?.processImage();
    }

    onSecondaryButtonClick(): void {
        this.close();
    }

    onProcess(event: PinturaDefaultImageWriterResult): void {
        this._dialogRef.close({
            file: event.dest,
            cropAspectRatioSelected: event.imageState.cropAspectRatio ? numberToAspectRatio(event.imageState.cropAspectRatio) : undefined,
            serializedPinturaEditorOptions: JSON.stringify(event.imageState),
        });
    }

    close(result?: PinturaEditorModalResult): void {
        this._dialogRef.close(result);
    }

    private _getPinturaLocale(): any {
        const currentLang = this._translateService.currentLang;
        switch (currentLang) {
            case ApplicationLanguage.FR:
                return { ...locale_fr_fr, cropLabelTabZoom: 'Zoomer' };
            case ApplicationLanguage.EN:
                return locale_en_gb;
            case ApplicationLanguage.ES:
                return locale_es_es;
            case ApplicationLanguage.IT:
                return locale_it_it;
        }
    }

    /**
     * This prevent any browser (specifically chrome) from using a cached version of the image.
     * It force the browser to fetch the image again.
     * Solves this CORS problem : https://stackoverflow.com/questions/44865121/canvas-tainted-by-cors-data-and-s3/44866772#44866772
     */
    private _addRandomQueryParam(url: string): string {
        return `${url}?s3-chrome-headache=${v4()}`;
    }
}
