@use '_malou_animations.scss' as *;
@use '_malou_functions.scss' as *;
@use '_malou_variables.scss' as *;

.crop-options-container {
    padding: 10px 20px;
    border: 1px solid $malou-color-border-primary;
    border-radius: 8px;
    transition: all 0.15s ease-in-out;
    z-index: 2;
}

.crop-options {
    width: 0;
    opacity: 0;
    margin: 0;
    transition: all 0.15s ease-in-out;
    translate: translateX(-100%);
}

.dimensions-toggled {
    background: $malou-color-background-dark;
    border: 1px solid $malou-color-border-secondary;

    .crop-options {
        width: 160px;
        opacity: 1;
        transform: translateX(0);
        margin-left: 12px;

        span {
            position: relative;

            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: $malou-color-background-white;
                width: 100%;
                border-radius: 10px;
                z-index: -1;
                opacity: 0;
                transition: all 0.15s ease-in-out;
            }

            transition: all 0.15s ease-in-out;
        }
    }
}

.selected-crop-option {
    color: $malou-color-primary;
    padding: 3px 8px;

    &::before {
        opacity: 1 !important;
    }
}

::ng-deep .ngx-ic-source-image {
    display: inline-flex !important;
}

::ng-deep .ngx-ic-cropper {
    outline-color: rgba($malou-color-background-dark, 0.3) !important;
}

::ng-deep .ngx-ic-cropper .ngx-ic-move {
    border: 2px solid $malou-color-primary !important;
}

::ng-deep .ngx-ic-square {
    background: $malou-color-primary !important;
    border-radius: 50%;
}

img,
video {
    border-radius: toRem(10px);
}

.small-media {
    &:hover {
        .absolute {
            display: block;
        }
    }
}

:host ::ng-deep {
    .cdk-drag-preview {
        box-sizing: border-box;
        border-radius: 4px;
        box-shadow:
            0 5px 5px -3px rgba(0, 0, 0, 0.2),
            0 8px 10px 1px rgba(0, 0, 0, 0.14),
            0 3px 14px 2px rgba(0, 0, 0, 0.12);
    }

    .cdk-drag-placeholder {
        opacity: 0;
    }

    .cdk-drag-animating {
        transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
    }

    .medias-list.cdk-drop-list-dragging .small-media:not(.cdk-drag-placeholder) {
        transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
    }
}

.bg-light-error {
    background: $malou-color-background-error;
}

#imageContainer {
    &:hover {
        .cursor-tag {
            cursor: crosshair;
        }
    }
}

.fade-in {
    animation: fadeIn 0.2s cubic-bezier(0.39, 0.575, 0.565, 1);
}

@keyframes fadeIn {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}

.appear-animation {
    animation: appearAnimation 0.2s cubic-bezier(0.39, 0.575, 0.565, 1);
}

@keyframes appearAnimation {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}
