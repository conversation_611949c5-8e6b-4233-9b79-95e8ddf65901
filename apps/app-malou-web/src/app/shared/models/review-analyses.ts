import { PreviousSegmentAnalysisDto, ReviewAnalysisDto } from '@malou-io/package-dto';
import { RemoveMethodsFromClass, ReviewAnalysisSentiment, ReviewAnalysisStatus, ReviewAnalysisTag } from '@malou-io/package-utils';

type SemanticAnalysisProps = RemoveMethodsFromClass<SemanticAnalysis>;

export class SemanticAnalysis {
    id: string;
    providerKey: string;
    platformKey: string;
    reviewSocialId: string;
    status?: ReviewAnalysisStatus;
    socialCreatedAt?: Date;
    segmentAnalyses: SegmentAnalyses[];

    public constructor(init: SemanticAnalysisProps, reviewText: string | null) {
        this.id = init.id;
        this.platformKey = init.platformKey;
        this.status = init.status;
        this.reviewSocialId = init.reviewSocialId;
        this.socialCreatedAt = init.socialCreatedAt;
        this.segmentAnalyses = init.segmentAnalyses || [];
        this.socialCreatedAt = this.socialCreatedAt ? new Date(this.socialCreatedAt) : undefined;
        const sortedSegments = this._sortSegmentsByPositionInText(this.segmentAnalyses, reviewText);
        const filteredSegments = this._filterSegmentAnalysesByTag(sortedSegments, ReviewAnalysisTag.OVERALL_EXPERIENCE);
        this.segmentAnalyses = this._filterSegmentAnalysesBySentiment(filteredSegments, ReviewAnalysisSentiment.NEUTRAL);
    }

    static fromReviewAnalysisDto(semanticAnalysis: ReviewAnalysisDto | undefined, reviewText: string): SemanticAnalysis | null {
        if (!semanticAnalysis) {
            return null;
        }

        return new SemanticAnalysis(
            {
                ...semanticAnalysis,
                id: semanticAnalysis._id,
                status: SemanticAnalysis.mapToReviewAnalysisStatus(semanticAnalysis.status),
                socialCreatedAt: semanticAnalysis.socialCreatedAt ? new Date(semanticAnalysis.socialCreatedAt) : undefined,
                segmentAnalyses:
                    semanticAnalysis.segmentAnalyses?.map((segmentAnalysis) => SemanticAnalysis.mapToSegmentAnalysis(segmentAnalysis)) ??
                    [],
            },
            reviewText
        );
    }

    static mapToReviewAnalysisStatus(status: string): ReviewAnalysisStatus | undefined {
        switch (status) {
            case ReviewAnalysisStatus.DONE:
                return ReviewAnalysisStatus.DONE;
            case ReviewAnalysisStatus.PENDING:
                return ReviewAnalysisStatus.PENDING;
            case ReviewAnalysisStatus.FAILED:
                return ReviewAnalysisStatus.FAILED;
            case ReviewAnalysisStatus.REVIEW_TOO_OLD:
                return ReviewAnalysisStatus.REVIEW_TOO_OLD;
            case ReviewAnalysisStatus.UNSUPPORTED_PLATFORM:
                return ReviewAnalysisStatus.UNSUPPORTED_PLATFORM;
            case ReviewAnalysisStatus.NO_RESULTS:
                return ReviewAnalysisStatus.NO_RESULTS;
            default:
                return undefined;
        }
    }

    static mapToSegmentAnalysis(segmentAnalysis: PreviousSegmentAnalysisDto): SegmentAnalyses {
        return {
            tag: segmentAnalysis.tag,
            segment: segmentAnalysis.segment,
            sentiment: SemanticAnalysis.mapToReviewAnalysisSentiment(segmentAnalysis.sentiment),
        };
    }

    static mapToReviewAnalysisSentiment(sentiment: ReviewAnalysisSentiment): ReviewAnalysisSentiment {
        switch (sentiment) {
            case ReviewAnalysisSentiment.POSITIVE:
                return ReviewAnalysisSentiment.POSITIVE;
            case ReviewAnalysisSentiment.NEGATIVE:
                return ReviewAnalysisSentiment.NEGATIVE;
            case ReviewAnalysisSentiment.NEUTRAL:
                return ReviewAnalysisSentiment.NEUTRAL;
        }
    }

    private _sortSegmentsByPositionInText(segmentAnalyses: SegmentAnalyses[], reviewText: string | null): SegmentAnalyses[] {
        const cleanReviewText = reviewText ? reviewText.replace(/\n/g, '') : '';
        return segmentAnalyses
            ?.map((segmentAnalysis) => ({
                ...segmentAnalysis,
                position: cleanReviewText.indexOf(segmentAnalysis.segment.replace(/\n/g, '').trim()),
            }))
            .sort((a, b) => a.position - b.position)
            .map((segmentAnalysis: SegmentAnalyses) => {
                delete segmentAnalysis.position;
                return {
                    ...segmentAnalysis,
                    segment: segmentAnalysis.segment.trim(),
                };
            });
    }

    private _filterSegmentAnalysesByTag(segmentAnalyses: SegmentAnalyses[], tag: ReviewAnalysisTag): SegmentAnalyses[] {
        return segmentAnalyses?.filter((segmentAnalysis) => segmentAnalysis.tag !== tag);
    }

    private _filterSegmentAnalysesBySentiment(segmentAnalyses: SegmentAnalyses[], sentiment: ReviewAnalysisSentiment): SegmentAnalyses[] {
        return segmentAnalyses?.filter((segmentAnalysis) => segmentAnalysis.sentiment?.toLowerCase() !== sentiment?.toLowerCase());
    }
}
export interface SegmentAnalyses {
    tag: ReviewAnalysisTag;
    segment: string;
    sentiment: ReviewAnalysisSentiment;
    position?: number;
}

export enum SemanticAnalysisProviderKey {
    OPENAI = 'openai',
}
