import { Injectable } from '@angular/core';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { capitalize } from 'lodash';

import { ReviewAnalysisSentiment } from '@malou-io/package-utils';

import { concatReviewTextAndRatingTags } from ':shared/helpers/review-text-and-tags';
import { Review, SegmentAnalyses, SemanticAnalysisInsightsReview } from ':shared/models';
import { PrivateReview } from ':shared/models/private-review';
import { SegmentAnalysis } from ':shared/models/segment-analysis';

@Injectable({ providedIn: 'root' })
export class ReviewAnalysisHighlighter {
    constructor(private readonly _sanitizer: DomSanitizer) {}

    getHighlightedReviewTextHtml({
        review,
        isNewSemanticAnalysisFeatureEnabledForReview,
        translationOptions,
    }: {
        review: Review | PrivateReview | SemanticAnalysisInsightsReview;
        isNewSemanticAnalysisFeatureEnabledForReview: boolean;
        translationOptions?: { showTranslation: boolean; language: string };
    }): SafeHtml {
        const reviewSegments =
            isNewSemanticAnalysisFeatureEnabledForReview || review instanceof SemanticAnalysisInsightsReview
                ? review?.semanticAnalysisSegments
                : review?.semanticAnalysis?.segmentAnalyses;

        let reviewText = review.text ?? '';
        let reviewRatingTags = (review as Review).getRatingTags() ?? [];
        if (!reviewSegments?.length || (!reviewText && !reviewRatingTags.length)) {
            return review.getFullReviewTextWithRatingTags(translationOptions) ?? '';
        }

        reviewSegments?.forEach((element) => {
            const highlightedSegment = this._getHighlightedSegment({ element, review: review as Review, translationOptions });
            reviewText = reviewText.replace(element.segment, highlightedSegment);
            reviewRatingTags = reviewRatingTags.map((tag) =>
                // TODO: remove the replace : temporary because AI is adding . at the end of topic and segments
                tag.toLowerCase().replace(element.segment?.toLowerCase().replace(/\.$/, ''), capitalize(highlightedSegment))
            );
        });

        const reviewTextWithRatingTags = concatReviewTextAndRatingTags(reviewText, reviewRatingTags);
        return this._sanitizer.bypassSecurityTrustHtml(reviewTextWithRatingTags);
    }

    setReviewTextHtmlSegmentsHighlights({
        review,
        segmentAnalysis,
        isNewSemanticAnalysisFeatureEnabledForReview,
        translationOptions,
    }: {
        review: Review | PrivateReview;
        segmentAnalysis: SegmentAnalyses | SegmentAnalysis;
        isNewSemanticAnalysisFeatureEnabledForReview: boolean;
        translationOptions?: { showTranslation: boolean; language: string };
    }): SafeHtml {
        const sameSegmentAnalysis = isNewSemanticAnalysisFeatureEnabledForReview
            ? review.semanticAnalysisSegments?.filter(
                  (item) => item.sentiment === segmentAnalysis.sentiment && item.category === (segmentAnalysis as SegmentAnalysis).category
              )
            : review.semanticAnalysis?.segmentAnalyses?.filter(
                  (item) => item.sentiment === segmentAnalysis.sentiment && item.tag === (segmentAnalysis as SegmentAnalyses).tag
              );

        let highlightedReviewText = review.text ?? '';
        let highlightedRatingTags = (review as Review).getRatingTags() ?? [];
        sameSegmentAnalysis?.forEach((element) => {
            const highlightedSegment = this._getHighlightedSegment({ element, review: review as Review, translationOptions });
            highlightedReviewText = highlightedReviewText.replace(element.segment, highlightedSegment);
            highlightedRatingTags = highlightedRatingTags.map((tag) =>
                // TODO: remove the replace : temporary because AI is adding . at the end of topic and segments
                tag.toLowerCase().replace(element.segment?.toLowerCase().replace(/\.$/, ''), highlightedSegment)
            );
        });

        const highlightedReviewTextWithRatingTags = concatReviewTextAndRatingTags(highlightedReviewText, highlightedRatingTags);
        return this._sanitizer.bypassSecurityTrustHtml(highlightedReviewTextWithRatingTags);
    }

    // TODO: Remove SegmentAnalyses type when feature toggle 'release-new-semantic-analysis' is removed
    private _getHighlightedSegment({
        element,
        review,
        translationOptions,
    }: {
        element: SegmentAnalyses | SegmentAnalysis;
        review: Review;
        translationOptions?: { showTranslation: boolean; language: string };
    }): string {
        const ratingTags = (review as Review).getRatingTags();
        const isSegmentARatingTag = this._isSegmentARatingTag(element.segment, ratingTags);
        const color = element.sentiment === ReviewAnalysisSentiment.POSITIVE ? '#34B467' : '#EE116E';
        if (!translationOptions?.showTranslation || !isSegmentARatingTag) {
            // TODO: remove the replace : temporary because AI is adding . at the end of topic and segments
            return `<span style="color: ${color};">${element.segment?.replace(/\.$/, '')}</span>`;
        }

        const translatedRatingTag =
            review.ratingTags?.find((ratingTag) => ratingTag.tag.toLowerCase() === element.segment?.toLowerCase().replace(/\.$/, ''))
                ?.translations?.[translationOptions.language] ?? element.segment;

        return `<span style="color: ${color};">${translatedRatingTag}</span>`;
    }

    private _isSegmentARatingTag(segmentText?: string, ratingTags?: string[]): boolean {
        return ratingTags?.some((tag) => tag.toLowerCase() === segmentText?.toLowerCase().replace(/\.$/, '')) ?? false;
    }
}
