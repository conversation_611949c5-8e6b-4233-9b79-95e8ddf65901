import { ChangeDetectionStrategy, Component, computed, effect, ElementRef, inject, input, viewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Router } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';

import { IntelligentSubjectAutomationDto } from '@malou-io/package-dto';
import { ReviewAnalysisSentiment, TooltipPosition } from '@malou-io/package-utils';

import { LocalStorage } from ':core/storage/local-storage';
import { User } from ':modules/user/user';
import { ReviewIntelligentSubjectTooltipComponent } from ':shared/components/review-intelligent-subject-tooltip/review-intelligent-subject-tooltip.component';
import { CustomTooltipDirective } from ':shared/directives/custom-tooltip-directive';
import { LocalStorageKey } from ':shared/enums/local-storage-key';
import { PrivateReview, Review } from ':shared/models';
import { SvgIcon } from ':shared/modules/svg-icon.enum';

interface ReviewSensitiveSubjectsTooltipShown {
    [userId: string]: { [reviewId: string]: boolean };
}
@Component({
    selector: 'app-intelligent-subject-headband',
    imports: [TranslateModule, MatTooltipModule, ReviewIntelligentSubjectTooltipComponent, CustomTooltipDirective],
    templateUrl: './intelligent-subject-headband.component.html',
    styleUrl: './intelligent-subject-headband.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class IntelligentSubjectHeadbandComponent {
    readonly review = input.required<Review | PrivateReview>();
    readonly intelligentSubjectAutomations = input<IntelligentSubjectAutomationDto[] | undefined>(undefined);
    readonly currentUser = input<User | undefined>(undefined);

    private _sensitiveSubjectChip = viewChild<ElementRef<HTMLDivElement>>('sensitiveSubjectChip');

    private readonly _router = inject(Router);
    private readonly _matDialog = inject(MatDialog);

    readonly SvgIcon = SvgIcon;
    readonly TooltipPosition = TooltipPosition;

    readonly intelligentSubjectsChipContext = computed<{
        hasDetectedNegativeSubject: boolean;
        withActiveAutomation: boolean;
    }>(() => {
        const review = this.review();
        const intelligentSubjectAutomations = this.intelligentSubjectAutomations() ?? [];
        const intelligentSubjects = review.intelligentSubjects ?? [];

        const hasDetectedNegativeSubject = intelligentSubjects.some(
            (subject) => subject.isDetected && subject.sentiment === ReviewAnalysisSentiment.NEGATIVE
        );
        const withActiveAutomation = intelligentSubjects.some(
            (subject) =>
                subject.isDetected &&
                subject.sentiment === ReviewAnalysisSentiment.NEGATIVE &&
                intelligentSubjectAutomations.some((automation) => automation.active && automation.subject === subject.subject)
        );

        return { hasDetectedNegativeSubject, withActiveAutomation };
    });

    constructor() {
        effect(() => {
            const context = this.intelligentSubjectsChipContext();
            const TOOLTIP_DISPLAY_DELAY_MS = 200;

            const currentUserId = this.currentUser()?._id;
            if (!currentUserId) {
                return;
            }
            const hasAlreadyShownTooltip = this._hasAlreadyShownTooltipForUser(currentUserId, this.review()._id);
            if (hasAlreadyShownTooltip) {
                return;
            }

            if (context.hasDetectedNegativeSubject && !context.withActiveAutomation) {
                // Use setTimeout to ensure the element is rendered in the DOM
                this._markTooltipAsShownForUser(currentUserId, this.review()._id);
                setTimeout(() => {
                    this._triggerHoverEvent();
                }, TOOLTIP_DISPLAY_DELAY_MS);
            }
        });
    }

    goToAutomations(): void {
        const restaurantId = this.review().restaurantId;
        this._matDialog.closeAll();
        this._router.navigate(['restaurants', restaurantId, 'settings', 'automations']);
    }

    onCtaClick(): void {
        this._matDialog.closeAll();
    }

    private _triggerHoverEvent(): void {
        const element = this._sensitiveSubjectChip()?.nativeElement;
        if (element) {
            // Create and dispatch a mouseenter event to trigger the hover/tooltip
            const mouseEnterEvent = new MouseEvent('mouseenter', {
                bubbles: true,
                cancelable: true,
                view: window,
            });
            element.dispatchEvent(mouseEnterEvent);
        }
    }

    private _hasAlreadyShownTooltipForUser(userId: string, reviewId: string): boolean {
        const data = LocalStorage.getItem(LocalStorageKey.REVIEW_SENSITIVE_SUBJECTS_TOOLTIP_SHOWN) || '{}';
        const parsedData: ReviewSensitiveSubjectsTooltipShown = JSON.parse(data);
        return !!parsedData[userId]?.[reviewId];
    }

    private _markTooltipAsShownForUser(userId: string, reviewId: string): void {
        const data = LocalStorage.getItem(LocalStorageKey.REVIEW_SENSITIVE_SUBJECTS_TOOLTIP_SHOWN) || '{}';
        const parsedData: ReviewSensitiveSubjectsTooltipShown = JSON.parse(data);
        if (!parsedData[userId]) {
            parsedData[userId] = {};
        }
        parsedData[userId][reviewId] = true;
        LocalStorage.setItem(LocalStorageKey.REVIEW_SENSITIVE_SUBJECTS_TOOLTIP_SHOWN, JSON.stringify(parsedData));
    }
}
