<ng-container [ngTemplateOutlet]="seoTemplateV2"></ng-container>

<ng-template #seoTemplateV2>
    <div class="flex h-full flex-col gap-y-4">
        <div class="flex h-full flex-col">
            <mat-tab-group
                class="malou-tab-group h-full"
                [disableRipple]="true"
                [selectedIndex]="selectedTabIndex()"
                (selectedIndexChange)="handleTabChange($event)">
                <mat-tab label="{{ 'statistics.seo.tabs.keywords' | translate }}">
                    <ng-container
                        [ngTemplateOutlet]="tab"
                        [ngTemplateOutletContext]="{
                            template: keywordsTemplateV2,
                        }"></ng-container>
                </mat-tab>
                <mat-tab label=" {{ 'statistics.seo.tabs.gmb_visibility' | translate }}">
                    <ng-container
                        [ngTemplateOutlet]="tab"
                        [ngTemplateOutletContext]="{
                            template: gmbTemplateV2,
                        }"></ng-container>
                </mat-tab>
            </mat-tab-group>
        </div>
    </div>
</ng-template>

<ng-template #filtersTemplateV2>
    <div class="flex items-center justify-between sm:flex-col sm:items-center">
        <app-statistics-filters [platformFilterPage]="PlatformFilterPage.SEO" [datePickerType]="datePickerType()"> </app-statistics-filters>

        <app-menu-button-v2 [text]="'statistics.common.download_statistics' | translate" [size]="MenuButtonSize.LARGE">
            <button class="flex !px-5" mat-menu-item [disabled]="isLoading()" (click)="openStatisticsDownload()">
                <span class="malou-text-14--regular text-malou-color-text-2">
                    {{ InsightsTab.SEO_IMPRESSIONS | enumTranslate: 'insights_tab_name' }}
                </span>
            </button>
            <button class="flex !h-12 !px-5" mat-menu-item [disabled]="isLoading()" (click)="downloadInsightsSummary()">
                <span class="malou-text-14--regular text-malou-color-text-2">
                    {{ 'statistics.common.download_statistics_sub_text' | translate }}
                </span>
            </button>
        </app-menu-button-v2>
    </div>
</ng-template>

<ng-template let-template="template" #tab>
    <div class="flex h-full flex-col gap-4 px-8.5 py-4 sm:px-4">
        @if (!screenSizeService.isPhoneScreen) {
            <ng-container [ngTemplateOutlet]="filtersTemplateV2"></ng-container>
        }
        <div class="h-full">
            <div class="flex flex-col gap-4">
                @if (screenSizeService.isPhoneScreen) {
                    <ng-container [ngTemplateOutlet]="filtersTemplateV2"></ng-container>
                }
                <ng-container [ngTemplateOutlet]="template"></ng-container>
            </div>
        </div>
    </div>
</ng-template>

<ng-template #keywordsTemplateV2>
    <div class="flex flex-col gap-10">
        <app-keyword-search-impressions
            (hiddenDatasetIndexesChange)="onHiddenDatasetIndexesChange(InsightsChart.KEYWORD_SEARCH_IMPRESSIONS, $event)"
            (isLoadingEvent)="isKeywordSearchImpressionsLoading.set($event)">
        </app-keyword-search-impressions>
        <app-statistics-keywords-detail
            (tableSortChange)="onTableSortOptionsChange(InsightsChart.KEYWORDS, $event)"
            (isLoadingEvent)="isKeywordsLoading.set($event)"></app-statistics-keywords-detail>
    </div>
</ng-template>

<ng-template #gmbTemplateV2>
    <div class="min-w-0 flex-1">
        <app-gmb-insights
            (isLoadingEvent)="isGmbInsightsLoading.set($event)"
            (viewByChange)="onViewByChange($event.chart, $event.viewBy)"
            (hiddenDatasetIndexesChange)="onHiddenDatasetIndexesChange($event.chart, $event.hiddenDatasetIndexes)">
        </app-gmb-insights>
    </div>
</ng-template>
