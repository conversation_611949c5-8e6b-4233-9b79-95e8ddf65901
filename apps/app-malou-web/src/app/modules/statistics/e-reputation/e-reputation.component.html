<div class="flex h-full flex-col gap-y-4">
    <div class="flex h-full flex-col">
        <mat-tab-group
            class="malou-tab-group h-full"
            [selectedIndex]="selectedIndex()"
            [disableRipple]="true"
            (selectedIndexChange)="handleTabChange($event)">
            <mat-tab label="{{ 'statistics.e_reputation.tabs.reviews' | translate }}">
                <ng-container
                    [ngTemplateOutlet]="scrollableTab"
                    [ngTemplateOutletContext]="{
                        template: reviewsTemplate,
                        tabIndex: EReputationInsightsTabs.REVIEWS,
                    }"></ng-container>
            </mat-tab>
            <mat-tab>
                <ng-template mat-tab-label>
                    <span>{{ 'statistics.e_reputation.tabs.semantic_analysis' | translate }}</span>
                    @if (isNewSemanticAnalysisFeatureEnabled()) {
                        <div class="malou-chip malou-chip--purple !ml-[10px] !h-[20px] !w-[40px] justify-center !p-0">
                            <span>{{ 'common.new' | translate }}</span>
                        </div>
                    }
                </ng-template>
                <ng-container
                    [ngTemplateOutlet]="scrollableTab"
                    [ngTemplateOutletContext]="{
                        template: semanticAnalysisTemplate,
                        tabIndex: EReputationInsightsTabs.SEMANTIC_ANALYSIS,
                    }"></ng-container>
            </mat-tab>
        </mat-tab-group>
    </div>
</div>

<ng-template let-template="template" let-tabIndex="tabIndex" #scrollableTab>
    <div class="flex h-full flex-col gap-4 px-8.5 py-4 sm:px-4">
        @if (!screenSizeService.isPhoneScreen) {
            <ng-container
                [ngTemplateOutlet]="filtersTemplate"
                [ngTemplateOutletContext]="{
                    tabIndex,
                }"></ng-container>
        }
        <div class="h-full" [id]="tabIndex">
            <div class="flex flex-col gap-4">
                @if (screenSizeService.isPhoneScreen) {
                    <ng-container
                        [ngTemplateOutlet]="filtersTemplate"
                        [ngTemplateOutletContext]="{
                            tabIndex,
                        }"></ng-container>
                }
                @if (hasAtLeastOnePlatformConnected()) {
                    <ng-container [ngTemplateOutlet]="template"></ng-container>
                } @else {
                    <ng-container [ngTemplateOutlet]="noPlatformsConnectedTemplate"></ng-container>
                }
            </div>
        </div>
    </div>
</ng-template>

<ng-template let-tabIndex="tabIndex" #filtersTemplate>
    <div class="flex items-end gap-4 sm:flex-col sm:items-center md:px-0">
        <div class="flex-1">
            <app-statistics-filters
                [showPlatformsFilter]="true"
                [platformFilterPage]="PlatformFilterPage.E_REPUTATION"
                [tabIndex]="tabIndex">
            </app-statistics-filters>
        </div>

        <app-menu-button-v2 [text]="'statistics.common.download_statistics' | translate" [size]="MenuButtonSize.LARGE">
            <button class="flex !px-5" mat-menu-item [disabled]="isLoading()" (click)="openStatisticsDownload()">
                <span class="malou-text-14--regular text-malou-color-text-2">
                    {{ InsightsTab.E_REPUTATION | enumTranslate: 'insights_tab_name' }}
                </span>
            </button>
            <button class="flex !h-12 !px-5" mat-menu-item [disabled]="isLoading()" (click)="downloadInsightsSummary()">
                <span class="malou-text-14--regular text-malou-color-text-2">
                    {{ 'statistics.common.download_statistics_sub_text' | translate }}
                </span>
            </button>
        </app-menu-button-v2>
    </div>
</ng-template>

<ng-template #reviewsTemplate>
    <app-statistics-seo-ratings (isLoadingEvent)="isSeoRatingLoading.set($event)"></app-statistics-seo-ratings>
    <app-reviews-kpis (isLoadingEvent)="isReviewsKpisLoading.set($event)"></app-reviews-kpis>
    <div class="flex gap-4 md:flex-col">
        <div class="min-w-0 flex-1">
            <app-reviews-ratings-evolution
                (hiddenDatasetIndexesChange)="onHiddenDatasetIndexesChange(InsightsChart.REVIEW_RATING_EVOLUTION, $event)"
                (viewByChange)="onViewByChange(InsightsChart.REVIEW_RATING_EVOLUTION, $event)"
                (isLoadingEvent)="isReviewsRatingsEvolutionLoading.set($event)">
            </app-reviews-ratings-evolution>
        </div>
        <div class="min-w-0 flex-1">
            <app-reviews-ratings-total
                (isLoadingEvent)="isReviewsRatingsTotalLoading.set($event)"
                (onStatisticsDataViewModeChange)="onViewModeChange(InsightsChart.REVIEW_RATING_TOTAL, $event)"></app-reviews-ratings-total>
        </div>
    </div>
</ng-template>

<ng-template #semanticAnalysisTemplate>
    @if (isNewSemanticAnalysisFeatureEnabledForDate()) {
        <app-semantic-analysis
            (isLoadingEvent)="isReviewAnalysesLoading.set($event)"
            (viewByChange)="onViewByChange(InsightsChart.SEMANTIC_ANALYSIS_TOPICS_EVOLUTION, $event)"></app-semantic-analysis>
    } @else {
        <app-review-analyses
            (hiddenDatasetIndexesChange)="onHiddenDatasetIndexesChange(InsightsChart.REVIEW_ANALYSES_TAG_EVOLUTION, $event)"
            (viewByChange)="onViewByChange(InsightsChart.REVIEW_ANALYSES_TAG_EVOLUTION, $event)"
            (isLoadingEvent)="isReviewAnalysesLoading.set($event)">
        </app-review-analyses>
    }
</ng-template>

<ng-template #noPlatformsConnectedTemplate>
    <div class="flex flex-col items-center px-8.5 py-6">
        <img class="mb-6 h-20 w-20" alt="Taster illustration" [src]="Illustration.Taster | illustrationPathResolver" />
        <span class="malou-text-14--bold mb-2">{{ 'statistics.errors.no_data' | translate }}</span>
        <span class="malou-text-10--regular">{{ 'statistics.errors.platforms_not_connected' | translate }}</span>
    </div>
</ng-template>
