import { KeyV<PERSON>uePipe, LowerCasePipe, NgTemplateOutlet } from '@angular/common';
import { Component, computed, DestroyRef, effect, inject, input, output, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { groupBy } from 'lodash';
import { startWith } from 'rxjs';

import { PartialRecord, ReviewAnalysisSentiment, ReviewAnalysisTag } from '@malou-io/package-utils';

import { SelectComponent } from ':shared/components/select/select.component';
import { NavBarTab, TabNavBarComponent } from ':shared/components/tab-nav-bar/tab-nav-bar.component';
import { ViewBy } from ':shared/enums/view-by.enum';
import {
    ChartDataArray,
    getDaysFromCurrentRange,
    getMonthsFromPeriod,
    getWeeksFromCurrentRange,
    isInDayList,
    isSameDay,
    Month,
    WeekRange,
} from ':shared/helpers';
import { DatesAndPeriod, ReviewWithAnalysis } from ':shared/models';
import { ApplyPurePipe } from ':shared/pipes/apply-fn.pipe';
import { EnumTranslatePipe } from ':shared/pipes/enum-translate.pipe';
import { Illustration, IllustrationPathResolverPipe } from ':shared/pipes/illustration-path-resolver.pipe';

import { TagEvolutionData, TagsEvolutionChartComponent } from './tags-evolution-chart/tags-evolution-chart.component';

const analysisTagsSorted: ReviewAnalysisTag[] = [
    ReviewAnalysisTag.FOOD,
    ReviewAnalysisTag.SERVICE,
    ReviewAnalysisTag.ATMOSPHERE,
    ReviewAnalysisTag.PRICE,
    ReviewAnalysisTag.EXPEDITIOUSNESS,
    ReviewAnalysisTag.HYGIENE,
];

interface SegmentTagEvolution {
    sentiment: ReviewAnalysisSentiment;
    date: Date;
    tag: ReviewAnalysisTag;
}

@Component({
    selector: 'app-tags-evolution',
    templateUrl: './tags-evolution.component.html',
    styleUrls: ['./tags-evolution.component.scss'],
    imports: [
        NgTemplateOutlet,
        SelectComponent,
        FormsModule,
        ReactiveFormsModule,
        TabNavBarComponent,
        TagsEvolutionChartComponent,
        IllustrationPathResolverPipe,
        TranslateModule,
        KeyValuePipe,
        ApplyPurePipe,
        LowerCasePipe,
    ],
    providers: [EnumTranslatePipe],
})
export class TagsEvolutionComponent {
    readonly shouldDetailTagsEvolutionCharts = input<boolean>();
    readonly dates = input<DatesAndPeriod>();
    readonly reviews = input<ReviewWithAnalysis[]>();
    readonly viewBy = input<ViewBy>();

    readonly viewByChange = output<ViewBy>();
    readonly hiddenDatasetIndexesChange = output<number[]>();

    private readonly _destroyRef = inject(DestroyRef);
    private readonly _translate = inject(TranslateService);
    private readonly _enumTranslate = inject(EnumTranslatePipe);

    readonly VIEW_BY_FILTER_VALUES = Object.values(ViewBy);
    readonly Illustration = Illustration;

    readonly viewByControl: FormControl<ViewBy> = new FormControl<ViewBy>(ViewBy.WEEK) as FormControl<ViewBy>;
    readonly chartViewBy = signal<ViewBy>(ViewBy.WEEK);

    readonly processedReviews = computed(() => {
        const reviewsValue = this.reviews();
        return reviewsValue ? this._getReviewWithAnalysisWithPositiveAndNegativeSentiments(reviewsValue) : [];
    });
    readonly segmentsByTag = computed<Record<string, SegmentTagEvolution[]>>(() => {
        const processedReviews = this.processedReviews();
        if (!processedReviews) {
            return {};
        }
        return this._getSegmentsByTag(processedReviews);
    });
    readonly tagsWithCount = computed(() => {
        const segmentsByTag: Record<string, SegmentTagEvolution[]> = this.segmentsByTag();
        const tagsCount: PartialRecord<ReviewAnalysisTag, number> = {};
        for (const [tag, segments] of Object.entries(segmentsByTag)) {
            tagsCount[tag] = segments.length;
        }
        return tagsCount;
    });
    readonly allTags = computed<string[]>(() => Object.keys(this.tagsWithCount()));
    readonly tabs = computed<NavBarTab[]>(() => {
        const tagsWithCount = this.tagsWithCount();
        return Object.entries(tagsWithCount)
            .map(([key, value]: [ReviewAnalysisTag, number]) => ({
                title: this._translate.instant(`reviews.review_analyses.${key}`),
                subtitle: `(${value})`,
                data: key,
            }))
            .sort((a, b) => analysisTagsSorted.indexOf(a.data) - analysisTagsSorted.indexOf(b.data));
    });
    readonly hasData = computed(() => this.tabs().length > 0);
    readonly currentTag = signal<string | null>(null);
    readonly range = computed<Date[] | WeekRange[] | Month[]>(() => {
        const dates = this.dates();
        const viewBy = this.chartViewBy();
        if (!dates?.startDate || !dates?.endDate) {
            return [];
        }

        switch (viewBy) {
            case ViewBy.DAY:
                return getDaysFromCurrentRange(dates.startDate, dates.endDate);
            case ViewBy.WEEK:
                return getWeeksFromCurrentRange(dates.startDate, dates.endDate);
            case ViewBy.MONTH:
                return getMonthsFromPeriod(dates.startDate, dates.endDate);
            default:
                return [];
        }
    });
    readonly labels = computed<Date[]>(() => {
        const range = this.range();
        const viewBy = this.chartViewBy();
        switch (viewBy) {
            case ViewBy.DAY:
                return range as Date[];
            case ViewBy.WEEK:
                return (range as WeekRange[]).map((week) => week.start);
            case ViewBy.MONTH:
                return (range as Month[]).map((month) => month.start);
            default:
                return [];
        }
    });
    readonly tagEvolutionData = computed<Record<string, TagEvolutionData>>(() => {
        const dates = this.dates();
        const viewBy = this.chartViewBy();
        const allTags = this.allTags();

        if (!dates?.startDate || !dates?.endDate) {
            return {};
        }
        const segmentsByTag = this.segmentsByTag();
        const range = this.range();
        switch (viewBy) {
            case ViewBy.DAY: {
                return this._computeDailyData(segmentsByTag, range as Date[], allTags);
            }
            case ViewBy.WEEK: {
                return this._computeWeeklyData(segmentsByTag, range as WeekRange[], allTags);
            }
            case ViewBy.MONTH: {
                return this._computeMonthlyData(segmentsByTag, range as Month[], allTags);
            }
            default:
                return {};
        }
    });
    readonly tagEvolutionDataForCurrentTag = computed<TagEvolutionData>(() => {
        const currentTag = this.currentTag();
        const tagEvolutionData = this.tagEvolutionData();
        if (currentTag && tagEvolutionData[currentTag]) {
            return tagEvolutionData[currentTag];
        }
        return {
            [ReviewAnalysisSentiment.POSITIVE]: [],
            [ReviewAnalysisSentiment.NEGATIVE]: [],
        };
    });

    constructor() {
        // Watch for viewBy control changes
        this.viewByControl.valueChanges.pipe(startWith(ViewBy.WEEK), takeUntilDestroyed(this._destroyRef)).subscribe((viewBy: ViewBy) => {
            this.chartViewBy.set(viewBy);
            this.viewByChange.emit(viewBy);
        });

        // Watch for input changes using effect
        this._watchInputChanges();
        effect(() => {
            const tabsData = this.tabs()[0]?.data || null;
            this.currentTag.set(tabsData);
        });
    }

    private _watchInputChanges(): void {
        effect(() => {
            const viewByValue = this.viewBy();
            if (viewByValue) {
                this.chartViewBy.set(viewByValue);
                this.viewByControl.setValue(viewByValue);
            }
        });
    }

    onTabChange(tab: NavBarTab): void {
        this.currentTag.set(tab.data);
    }

    viewByDisplayWith = (option: ViewBy): string => this._enumTranslate.transform(option, 'view_by');

    getChartTitleByKey = (key: string): string => this._translate.instant(`reviews.review_analyses.${key}`);

    private _getReviewWithAnalysisWithPositiveAndNegativeSentiments(reviews: ReviewWithAnalysis[]): ReviewWithAnalysis[] {
        return reviews.map((review) => ({
            ...review,
            semanticAnalysis: {
                ...review.semanticAnalysis,
                segmentAnalyses: review?.semanticAnalysis?.segmentAnalyses?.filter((segment) =>
                    [ReviewAnalysisSentiment.POSITIVE, ReviewAnalysisSentiment.NEGATIVE].includes(segment.sentiment)
                ),
            },
            semanticAnalysisSegments: review?.semanticAnalysisSegments?.filter((segment) =>
                [ReviewAnalysisSentiment.POSITIVE, ReviewAnalysisSentiment.NEGATIVE].includes(segment.sentiment)
            ),
        })) as ReviewWithAnalysis[];
    }

    private _getSegmentsByTag(reviews: ReviewWithAnalysis[]): Record<string, SegmentTagEvolution[]> {
        const segmentsWithReview = reviews.flatMap((review) => {
            const oldSegments = review.semanticAnalysis?.segmentAnalyses ?? [];
            const newSegments = review.semanticAnalysisSegments ?? [];
            return [
                ...oldSegments.map((segment) => ({
                    sentiment: segment.sentiment,
                    date: new Date(review.socialCreatedAt),
                    tag: segment.tag,
                })),
                ...newSegments.map((segment) => ({
                    sentiment: segment.sentiment,
                    date: new Date(review.socialCreatedAt),
                    tag: segment.category,
                })),
            ];
        });
        return groupBy(segmentsWithReview, 'tag');
    }

    private _computeDailyData(
        segmentsByTag: Record<string, SegmentTagEvolution[]>,
        days: Date[],
        allTags: string[]
    ): Record<string, TagEvolutionData> {
        const dataByTag: Record<string, TagEvolutionData> = {};

        for (const tag of allTags) {
            const segmentsWithReview = segmentsByTag[tag];
            const chartDataNegative: ChartDataArray = [];
            const chartDataPositive: ChartDataArray = [];
            for (const day of days) {
                const daySegments = segmentsWithReview.filter((e) => isSameDay(new Date(e.date), day));
                chartDataNegative.push(daySegments.filter((segment) => segment.sentiment === ReviewAnalysisSentiment.NEGATIVE).length);
                chartDataPositive.push(daySegments.filter((segment) => segment.sentiment === ReviewAnalysisSentiment.POSITIVE).length);
            }
            dataByTag[tag] = {
                [ReviewAnalysisSentiment.NEGATIVE]: chartDataNegative,
                [ReviewAnalysisSentiment.POSITIVE]: chartDataPositive,
            };
        }
        return dataByTag;
    }

    private _computeWeeklyData(
        segmentsByTag: Record<string, SegmentTagEvolution[]>,
        weeks: WeekRange[],
        allTags: string[]
    ): Record<string, TagEvolutionData> {
        const dataByTag: Record<string, TagEvolutionData> = {};
        for (const tag of allTags) {
            const segmentsWithReview = segmentsByTag[tag];
            const chartDataNegative: ChartDataArray = [];
            const chartDataPositive: ChartDataArray = [];
            for (const week of weeks) {
                const weekSegments = segmentsWithReview.filter((e) => isInDayList(new Date(e.date), week.days));
                chartDataNegative.push(weekSegments.filter((segment) => segment.sentiment === ReviewAnalysisSentiment.NEGATIVE).length);
                chartDataPositive.push(weekSegments.filter((segment) => segment.sentiment === ReviewAnalysisSentiment.POSITIVE).length);
            }
            dataByTag[tag] = {
                [ReviewAnalysisSentiment.NEGATIVE]: chartDataNegative,
                [ReviewAnalysisSentiment.POSITIVE]: chartDataPositive,
            };
        }
        return dataByTag;
    }

    private _computeMonthlyData(
        segmentsByTag: Record<string, SegmentTagEvolution[]>,
        months: Month[],
        allTags: string[]
    ): Record<string, TagEvolutionData> {
        const dataByTag: Record<string, TagEvolutionData> = {};

        for (const tag of allTags) {
            const segmentsWithReview = segmentsByTag[tag];
            const chartDataNegative: ChartDataArray = [];
            const chartDataPositive: ChartDataArray = [];
            for (const month of months) {
                const monthSegments = segmentsWithReview.filter((e) => isInDayList(new Date(e.date), month.days));
                chartDataNegative.push(monthSegments.filter((segment) => segment.sentiment === ReviewAnalysisSentiment.NEGATIVE).length);
                chartDataPositive.push(monthSegments.filter((segment) => segment.sentiment === ReviewAnalysisSentiment.POSITIVE).length);
            }
            dataByTag[tag] = {
                [ReviewAnalysisSentiment.NEGATIVE]: chartDataNegative,
                [ReviewAnalysisSentiment.POSITIVE]: chartDataPositive,
            };
        }
        return dataByTag;
    }
}
