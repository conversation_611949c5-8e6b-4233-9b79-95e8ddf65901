<div class="flex h-full flex-col gap-y-6 py-4" #topOfComponent>
    @if (!screenSizeService.isPhoneScreen) {
        <ng-container [ngTemplateOutlet]="filtersTemplate"></ng-container>
    }

    <div class="flex h-full flex-col gap-y-4 px-8.5">
        @if (screenSizeService.isPhoneScreen) {
            <ng-container [ngTemplateOutlet]="filtersTemplate"></ng-container>
        }
        @if (platformKeys().length !== 0) {
            <ng-container [ngTemplateOutlet]="performanceImprovementsTemplate"></ng-container>
        } @else {
            <div class="flex flex-col items-center py-6">
                <img class="mb-6 h-20 w-20" alt="Taster illustration" [lazyLoad]="Illustration.Taster | illustrationPathResolver" />
                <span class="malou-text-14--bold mb-2">{{ 'statistics.errors.no_data' | translate }}</span>
                <span class="malou-text-10--regular">{{ 'statistics.errors.platforms_not_connected' | translate }}</span>
            </div>
        }
    </div>
</div>

<ng-template #filtersTemplate>
    <div class="flex items-end gap-4 px-8.5 sm:flex-col sm:items-center md:px-0">
        <div class="flex-1">
            <app-statistics-filters [showPlatformsFilter]="true" [platformFilterPage]="PlatformFilterPage.SOCIAL_NETWORKS">
            </app-statistics-filters>
        </div>

        <app-menu-button-v2 [text]="'statistics.common.download_statistics' | translate" [size]="MenuButtonSize.LARGE">
            <button class="flex !px-5" mat-menu-item [disabled]="isLoading()" (click)="openStatisticsDownload()">
                <span class="malou-text-14--regular text-malou-color-text-2">
                    {{ InsightsTab.SOCIAL_NETWORKS | enumTranslate: 'insights_tab_name' }}
                </span>
            </button>
            <button class="flex !h-12 !px-5" mat-menu-item [disabled]="isLoading()" (click)="downloadInsightsSummary()">
                <span class="malou-text-14--regular text-malou-color-text-2">
                    {{ 'statistics.common.download_statistics_sub_text' | translate }}
                </span>
            </button>
        </app-menu-button-v2>
    </div>
</ng-template>

<ng-template #performanceImprovementsTemplate>
    <div class="flex gap-6 md:flex-col">
        <div class="min-w-0 flex-1">
            <app-community-v2
                (hiddenDatasetIndexesChange)="onHiddenDatasetIndexesChange(InsightsChart.COMMUNITY, $event)"
                (viewByChange)="onViewByChange(InsightsChart.COMMUNITY, $event)"
                (isLoadingEvent)="isCommunityLoading.set($event)">
            </app-community-v2>
        </div>
        <div class="min-w-0 flex-1">
            <ng-container [ngTemplateOutlet]="engagementTemplate"></ng-container>
        </div>
    </div>
    <div class="mt-5">
        <ng-container *ngTemplateOutlet="postsAndStories"></ng-container>
    </div>
</ng-template>

<ng-template #postsAndStories>
    @if (isPostInsightsV2Enabled()) {
        <app-post-insights-table-v2
            (tableSortOptionsChange)="onTableSortOptionsChange($event)"
            (isLoadingEvent)="isPostInsightsTableLoading.set($event)">
        </app-post-insights-table-v2>
    } @else {
        <app-posts-insights-table
            [followers]="followersData()"
            (tableSortOptionsChange)="onTableSortOptionsChange($event)"
            (isLoadingEvent)="isPostInsightsTableLoading.set($event)">
        </app-posts-insights-table>
    }
</ng-template>

<ng-template #engagementTemplate>
    @if (isPostInsightsV2Enabled()) {
        <app-engagement-v3
            (hiddenDatasetIndexesChange)="onHiddenDatasetIndexesChange(InsightsChart.ENGAGEMENT, $event)"
            (viewByChange)="onViewByChange(InsightsChart.ENGAGEMENT, $event)"
            (isLoadingEvent)="isEngagementLoading.set($event)">
        </app-engagement-v3>
    } @else {
        <app-engagement-v2
            (hiddenDatasetIndexesChange)="onHiddenDatasetIndexesChange(InsightsChart.ENGAGEMENT, $event)"
            (viewByChange)="onViewByChange(InsightsChart.ENGAGEMENT, $event)"
            (isLoadingEvent)="isEngagementLoading.set($event)">
        </app-engagement-v2>
    }
</ng-template>
