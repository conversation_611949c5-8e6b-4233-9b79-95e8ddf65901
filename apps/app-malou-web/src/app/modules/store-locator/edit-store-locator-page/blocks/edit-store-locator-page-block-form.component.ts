import { NgClass, NgTemplateOutlet } from '@angular/common';
import {
    ChangeDetectionStrategy,
    Component,
    computed,
    DestroyRef,
    inject,
    input,
    signal,
    TemplateRef,
    WritableSignal,
} from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { FormControl } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { TranslateModule } from '@ngx-translate/core';
import { isEqual } from 'lodash';

import {
    GenerateStoreLocatorContentType,
    MimeType,
    StoreLocatorLanguage,
    StoreLocatorRestaurantPageElementIds,
} from '@malou-io/package-utils';

import { ExperimentationService } from ':core/services/experimentation.service';
import { ScreenSizeService } from ':core/services/screen-size.service';
import { StoreLocatorInputType } from ':modules/store-locator/edit-store-locator-page/blocks/edit-store-locator-page-blocks.interface';
import { EditStoreLocatorPageOrganizationRestaurantsSelectorComponent } from ':modules/store-locator/edit-store-locator-page/blocks/shared/organization-restaurants-selector/organization-restaurants-selector.component';
import { EditStoreLocatorPageLanguageSelectorComponent } from ':modules/store-locator/edit-store-locator-page/blocks/shared/store-locator-language-selector/store-locator-language-selector.component';
import { ConfirmContentDuplicationModalComponent } from ':modules/store-locator/edit-store-locator-page/duplication/confirm-content-duplication-modal/confirm-content-duplication-modal.component';
import { ConfirmContentDuplicationModalInputData } from ':modules/store-locator/edit-store-locator-page/duplication/confirm-content-duplication-modal/confirm-content-duplication-modal.interface';
import { EditStoreLocatorPageContext } from ':modules/store-locator/edit-store-locator-page/edit-store-locator-page.context';
import { StoreLocatorPageBlockType } from ':modules/store-locator/edit-store-locator-page/edit-store-locator-page.interface';
import { StoreLocatorOrganizationRestaurant } from ':modules/store-locator/models/store-locator-organization-restaurant';
import { CssKeys, EXTRA_COLORS } from ':modules/store-locator/shared/edit-store-locator/edit-store-locator.interface';
import { parseConfigurationStyleClassesToCssStyle } from ':modules/store-locator/shared/edit-store-locator/utils/edit-store-locator-page.utils';
import { StoreLocatorContext } from ':modules/store-locator/store-locator.context';
import { DefaultErrorMessage } from ':shared/components/input-text/input-text.component';
import { InputTextTheme } from ':shared/components/input-text/input-text.interface';
import { InputTextAreaTheme } from ':shared/components/text-area/text-area.interface';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { CustomDialogService, DialogScreenSize } from ':shared/services/custom-dialog.service';

enum BlockFromTabs {
    CONTENT,
    STYLE,
}
@Component({
    selector: 'app-edit-store-locator-page-block-form-wrapper',
    templateUrl: './edit-store-locator-page-block-form.component.html',
    styleUrls: ['./edit-store-locator-page-block-form.component.scss'],
    imports: [
        MatButtonModule,
        MatTabsModule,
        TranslateModule,
        NgTemplateOutlet,
        EditStoreLocatorPageOrganizationRestaurantsSelectorComponent,
        MatIconModule,
        NgClass,
        EditStoreLocatorPageLanguageSelectorComponent,
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StoreLocatorPageBlockFormComponent {
    readonly contentFormTemplate = input<TemplateRef<any>>();
    readonly styleFormTemplate = input<TemplateRef<any>>();
    readonly duplicationText = input<string>();

    readonly destroyRef = inject(DestroyRef);
    readonly editStoreLocatorPageContext = inject(EditStoreLocatorPageContext);
    readonly storeLocatorContext = inject(StoreLocatorContext);
    private readonly _experimentationService = inject(ExperimentationService);
    private readonly _customDialogService = inject(CustomDialogService);
    private readonly _screenService = inject(ScreenSizeService);

    readonly isStoreLocatorContentDuplicationEnabled = toSignal(
        this._experimentationService.isFeatureEnabled$('release-store-locator-content-duplication'),
        { initialValue: false }
    );

    readonly selectedRestaurantPagesLanguage = computed(() => this.editStoreLocatorPageContext.selectedRestaurantPagesLanguage());
    readonly restaurantPagesLanguages = computed(() => this.editStoreLocatorPageContext.restaurantPagesLanguages());

    readonly organizationRestaurants = computed(() => this.editStoreLocatorPageContext.organizationRestaurants());
    readonly currentEditingRestaurant = computed(() => this.editStoreLocatorPageContext.currentEditingRestaurant());
    readonly organizationStyleConfiguration = computed(() => this.editStoreLocatorPageContext.organizationStyleConfiguration());
    readonly colorOptions = computed(() => [
        ...(this.editStoreLocatorPageContext.organizationStyleConfiguration()?.colors.map((color) => color.value) ?? []),
        ...EXTRA_COLORS,
    ]);
    readonly currentEditingRestaurantId = computed(() => this.currentEditingRestaurant()?.id || '');

    readonly selectedTabIndex: WritableSignal<number> = signal(BlockFromTabs.CONTENT);
    readonly selectedRestaurant = this.editStoreLocatorPageContext.currentEditingRestaurant;

    readonly InputTextTheme = InputTextTheme;
    readonly InputTextAreaTheme = InputTextAreaTheme;
    readonly DefaultErrorMessage = DefaultErrorMessage;
    readonly GenerateStoreLocatorContentType = GenerateStoreLocatorContentType;
    readonly SvgIcon = SvgIcon;
    readonly MimeType = MimeType;
    readonly DialogScreenSize = DialogScreenSize;

    restaurantsFilterControl: FormControl<StoreLocatorOrganizationRestaurant> = new FormControl<StoreLocatorOrganizationRestaurant | null>(
        this.editStoreLocatorPageContext.currentEditingRestaurant()
    ) as FormControl<StoreLocatorOrganizationRestaurant>;

    restaurantPagesLanguagesControl: FormControl<StoreLocatorLanguage> = new FormControl<StoreLocatorLanguage>(
        this.editStoreLocatorPageContext.selectedRestaurantPagesLanguage()
    ) as FormControl<StoreLocatorLanguage>;

    readonly storeLocatorPageState = computed(() => this.editStoreLocatorPageContext.selectedRestaurantStorePageState());

    readonly isBlockInError = computed(() => this.editStoreLocatorPageContext.isBlockInError().isError);
    readonly shouldDisableModal = computed(() => this.editStoreLocatorPageContext.shouldDisableModal());

    constructor() {
        this.restaurantsFilterControl.valueChanges.subscribe((restaurant) => {
            if (restaurant) {
                this.editStoreLocatorPageContext.updateCurrentEditingRestaurant(restaurant);
            }
        });
        this.restaurantPagesLanguagesControl.valueChanges.subscribe((language) => {
            if (language) {
                this.editStoreLocatorPageContext.updateSelectedRestaurantPagesLanguage(language);
            }
        });
    }

    handleTabChange(event: number): void {
        this.selectedTabIndex.set(event);
    }

    handleDuplicateBlockUpdatesToAll(): void {
        const isBlockInError = this.editStoreLocatorPageContext.isBlockInError();
        if (!isBlockInError.isError) {
            this._customDialogService
                .open<ConfirmContentDuplicationModalComponent, ConfirmContentDuplicationModalInputData>(
                    ConfirmContentDuplicationModalComponent,
                    {
                        panelClass: this._screenService.isPhoneScreen ? 'malou-dialog-panel--full' : 'malou-dialog-panel--fit-content',
                        data: {
                            isViaAi: false,
                            language: this.selectedRestaurantPagesLanguage(),
                        },
                    },
                    { animateScreenSize: DialogScreenSize.ALL }
                )
                .afterClosed()
                .subscribe({
                    next: () => {
                        // this.editStoreLocatorPageContext.duplicateBlockDataToAll();
                    },
                });
        }
    }

    protected getStyleMap(elementIds: StoreLocatorRestaurantPageElementIds[]): Record<string, Record<CssKeys, string>> {
        const organizationStyleConfiguration = this.organizationStyleConfiguration();
        if (!organizationStyleConfiguration) {
            return {};
        }

        const styleMap: Record<string, Record<CssKeys, string>> = {};

        elementIds.forEach((key) => {
            styleMap[key] = parseConfigurationStyleClassesToCssStyle(
                organizationStyleConfiguration.getStorePageElementStyle(key) as string[],
                {
                    colors: organizationStyleConfiguration.colors,
                    fonts: organizationStyleConfiguration.fonts,
                }
            );
        });

        return styleMap;
    }

    protected trackEditContentActivity({ block, element }: { block: StoreLocatorPageBlockType; element: StoreLocatorInputType }): void {
        this.editStoreLocatorPageContext.editStoreLocatorActivityTracker()?.trackEditContentActivity({
            restaurantId: this.currentEditingRestaurantId(),
            block,
            element,
        });
    }

    protected trackEditStyleActivity({ block, changes }: { block: StoreLocatorPageBlockType; changes: Record<string, string[]> }): void {
        const organizationStyleConfiguration = this.organizationStyleConfiguration();
        if (organizationStyleConfiguration) {
            Object.keys(changes).forEach((elementId: StoreLocatorRestaurantPageElementIds) => {
                if (this.isElementStyleEditTracked(elementId)) {
                    return;
                }
                if (
                    !isEqual(
                        organizationStyleConfiguration.getStorePageElementStyle(elementId as StoreLocatorRestaurantPageElementIds),
                        changes[elementId]
                    )
                ) {
                    this.editStoreLocatorPageContext.editStoreLocatorActivityTracker()?.trackEditStyleActivity({
                        block,
                        elementId,
                    });
                }
            });
        }
    }

    protected isElementTrackedForRestaurant(element: StoreLocatorInputType, block: StoreLocatorPageBlockType): boolean {
        const restaurantId = this.currentEditingRestaurantId();

        return (
            this.editStoreLocatorPageContext
                .editStoreLocatorActivityTracker()
                ?.isElementContentEditTrackedForRestaurant({ restaurantId, block, element }) || false
        );
    }

    protected isElementStyleEditTracked(elementId: StoreLocatorRestaurantPageElementIds): boolean {
        return this.editStoreLocatorPageContext.editStoreLocatorActivityTracker()?.isElementStyleEditTracked(elementId) || false;
    }
}
