import { HttpClientTestingModule } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { provideMockStore } from '@ngrx/store/testing';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { vi } from 'vitest';

import { MediaType, PlatformKey, PostPublicationStatus, PostType, TiktokPrivacyStatus } from '@malou-io/package-utils';

import { EditionMedia } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/components/social-post-content-form/social-post-medias/edition-media.interface';
import { UpsertSocialPostService } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/upsert-social-post.service';
import { IUpsertSocialPost } from ':modules/posts-v2/social-posts/models/upsert-social-post';
import { SubmitPublicationStatus } from ':shared/components/posts-v2/posts-v2.interface';

describe('UpsertSocialPostService', () => {
    let service: UpsertSocialPostService;
    let translateService: TranslateService;

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [HttpClientTestingModule, TranslateModule.forRoot()],
            providers: [UpsertSocialPostService, provideMockStore({})],
        });
        service = TestBed.inject(UpsertSocialPostService);
        translateService = TestBed.inject(TranslateService);

        // Mock translate service
        vi.spyOn(translateService, 'instant').mockImplementation((key) => key);
    });

    describe('getPostErrors', () => {
        let mockPost: IUpsertSocialPost;

        beforeEach(() => {
            // Setup default valid post
            mockPost = {
                id: '123',
                text: 'Valid post text',
                title: 'Valid title',
                published: PostPublicationStatus.DRAFT,
                isPublishing: false,
                platformKeys: [PlatformKey.INSTAGRAM],
                plannedPublicationDate: new Date(Date.now() + 24 * 60 * 60 * 1000), // tomorrow
                hashtags: { selected: [], suggested: [] },
                medias: [{ uploadedMedia: { type: MediaType.PHOTO } as EditionMedia }],
                postType: PostType.IMAGE,
                tiktokOptions: {
                    contentDisclosureSettings: {
                        isActivated: false,
                        yourBrand: false,
                        brandedContent: false,
                    },
                    privacyStatus: TiktokPrivacyStatus.PUBLIC_TO_EVERYONE,
                    interactionAbility: {
                        comment: false,
                        duet: false,
                        stitch: false,
                    },
                    autoAddMusic: false,
                },
                location: null,
                callToAction: undefined,
                feedbacks: null,
                error: undefined,
                socialLink: undefined,
                socialCreatedAt: undefined,
                duplicateToPlatforms: [],
                author: undefined,
                userTagsList: [],
                bindingId: undefined,
                reelThumbnailFromMedia: undefined,
                reelThumbnailFromFrame: undefined,
                instagramCollaboratorsUsernames: [],
            };
        });

        it('should return empty array for valid post', () => {
            const errors = service.getPostErrors(mockPost, SubmitPublicationStatus.SCHEDULE, false);
            expect(errors.length).toBe(0);
        });

        it('should return error when text is empty', () => {
            mockPost.text = '';
            const errors = service.getPostErrors(mockPost, SubmitPublicationStatus.SCHEDULE, false);
            expect(errors).toContain('social_posts.upsert_social_post_modal.errors.add_text');
        });

        it('should return error when no platform is selected', () => {
            mockPost.platformKeys = [];
            const errors = service.getPostErrors(mockPost, SubmitPublicationStatus.SCHEDULE, false);
            expect(errors).toContain('social_posts.upsert_social_post_modal.errors.no_platform_selected');
        });

        it('should return error when no media is selected', () => {
            mockPost.medias = [];
            const errors = service.getPostErrors(mockPost, SubmitPublicationStatus.SCHEDULE, false);
            expect(errors).toContain('social_posts.upsert_social_post_modal.errors.no_media_selected');
        });

        it('should return error when planned date is in the past', () => {
            mockPost.plannedPublicationDate = new Date(Date.now() - 24 * 60 * 60 * 1000); // yesterday
            const errors = service.getPostErrors(mockPost, SubmitPublicationStatus.SCHEDULE, false);
            expect(errors).toContain('social_posts.upsert_social_post_modal.errors.planned_publication_date_in_past');
        });

        it('should return error when too many hashtags are selected', () => {
            mockPost.hashtags.selected = Array(31).fill({ text: '#hashtag' }); // More than max allowed
            const errors = service.getPostErrors(mockPost, SubmitPublicationStatus.SCHEDULE, false);
            expect(errors).toContain('social_posts.upsert_social_post_modal.errors.too_many_hashtags_error');
        });

        it('should return error when too many hashtags are in description', () => {
            mockPost.text = Array.from({ length: 31 }, (_, i) => `#hashtag${i}`).join(' '); // More than max allowed
            const errors = service.getPostErrors(mockPost, SubmitPublicationStatus.SCHEDULE, false);
            expect(errors).toContain('social_posts.upsert_social_post_modal.errors.too_many_hashtags_error');
        });

        it('should return error for carousel videos with Facebook', () => {
            mockPost.medias = [
                { uploadedMedia: { type: MediaType.VIDEO } as EditionMedia },
                { uploadedMedia: { type: MediaType.VIDEO } as EditionMedia },
            ];
            mockPost.platformKeys = [PlatformKey.FACEBOOK];
            const errors = service.getPostErrors(mockPost, SubmitPublicationStatus.SCHEDULE, false);
            expect(errors).toContain('social_posts.upsert_social_post_modal.errors.carousel_facebook_videos_error');
        });

        it('should return error for TikTok content disclosure issues', () => {
            mockPost.platformKeys = [PlatformKey.TIKTOK];
            mockPost.tiktokOptions.contentDisclosureSettings.isActivated = true;
            const errors = service.getPostErrors(mockPost, SubmitPublicationStatus.SCHEDULE, false);
            expect(errors).toContain('social_posts.upsert_social_post_modal.errors.tiktok_content_disclosure_error');
        });

        it('should return Mapstr specific errors when Mapstr is selected', () => {
            mockPost.platformKeys = [PlatformKey.MAPSTR];
            mockPost.title = '';
            const errors = service.getPostErrors(mockPost, SubmitPublicationStatus.SCHEDULE, false);
            expect(errors).toContain('social_posts.upsert_social_post_modal.errors.add_title');
        });

        it('should return uploading media message when media are uploading', () => {
            const errors = service.getPostErrors(mockPost, SubmitPublicationStatus.SCHEDULE, true);
            expect(errors).toContain('social_posts.upsert_social_post_modal.errors.media_uploading');
        });

        it('should not return uploading media message if there is another error', () => {
            mockPost.text = '';
            const errors = service.getPostErrors(mockPost, SubmitPublicationStatus.SCHEDULE, true);
            expect(errors).toContain('social_posts.upsert_social_post_modal.errors.add_text');
            expect(errors).not.toContain('social_posts.upsert_social_post_modal.errors.media_uploading');
        });
    });
});
