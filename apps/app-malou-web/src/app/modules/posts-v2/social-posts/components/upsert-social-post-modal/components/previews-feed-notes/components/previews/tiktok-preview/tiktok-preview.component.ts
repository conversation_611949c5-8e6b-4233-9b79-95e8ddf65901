import { <PERSON><PERSON><PERSON>, NgStyle } from '@angular/common';
import {
    AfterViewInit,
    ChangeDetectionStrategy,
    Component,
    computed,
    effect,
    ElementRef,
    inject,
    input,
    signal,
    viewChild,
} from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';

import { MediaCropComputer, MediaType, PublicationType } from '@malou-io/package-utils';

import { ImageViewerComponent } from ':modules/posts-v2/social-posts/components/image-viewer/image-viewer.component';
import { PreviewsService } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/components/previews-feed-notes/components/previews/previews.service';
import { EditionMedia } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/components/social-post-content-form/social-post-medias/edition-media.interface';
import { UpsertSocialPostContext } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/contexts/upsert-social-post.context';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { ApplyPurePipe } from ':shared/pipes/apply-fn.pipe';
import { HtmlTagPipe } from ':shared/pipes/html-tag.pipe';
import { ImagePathResolverPipe } from ':shared/pipes/image-path-resolver.pipe';

@Component({
    selector: 'app-tiktok-preview',
    templateUrl: './tiktok-preview.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [NgClass, NgStyle, MatIconModule, TranslateModule, ImageViewerComponent, ApplyPurePipe, HtmlTagPipe, ImagePathResolverPipe],
})
export class TiktokPreviewComponent implements AfterViewInit {
    readonly title = input<string>('');
    readonly text = input<string>('');
    readonly medias = input<EditionMedia[]>([]);
    readonly username = input<string>('');
    readonly profilePicture = input<string | undefined>();
    readonly hashtags = input<string[]>([]);

    readonly slider = viewChild<HTMLDivElement>('slider');
    readonly sliderContainer = viewChild.required<ElementRef<HTMLDivElement>>('sliderContainer');

    private readonly _upsertSocialPostContext = inject(UpsertSocialPostContext);
    private readonly _previewsService = inject(PreviewsService);

    readonly usernameWithoutAt = computed(() => this.username().replace('@', ''));

    readonly currentMediaIndex = signal(0);

    readonly aspectRatioStyle = computed<{ aspectRatio: number }>(() => {
        const medias = this.medias();
        const media = medias[medias.length - 1];

        if (!media) {
            return { aspectRatio: 1 };
        }

        const computedAspectRatio = MediaCropComputer.computePreferredAspectRatioFor({
            publicationType: PublicationType.POST,
            originalAspectRatio: media.aspectRatio,
        });
        return { aspectRatio: computedAspectRatio };
    });

    readonly hashtagsText = computed(() => this.hashtags().join(' '));

    readonly isCarousel = computed(() => this.medias().length > 1);

    readonly userTagsList = this._upsertSocialPostContext.upsertSocialPostState.post.userTagsList;
    readonly userTags = computed(() => {
        const userTagsList = this.userTagsList();
        const currentMediaIndex = this.currentMediaIndex();
        return userTagsList[currentMediaIndex] ?? [];
    });

    readonly SvgIcon = SvgIcon;
    readonly MediaType = MediaType;
    readonly IMG_ID_PREFIX = 'tiktok-preview-img-';

    constructor() {
        effect(() => {
            const medias = this.medias();
            this.currentMediaIndex.update((index) => Math.max(Math.min(index, medias.length - 1), 0));
            this._scrollToCurrentMedia();
        });

        this._previewsService.mediaIdSelected$.subscribe((mediaId) => {
            this.currentMediaIndex.set(this.medias().findIndex((media) => media.id === mediaId) ?? 0);
        });
    }

    ngAfterViewInit(): void {
        this._scrollToCurrentMedia('instant');
    }

    goToMedia(index: number): void {
        this.currentMediaIndex.set(index);
        this._scrollToCurrentMedia();
    }

    formatText(text: string): string {
        const textWithLineBreaks = text.replace(/\n/g, ' <br /> ');
        const textWords = textWithLineBreaks.split(' ');

        const instagramMentionsAndHashtagsColor = '#3769A9';
        const textWithAndMentions = textWords
            .map((word) =>
                word.startsWith('@') && word.length > 1 ? `<span style="color: ${instagramMentionsAndHashtagsColor}">${word}</span>` : word
            )
            .join(' ');

        return textWithAndMentions;
    }

    private _scrollToCurrentMedia(behavior?: ScrollBehavior): void {
        const currentMediaIndex = this.currentMediaIndex();
        const mediaElement = document.getElementById(`${this.IMG_ID_PREFIX}${currentMediaIndex}`);
        if (mediaElement) {
            mediaElement.scrollIntoView({ behavior: behavior ?? 'smooth', block: 'nearest', inline: 'center' });
        }
    }
}
