<div class="modal-container max-h-[90vh]">
    <div class="malou-dialog malou-dialog-mobile h-full max-h-[90vh]">
        <div class="malou-dialog__header items-start gap-10">
            <div class="flex flex-col gap-1">
                <div class="malou-text-18--bold text-malou-color-text-1">
                    {{ 'social_posts.upsert_social_post_modal.content_form.alternative_texts.title' | pluralTranslate: photoMediasCount() }}
                </div>
                <div class="malou-text-12--regular text-malou-color-text-2">
                    <div>{{ 'social_posts.upsert_social_post_modal.content_form.alternative_texts.subtitle_1' | translate }}</div>
                    <div>{{ 'social_posts.upsert_social_post_modal.content_form.alternative_texts.subtitle_2' | translate }}</div>
                </div>
            </div>
            <button
                class="malou-btn-icon !rounded-full !bg-malou-color-background-dark"
                data-testid="close-upsert-social-post-btn"
                mat-icon-button
                (click)="cancel()">
                <mat-icon color="primary" [svgIcon]="SvgIcon.CROSS"></mat-icon>
            </button>
        </div>

        <div class="malou-dialog__body">
            <div class="flex flex-col gap-3">
                @for (media of socialPostMedias(); let index = $index; track media.url) {
                    @if (media.type === MediaType.PHOTO) {
                        <div class="flex gap-3">
                            <div
                                class="overflow-hidden rounded-[5px]"
                                [ngStyle]="{ height: LINE_HEIGHT + 'px', width: LINE_HEIGHT + 'px' }">
                                <app-social-post-media-item [media]="media"></app-social-post-media-item>
                            </div>

                            <app-text-area
                                class="grow"
                                [heightPx]="LINE_HEIGHT"
                                [defaultValue]="medias()[index].alternativeText ?? ''"
                                [placeholder]="
                                    'social_posts.upsert_social_post_modal.content_form.alternative_texts.placeholder' | translate
                                "
                                [rows]="1"
                                [isEmojiPickerEnabled]="false"
                                [maxLength]="MAX_ALTERNATIVE_TEXT_LENGTH"
                                (textAreaChange)="onAlternativeTextChanged($event, index)"></app-text-area>
                        </div>
                    }
                }
            </div>
        </div>

        <div class="malou-dialog__footer md:justify-between">
            <app-button
                buttonClasses="!h-11 !rounded-[5px]"
                [text]="'common.cancel' | translate"
                [theme]="'secondary'"
                (buttonClick)="cancel()"></app-button>
            <app-button buttonClasses="!h-11 !rounded-[5px]" [text]="'common.save' | translate" (buttonClick)="save()"></app-button>
        </div>
    </div>
</div>
