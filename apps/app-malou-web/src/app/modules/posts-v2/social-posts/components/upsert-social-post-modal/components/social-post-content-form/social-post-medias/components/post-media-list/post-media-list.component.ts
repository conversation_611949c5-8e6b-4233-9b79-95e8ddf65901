import { moveItemInArray } from '@angular/cdk/drag-drop';
import { ChangeDetectionStrategy, Component, computed, inject, input, model, output } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { lastValueFrom } from 'rxjs';

import { CarouselAspectRatio, isEnum, MediaCropComputer, ProcessingMediaStatus, PublicationType } from '@malou-io/package-utils';

import { ProcessingMediasService } from ':core/services/processing-medias.service';
import { RestaurantsService } from ':core/services/restaurants.service';
import { ToastService } from ':core/services/toast.service';
import { MediaService, UploadClientError } from ':modules/media/media.service';
import { PreviewsService } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/components/previews-feed-notes/components/previews/previews.service';
import { IUpsertSocialPost } from ':modules/posts-v2/social-posts/models/upsert-social-post';
import { MediaThumbnailListComponent } from ':shared/components/media-thumbnail-list/media-thumbnail-list.component';
import {
    PinturaEditorModalComponent,
    PinturaEditorModalData,
    PinturaEditorModalResult,
} from ':shared/components/pintura-editor-modal/pintura-editor-modal.component';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { CustomDialogService } from ':shared/services/custom-dialog.service';

@Component({
    selector: 'app-post-media-list',
    templateUrl: './post-media-list.component.html',
    imports: [MatIconModule, TranslateModule, MediaThumbnailListComponent, MatMenuModule],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PostMediaListComponent {
    private readonly _customDialogService = inject(CustomDialogService);
    private readonly _mediaService = inject(MediaService);
    private readonly _restaurantsService = inject(RestaurantsService);
    private readonly _toastService = inject(ToastService);
    private readonly _translateService = inject(TranslateService);
    private readonly _processingMediasService = inject(ProcessingMediasService);
    private readonly _previewsService = inject(PreviewsService);

    readonly medias = model.required<IUpsertSocialPost['medias']>();
    readonly showEditMediaButton = input(true);
    readonly uploadingMediaCount = input(0);
    readonly isReadonly = input.required<boolean>();
    readonly carouselAspectRatio = input<CarouselAspectRatio>();
    readonly mediaClicked = output<string>();
    readonly openFilePicker = output();
    readonly importFromGallery = output();
    readonly carouselAspectRatioChanged = output<CarouselAspectRatio>();

    readonly SvgIcon = SvgIcon;

    readonly mediasForThumbnailList = computed(() =>
        this.medias()
            .map((media) => media.editedMedia ?? media.uploadedMedia)
            .map((media) => ({
                id: media.id,
                url: media.thumbnail256OutsideUrl,
                type: media.type,
            }))
    );

    onEditMedia(mediaId: string): void {
        const media = this.medias().find((m) => m.editedMedia?.id === mediaId || m.uploadedMedia.id === mediaId);
        if (!media) {
            return;
        }
        const preferredAspectRatio = MediaCropComputer.computePreferredAspectRatioFor({
            publicationType: PublicationType.POST,
            originalAspectRatio: media.uploadedMedia.aspectRatio,
        });
        const cropAspectRatioDefault = preferredAspectRatio === 1 ? '1:1' : preferredAspectRatio > 1 ? '16:9' : '4:5';
        this._customDialogService
            .open<PinturaEditorModalComponent, PinturaEditorModalData, PinturaEditorModalResult>(PinturaEditorModalComponent, {
                width: '75vw',
                height: '90vh',
                data: {
                    url: media.uploadedMedia.url,
                    cropAspectRatioDefault: cropAspectRatioDefault,
                    cropAspectRatioPresets: ['4:5', '1:1', '16:9'],
                    serializedPinturaEditorOptions: media.serializedPinturaEditorOptions,
                    cropAspectRatioOverride: this.carouselAspectRatio(),
                },
            })
            .afterClosed()
            .subscribe((result) => {
                if (result) {
                    this._onImageEditionDone(media.uploadedMedia.id, result);
                }
            });
    }

    private async _onImageEditionDone(uploadedMediaId: string, editionResult: PinturaEditorModalResult): Promise<void> {
        // optimistic update
        const previousCarouselAspectRatio = this.carouselAspectRatio();
        if (isEnum(editionResult.cropAspectRatioSelected, CarouselAspectRatio)) {
            this.carouselAspectRatioChanged.emit(editionResult.cropAspectRatioSelected);
        }
        const uploadRes = await this._mediaService.uploadV2({
            file: editionResult.file,
            onProgress: () => {},
            queryParams: {
                restaurantId: this._restaurantsService.currentRestaurant._id,
                originalMediaId: uploadedMediaId,
            },
        });
        if (!uploadRes.success) {
            if (uploadRes.errorCode === UploadClientError.NETWORK_ERROR) {
                this._toastService.openErrorToast(this._translateService.instant('upload_errors.network_error'));
            }
            // revert optimistic update
            if (previousCarouselAspectRatio) {
                this.carouselAspectRatioChanged.emit(previousCarouselAspectRatio);
            }
            return;
        }
        const processingMedia = await this._processingMediasService.waitUntilEnded(uploadRes.result.processingMediaId);
        if (processingMedia.status === ProcessingMediaStatus.ERROR) {
            this._toastService.openErrorToast(this._translateService.instant('upload_errors.invalid_file'));
            // revert optimistic update
            if (previousCarouselAspectRatio) {
                this.carouselAspectRatioChanged.emit(previousCarouselAspectRatio);
            }
            return;
        }
        if (processingMedia.mediaId) {
            {
                const mediaBeforeUpdate = this.medias().find((m) => m.uploadedMedia.id === uploadedMediaId);
                const mediaPreviewToSelect = mediaBeforeUpdate?.editedMedia?.id ?? mediaBeforeUpdate?.uploadedMedia.id;
                if (mediaPreviewToSelect) {
                    this._previewsService.mediaIdSelected$.next(mediaPreviewToSelect);
                }
            }
            const media = await lastValueFrom(this._mediaService.getMediaForEdition({ mediaId: processingMedia.mediaId }));
            this.medias.update((medias) =>
                medias.map((m) =>
                    m.uploadedMedia.id === uploadedMediaId
                        ? {
                              uploadedMedia: m.uploadedMedia,
                              editedMedia: media,
                              serializedPinturaEditorOptions: editionResult.serializedPinturaEditorOptions,
                              alternativeText: m.alternativeText,
                          }
                        : m
                )
            );
            return;
        }
        this._toastService.openErrorToast(this._translateService.instant('common.unknow_error'));
        // revert optimistic update
        if (previousCarouselAspectRatio) {
            this.carouselAspectRatioChanged.emit(previousCarouselAspectRatio);
        }
    }

    onRemoveMedia(mediaId: string): void {
        this.medias.update((medias) => medias.filter((m) => m.uploadedMedia.id !== mediaId && m.editedMedia?.id !== mediaId));
    }

    onDropMedia(event: { previousIndex: number; currentIndex: number }): void {
        this.medias.update((medias) => {
            const mediasWithNewRef = [...medias];
            moveItemInArray(mediasWithNewRef, event.previousIndex, event.currentIndex);
            return mediasWithNewRef;
        });
    }
}
