import { GetStoryMediaForEditionResponseDto, SocialPostMediaDto } from '@malou-io/package-dto';
import { MediaType, RemoveMethodsFromClass } from '@malou-io/package-utils';

import { EditionMedia } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/components/social-post-content-form/social-post-medias/edition-media.interface';

export type ISocialPostMedia = RemoveMethodsFromClass<SocialPostMedia>;

export class SocialPostMedia {
    url: string;
    type: MediaType;
    thumbnailUrl?: string;
    duration?: number;
    aspectRatio?: number;

    constructor(data: ISocialPostMedia) {
        this.url = data.url;
        this.type = data.type;
        this.thumbnailUrl = data.thumbnailUrl;
        this.duration = data.duration;
        this.aspectRatio = data.aspectRatio;
    }

    static fromDto(dto: SocialPostMediaDto): SocialPostMedia {
        return new SocialPostMedia({
            url: dto.url,
            type: dto.type,
            thumbnailUrl: dto.thumbnailUrl,
            duration: dto.duration,
            aspectRatio: dto.aspectRatio,
        });
    }

    static fromEditionMedia(media: EditionMedia): SocialPostMedia {
        return new SocialPostMedia({
            url: media.type === MediaType.VIDEO ? media.videoUrl : media.thumbnail1024OutsideUrl,
            type: media.type,
            thumbnailUrl: media.thumbnail1024OutsideUrl,
            duration: media.type === MediaType.VIDEO ? media.duration : undefined,
            aspectRatio: media.aspectRatio,
        });
    }

    static fromStoryMediaForEdition(media: GetStoryMediaForEditionResponseDto): SocialPostMedia {
        return new SocialPostMedia({
            url: media.url,
            type: media.type,
            thumbnailUrl: media.thumbnail256Url,
            duration: media.type === MediaType.VIDEO ? media.durationInSeconds : undefined,
        });
    }
}
