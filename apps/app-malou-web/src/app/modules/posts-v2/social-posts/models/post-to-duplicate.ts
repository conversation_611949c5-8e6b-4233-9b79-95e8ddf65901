import { PostToDuplicateDto } from '@malou-io/package-dto';
import {
    ApplicationLanguage,
    MediaType,
    PlatformKey,
    PostCallToActionType,
    PostPublicationStatus,
    PostSource,
    PostType,
    removeHashtagsFromText,
    RemoveMethodsFromClass,
} from '@malou-io/package-utils';

import { SocialPostMedia } from ':modules/posts-v2/social-posts/models/social-post-media';
import { Hashtag, PostHashtags } from ':shared/models';

type IPostToDuplicate = RemoveMethodsFromClass<PostToDuplicate>;

/** This is basically the response of GET /posts/to-duplicate */
export class PostToDuplicate implements IPostToDuplicate {
    id: string;
    bindingId: string;
    postType: PostType;
    source: PostSource;
    platformKeys: PlatformKey[];
    published: PostPublicationStatus;
    text: string;
    plannedPublicationDate: Date | null;
    socialCreatedAt: Date | null;
    medias: SocialPostMedia[];
    language: ApplicationLanguage | null;
    hashtags?: PostHashtags;
    callToAction?: {
        actionType: PostCallToActionType;
        url?: string;
    } | null;
    languagesDetectedByAI?: string[];

    constructor(data: IPostToDuplicate) {
        this.id = data.id;
        this.bindingId = data.bindingId;
        this.postType = data.postType;
        this.source = data.source;
        this.platformKeys = data.platformKeys;
        this.published = data.published;
        this.text = data.text;
        this.plannedPublicationDate = data.plannedPublicationDate;
        this.medias = data.medias;
        this.language = data.language;
        this.hashtags = data.hashtags;
        this.callToAction = data.callToAction;
        this.socialCreatedAt = data.socialCreatedAt;
        this.languagesDetectedByAI = data.languagesDetectedByAI;
    }

    static fromDto(dto: PostToDuplicateDto): PostToDuplicate {
        return new PostToDuplicate({
            id: dto.id,
            bindingId: dto.bindingId,
            postType: dto.postType,
            source: dto.source,
            platformKeys: dto.platformKeys,
            published: dto.published,
            text: dto.text,
            plannedPublicationDate: dto.plannedPublicationDate ? new Date(dto.plannedPublicationDate) : null,
            medias: dto.medias.map((media) => SocialPostMedia.fromDto(media)),
            language: dto.language,
            hashtags: dto.hashtags
                ? {
                      selected: dto.hashtags.selected.map((hashtag) => new Hashtag(hashtag)),
                      suggested: dto.hashtags.suggested.map((hashtag) => new Hashtag(hashtag)),
                  }
                : undefined,
            callToAction: dto.callToAction,
            socialCreatedAt: dto.socialCreatedAt ? new Date(dto.socialCreatedAt) : null,
            languagesDetectedByAI: dto.languagesDetectedByAI,
        });
    }

    getLanguage(): string | null {
        return this.languagesDetectedByAI?.[0] ?? this.language ?? null;
    }

    containsVideo(): boolean {
        return this.medias.some((media) => media.type === MediaType.VIDEO);
    }

    getAllSmallestAttachmentUrls(): { id: string; url: string; type: MediaType }[] {
        return this.medias.map((media) => ({
            id: '', // We don't have the id of the media here, but we do not need it in our current use cases
            url: media.thumbnailUrl ?? media.url,
            type: media.type,
        }));
    }

    removeHashtagsFromText(): string {
        return removeHashtagsFromText(this.text ?? '', this.hashtags?.selected.map((h) => h.text) ?? []);
    }

    isReel(): boolean {
        return this.postType === PostType.REEL;
    }
}
