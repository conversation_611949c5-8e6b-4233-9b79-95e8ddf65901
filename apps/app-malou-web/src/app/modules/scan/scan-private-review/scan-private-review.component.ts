import { Ng<PERSON><PERSON>, NgTemplateOutlet } from '@angular/common';
import { Component, inject, OnInit, signal } from '@angular/core';
import { FormControl, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatIconModule } from '@angular/material/icon';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { map, of, switchMap } from 'rxjs';

import { MalouSpinnerComponent } from ':core/components/spinner/spinner/malou-spinner.component';
import { RestaurantsService } from ':core/services/restaurants.service';
import { ScansService } from ':core/services/scans.service';
import { LocalStorageService } from ':core/storage/local-storage.service';
import { ReviewsService } from ':modules/reviews/reviews.service';
import { ButtonComponent } from ':shared/components/button/button.component';
import { InputTextComponent } from ':shared/components/input-text/input-text.component';
import { RateWithStarsComponent } from ':shared/components/rate-with-stars/rate-with-stars.component';
import { TextAreaComponent } from ':shared/components/text-area/text-area.component';
import { Media } from ':shared/models';
import { PrivateReview } from ':shared/models/private-review';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { Illustration, IllustrationPathResolverPipe } from ':shared/pipes/illustration-path-resolver.pipe';

export interface ScanPrivateReviewParams {
    restaurantId: string;
    rating: number;
    scanId: string;
}

interface WheelOfFortuneParams {
    wheelOfFortuneId: string;
    redirectionPlatform: string;
}

enum ReviewSubmissionStep {
    SUBMITTING_REVIEW = 'SUBMITTING_REVIEW',
    SUBMITTED = 'SUBMITTED',
}

@Component({
    selector: 'app-scan-private-review',
    templateUrl: './scan-private-review.component.html',
    styleUrls: ['./scan-private-review.component.scss'],
    imports: [
        NgClass,
        NgTemplateOutlet,
        MatButtonModule,
        MatCheckboxModule,
        MatIconModule,
        ReactiveFormsModule,
        TranslateModule,
        IllustrationPathResolverPipe,
        TextAreaComponent,
        MalouSpinnerComponent,
        RateWithStarsComponent,
        ButtonComponent,
        InputTextComponent,
    ],
})
export class ScanPrivateReviewComponent implements OnInit {
    private readonly _route = inject(ActivatedRoute);
    private readonly _router = inject(Router);
    private readonly _reviewsService = inject(ReviewsService);
    private readonly _translate = inject(TranslateService);
    private readonly _restaurantsService = inject(RestaurantsService);
    private readonly _localStorageService = inject(LocalStorageService);
    private readonly _scansService = inject(ScansService);

    readonly SvgIcon = SvgIcon;
    readonly Illustration = Illustration;
    readonly ReviewSubmissionStep = ReviewSubmissionStep;

    readonly initializing = signal(true);
    readonly isError = signal(false);
    readonly reviewSubmissionStep = signal(ReviewSubmissionStep.SUBMITTING_REVIEW);
    readonly isSubmittingReview = signal(false);
    readonly wofId = signal<string | null>(null);
    readonly selectedRating = signal<number | null>(null);
    readonly restaurantLogo = signal<Media | null>(null);
    readonly restaurantCover = signal<Media | null>(null);

    readonly textAreaControl = new FormControl<string>('', Validators.required);
    readonly emailControl = new FormControl<string>('', [
        Validators.required,
        Validators.email,
        Validators.pattern(/^[^\s@]+@[^\s@]+\.[^\s@]+$/),
    ]);
    readonly allowAcceptOffersControl = new FormControl<boolean>(false, Validators.requiredTrue);

    readonly shouldAskForEmail = signal(false);

    private _params: ScanPrivateReviewParams;
    private _wheelOfFortuneParams: WheelOfFortuneParams;

    ngOnInit(): void {
        const { restaurantId, rating, scanId, wofId, redirectionPlatform } = this._route.snapshot.queryParams;
        if (!restaurantId || !rating || !scanId) {
            this.isError.set(true);
            this.initializing.set(false);
            return;
        }
        this.selectedRating.set(parseInt(rating, 10));
        this.wofId.set(wofId);
        this._restaurantsService
            .getRestaurantCoverAndLogo(restaurantId)
            .pipe(map((res) => res.data))
            .subscribe({
                next: (restaurant) => {
                    this.restaurantLogo.set(restaurant.logo);
                    this.restaurantCover.set(restaurant.cover);
                },
            });
        this._params = { restaurantId, rating: parseInt(rating, 10), scanId };
        this._wheelOfFortuneParams = { wheelOfFortuneId: wofId, redirectionPlatform };

        this._scansService
            .getScanById(scanId)
            .pipe(map((res) => res.data))
            .subscribe({
                next: (scan) => {
                    this.shouldAskForEmail.set(!!scan.nfcSnapshot?.askForEmail);
                },
            });

        this.initializing.set(false);
    }

    submitReview(): void {
        const allowAdditionalContactModes = this.allowAcceptOffersControl.value ?? false;
        const shouldAskForEmail = this.shouldAskForEmail();
        const email = this.emailControl.value ?? '';
        if (shouldAskForEmail) {
            if (this.emailControl.invalid || !email) {
                return;
            }
        }
        const privateReviewToCreate: Partial<PrivateReview> = {
            restaurantId: this._params.restaurantId,
            text: this.textAreaControl.value || '',
            lang: this._translate.currentLang,
            scanId: this._params.scanId,
            socialCreatedAt: new Date(),
            rating: this._params.rating,
            archived: false,
        };
        this.isSubmittingReview.set(true);
        this._reviewsService
            .createPrivateReview(privateReviewToCreate)
            .pipe(
                map((res) => res.data.review),
                switchMap((privateReview) =>
                    shouldAskForEmail
                        ? this._reviewsService.saveClientIdOrEmailForReview(privateReview._id, { email, allowAdditionalContactModes })
                        : of({ data: privateReview })
                )
            )
            .subscribe({
                next: (res) => {
                    this.isSubmittingReview.set(false);
                    this.reviewSubmissionStep.set(ReviewSubmissionStep.SUBMITTED);
                    if (this._wheelOfFortuneParams.wheelOfFortuneId && this._wheelOfFortuneParams.redirectionPlatform) {
                        this._localStorageService.pushLeavedReviewWheelOfFortuneInLocalStorage({
                            wheelOfFortuneId: this._wheelOfFortuneParams.wheelOfFortuneId,
                            restaurantId: this._params.restaurantId,
                            redirectionPlatform: this._wheelOfFortuneParams.redirectionPlatform,
                        });
                        this._router.navigate(['wheel-of-fortune'], {
                            queryParams: {
                                wofId: this._wheelOfFortuneParams.wheelOfFortuneId,
                                restaurantId: this._params.restaurantId,
                                isFromTotem: false,
                                privateReviewId: res.data._id,
                            },
                        });
                    }
                },
                error: (err) => {
                    console.error('Error when submitting private review', err);
                    this.isSubmittingReview.set(false);
                    this.isError.set(true);
                },
            });
    }
}
