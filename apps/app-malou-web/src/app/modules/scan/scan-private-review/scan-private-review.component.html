@let cover = restaurantCover()?.urls?.original;
@let logo = restaurantLogo()?.urls?.original;

<div class="flex h-screen w-screen flex-col justify-between overflow-y-auto" [ngClass]="{ 'pb-4': cover || logo, 'py-4': !cover && !logo }">
    @if (initializing()) {
        <ng-container [ngTemplateOutlet]="initializingTemplate"></ng-container>
    } @else {
        <ng-container [ngTemplateOutlet]="pageLoadedTemplate"></ng-container>
    }
</div>

<ng-template #initializingTemplate>
    <div class="flex min-h-full flex-col items-center justify-center gap-y-8 px-8">
        <app-malou-spinner></app-malou-spinner>
    </div>
</ng-template>

<ng-template #pageLoadedTemplate>
    @if (isError()) {
        <ng-container [ngTemplateOutlet]="errorTemplate"></ng-container>
    } @else {
        <ng-container [ngTemplateOutlet]="pageTemplate"></ng-container>
    }
</ng-template>

<ng-template #errorTemplate>
    <div class="max-gap-y-8 flex min-h-full flex-col items-center justify-center px-8">
        <img class="w-24" [src]="Illustration.Cook | illustrationPathResolver" />
        {{ 'scan.scan_private_review.error' | translate }}
    </div>
</ng-template>

<ng-template #pageTemplate>
    <ng-container
        [ngTemplateOutlet]="restaurantCoverAndLogoTemplate"
        [ngTemplateOutletContext]="{
            cover,
            logo,
        }"></ng-container>
    @switch (reviewSubmissionStep()) {
        @case (ReviewSubmissionStep.SUBMITTING_REVIEW) {
            <div class="flex h-full flex-col items-center justify-between px-8 sm:gap-y-1" [ngClass]="{ 'pt-[60px]': logo || cover }">
                <div class="mt-4 flex flex-col items-center">
                    @if (!wofId()) {
                        <div class="malou-text-13--regular malou-color-text-2 text-center">
                            {{ 'scan.scan_private_review.description' | translate }}
                        </div>
                    }
                    <div class="w-72 rounded-[10px] p-4">
                        <app-rate-with-stars
                            class="flex h-10 sm:h-8"
                            [readOnly]="true"
                            [defaultStarRating]="selectedRating() ?? undefined"></app-rate-with-stars>
                    </div>
                </div>

                <div class="malou-text-25--bold sm:malou-text-20--bold malou-color-text-1 w-[460px] text-center sm:w-full">
                    {{ 'scan.scan_private_review.title' | translate }}
                </div>
                @if (shouldAskForEmail()) {
                    <div class="w-[460px] sm:w-full">
                        <app-input-text
                            [formControl]="emailControl"
                            [title]="'common.email' | translate"
                            [placeholder]="'common.mail_placeholder' | translate"
                            [required]="true"
                            [showRequiredStar]="true"
                            [errorMessage]="emailControl.dirty && (emailControl.errors?.error || emailControl.errors?.ref_email_phone)">
                        </app-input-text>
                    </div>
                }
                <div class="w-[460px] sm:w-full">
                    <app-text-area
                        [formControl]="textAreaControl"
                        [title]="'scan.scan_private_review.your_experience' | translate"
                        [placeholder]="'scan.scan_private_review.describe_experience' | translate"
                        [isEmojiPickerEnabled]="true"
                        [required]="true"
                        [rows]="5"></app-text-area>
                </div>
                @if (shouldAskForEmail()) {
                    <div class="w-[460px] sm:w-full">
                        <mat-checkbox color="primary" [formControl]="allowAcceptOffersControl">
                            {{ 'scan.scan_private_review.accept_offers' | translate }}
                        </mat-checkbox>
                    </div>
                }
                <div class="mb-4 w-[460px] sm:w-full">
                    <app-button
                        buttonClasses="w-full !h-12 sm:!h-14 bg-malou-background-color-text-1"
                        mat-raised-button
                        [loading]="isSubmittingReview()"
                        [text]="'scan.scan_private_review.submit' | translate"
                        [disabled]="textAreaControl.invalid || (shouldAskForEmail() && emailControl.invalid) || isSubmittingReview()"
                        (buttonClick)="submitReview()">
                    </app-button>
                </div>
            </div>
        }
        @case (ReviewSubmissionStep.SUBMITTED) {
            <div class="flex h-full max-w-full flex-col items-center justify-center gap-y-8 overflow-x-hidden px-8 pb-12">
                <div class="malou-text-14--bold malou-color-text-2 mt-10 w-[460px] text-center">
                    {{ 'scan.scan_private_review.review_email_submitted.title' | translate }}
                </div>

                <div class="malou-text-14--regular malou-color-text-2 mt-8 w-[460px] text-center sm:w-full">
                    {{ 'scan.scan_private_review.review_email_submitted.description' | translate }}
                </div>
            </div>
        }
    }
</ng-template>

<ng-template let-cover="cover" let-logo="logo" #restaurantCoverAndLogoTemplate>
    @let isShortCover = reviewSubmissionStep() === ReviewSubmissionStep.SUBMITTING_REVIEW;
    @let isLongCover = reviewSubmissionStep() === ReviewSubmissionStep.SUBMITTED;
    @if (cover || logo) {
        <div
            class="relative flex flex-col items-center transition-all duration-300 ease-linear"
            [ngClass]="{
                'h-[25vh]': cover && !isShortCover && !isLongCover,
                'h-[15vh]': !cover || isShortCover,
                'h-[35vh]': cover && isLongCover,
            }">
            @if (cover) {
                <img class="h-full w-screen object-cover" alt="cover" [src]="cover" />
            }
            @if (logo) {
                <img
                    class="malou-box-shadow h-24 w-24 rounded-full bg-white"
                    alt="logo"
                    [ngClass]="{
                        'mt-10': !cover,
                        'absolute -bottom-12': cover,
                    }"
                    [src]="logo" />
            }
        </div>
    }
</ng-template>
