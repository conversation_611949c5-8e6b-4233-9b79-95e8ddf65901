<a
    class="group flex cursor-pointer items-center justify-between rounded-md px-4 py-2 hover:bg-malou-color-background-dark"
    routerLinkActive
    [routerLink]="options().routerLink"
    [ngClass]="{
        '!justify-center !px-0': !isSidenavOpened(),
        'bg-malou-color-background-dark': isActiveComputed() && !options().hideBackgroundWhenActive,
    }"
    [routerLinkActiveOptions]="ROUTER_LINK_ACTIVE_OPTIONS"
    (click)="onClick()">
    <ng-container [ngTemplateOutlet]="leftPartTemplate"></ng-container>

    @if (isSidenavOpened()) {
        <ng-container [ngTemplateOutlet]="rightPartTemplate"></ng-container>
    }
</a>

<ng-template #leftPartTemplate>
    <div class="flex items-center gap-x-3" [ngClass]="{ 'justify-center': !isSidenavOpened() }">
        <ng-container [ngTemplateOutlet]="leftPartIconTemplate"></ng-container>

        @if (isSidenavOpened()) {
            <ng-container [ngTemplateOutlet]="leftPartTextTemplate"></ng-container>
            @let secondarySvgIcon = options().leftPart.secondarySvgIcon;
            @if (secondarySvgIcon) {
                <mat-icon class="!h-4 !w-4 {{ options().leftPart.secondarySvgIconColorClass }}" [svgIcon]="secondarySvgIcon"></mat-icon>
            }
        }
    </div>
</ng-template>

<ng-template #leftPartIconTemplate>
    @if (!options().leftPart.hideIconWhenActive || isActiveComputed()) {
        <div class="flex h-4 w-4 items-center justify-center">
            <mat-icon [class]="leftSvgIconSizeClass()" [svgIcon]="options().leftPart.svgIcon" [ngClass]="leftSvgIconNgClass()"></mat-icon>
        </div>
    }
</ng-template>

<ng-template #leftPartTextTemplate>
    <div class="truncate group-hover:font-semibold" [ngClass]="leftTextNgClass()">
        {{ options().leftPart.text }}
    </div>
</ng-template>

<ng-template #rightPartTemplate>
    <div class="leading-none">
        @let rightSvgIconOnHover = this.options().rightPart?.svgIconOnHover;
        @let rightChipText = options().rightPart?.chipText;
        @if (this.options().rightPart?.showChip && rightChipText) {
            <div
                class="malou-chip malou-chip--purple !ml-[10px] !h-[20px] !w-[40px] justify-center !p-0"
                [ngClass]="{ 'group-hover:hidden': !!rightSvgIconOnHover }">
                <span>{{ rightChipText }}</span>
            </div>
        }
        @if (rightSvgIconOnHover) {
            <mat-icon class="!hidden !h-3 !w-3 text-malou-color-primary group-hover:!block" [svgIcon]="rightSvgIconOnHover"></mat-icon>
        }
        @let rightNotificationCount = this.options().rightPart?.notificationCount;
        @if (this.options().rightPart?.showNotificationCount && rightNotificationCount !== undefined && rightNotificationCount > 0) {
            <div
                class="malou-color-background-dark malou-color-text-2 malou-text-10--medium rounded-md px-2 py-1"
                [class.group-hover:hidden]="!!rightNotificationCount">
                {{ rightNotificationCount ?? 0 | threeDigitNumber }}
            </div>
        }
    </div>
</ng-template>
