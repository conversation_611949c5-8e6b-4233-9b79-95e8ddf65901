@use '_malou_mixins.scss' as *;
@use '_malou_functions.scss' as *;
@use '../../../../styles/malou/malou_tooltips';
@use '_malou_variables.scss' as *;

.section-size {
    height: calc(90vh - 80px);
}
@include malou-respond-to('medium') {
    .section-size {
        height: auto;
    }
}

@for $i from 1 through 10 {
    .flex-#{$i} {
        flex: $i;
    }
}

input[type='time']::-webkit-calendar-picker-indicator {
    filter: invert(26%) sepia(92%) saturate(1934%) hue-rotate(237deg) brightness(107%) contrast(106%);
    font-size: toRem(16px);
    margin-left: 40px;
}

.mat-mdc-tab-link.small-tab-link {
    min-width: 0;
    padding: 0px 10px;
}

:host::ng-deep.mat-ink-bar {
    width: 54px;
}

.nav-button {
    border-radius: 0px !important;
    border-width: 0px 0px 2px 0px !important;
    border-style: solid;
    border-color: $malou-color-background-dark;
    &.active {
        border-color: $malou-color-primary;
    }
}

.form-errors-tooltip {
    @extend .mdc-tooltip;
    background-color: $malou-color-text-2 !important;
    width: 250px;
    position: absolute;
    border-radius: 5px;
    opacity: 0;
    top: 0;
    left: 0;
    transform: translate(-50%, -110%);
    padding: toRem(6px);
    z-index: 1;
    transition: opacity 0.15s ease-in-out;
    text-align: justify !important;
}

.wrapper:hover {
    .form-errors-tooltip {
        opacity: 1;
    }
}

@media (max-width: 559px) {
    ::ng-deep .cdk-overlay-connected-position-bounding-box > .cdk-overlay-pane {
        bottom: 393px !important;
    }
}
