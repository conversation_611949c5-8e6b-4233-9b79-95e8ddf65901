import { createFeatureSelector, createSelector } from '@ngrx/store';

import { StoriesState } from './stories.reducer';

export const selectStoriesState = createFeatureSelector<StoriesState>('stories');

export const selectStoriesSyncState = createSelector(selectStoriesState, (state) => state.lastSyncDates);

export const selectImageEditions = createSelector(selectStoriesState, (state) => state.imageEditionByStoryId);
