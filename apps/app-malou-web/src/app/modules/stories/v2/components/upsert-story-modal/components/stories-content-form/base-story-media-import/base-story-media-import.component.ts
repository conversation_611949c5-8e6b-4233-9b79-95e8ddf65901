import { ChangeDetectionStrategy, Component, inject, input } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { lastValueFrom } from 'rxjs';

import { GetStoryMediaForEditionResponseDto } from '@malou-io/package-dto';

import { RestaurantsService } from ':core/services/restaurants.service';
import { ToastService } from ':core/services/toast.service';
import { MediaPickerModalComponent } from ':modules/media/media-picker-modal/media-picker-modal.component';
import { MediaPickerFilter } from ':modules/media/media-picker-modal/media-picker-modal.interface';
import { MediaService } from ':modules/media/media.service';
import { MediaUploaderService } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/components/social-post-content-form/social-post-medias/media-uploader.service';
import { MAX_MEDIA_COUNT_IN_STORY } from ':modules/stories/v2/components/upsert-story-modal/upsert-story-modal.constants';
import { StoryForEdition } from ':modules/stories/v2/models/story-for-edition';
import { Media } from ':shared/models';
import { CustomDialogService } from ':shared/services/custom-dialog.service';

@Component({
    selector: 'app-base-story-media-import',
    template: '',
    imports: [],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export abstract class BaseStoryMediaImportComponent {
    readonly medias = input.required<StoryForEdition['medias']>();

    protected readonly _customDialogService = inject(CustomDialogService);
    protected readonly _mediaService = inject(MediaService);
    protected readonly _restaurantsService = inject(RestaurantsService);
    protected readonly _toastService = inject(ToastService);
    protected readonly _translateService = inject(TranslateService);
    private readonly _mediaUploaderService = inject(MediaUploaderService);

    onImportMediaFromGallery(): void {
        this._customDialogService
            .open(MediaPickerModalComponent, {
                width: '600px',
                data: {
                    restaurant: this._restaurantsService.currentRestaurant,
                    multi: true,
                    filter: MediaPickerFilter.ALL,
                    selectedMedias: [],
                    maxMedia: this._getMaxMediaCount(),
                },
            })
            .afterClosed()
            .subscribe({
                next: (medias: Media[] | false) => {
                    if (medias) {
                        medias.forEach((media) => {
                            this._importMediaFromGallery(media.id);
                        });
                    }
                },
                error: (err) => {
                    console.warn('err :>>', err);
                },
            });
    }

    onImportFromFile(event: Event): void {
        const target = event.target as HTMLInputElement;
        const files = target.files as FileList;
        for (const file of Array.from(files)) {
            this._importMediaFromFile(file);
        }
    }

    uploadingMediaCount(): number {
        return this._mediaUploaderService.mediaCount();
    }

    private async _importMediaFromGallery(mediaId: string): Promise<void> {
        const hasFreeSlots = this._hasFreeMediaSlots();
        if (!hasFreeSlots) {
            this._toastService.openWarnToast(this._translateService.instant('social_post_medias.max_medias_error'));
            return;
        }
        const res = await this._mediaUploaderService.uploadFromGalleryMediaId(mediaId);
        if (!res) {
            return;
        }
        const media = await lastValueFrom(this._mediaService.getStoryMediaForEdition({ mediaId: res.mediaId }));
        this.addMedia(media);
    }

    protected async _importMediaFromFile(file: File): Promise<void> {
        const hasFreeSlots = this._hasFreeMediaSlots();
        if (!hasFreeSlots) {
            this._toastService.openWarnToast(this._translateService.instant('social_post_medias.max_medias_error'));
            return;
        }
        const res = await this._mediaUploaderService.uploadFromFile(file);
        if (!res) {
            return;
        }
        const media = await lastValueFrom(this._mediaService.getStoryMediaForEdition({ mediaId: res.mediaId }));
        this.addMedia(media);
    }

    private _getMaxMediaCount(): number {
        return MAX_MEDIA_COUNT_IN_STORY - this.medias().length - this.uploadingMediaCount();
    }

    private _hasFreeMediaSlots(): boolean {
        const freeSlots = this._getMaxMediaCount();
        return freeSlots > 0;
    }

    protected abstract addMedia(storyMediaForEdition: GetStoryMediaForEditionResponseDto): void;
}
