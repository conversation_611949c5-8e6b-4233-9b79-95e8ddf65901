import { moveItemInArray } from '@angular/cdk/drag-drop';
import { computed, DestroyRef, effect, inject, Injectable, signal, untracked } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { patchState, signalState } from '@ngrx/signals';
import { Store } from '@ngrx/store';
import { TranslateService } from '@ngx-translate/core';
import {
    BehaviorSubject,
    catchError,
    combineLatest,
    forkJoin,
    lastValueFrom,
    map,
    Observable,
    of,
    Subject,
    switchMap,
    takeUntil,
    tap,
} from 'rxjs';

import { GetStoryForEditionResponseDto, GetStoryMediaForEditionResponseDto, UserTagsList } from '@malou-io/package-dto';
import { HeapEventName, MediaType, PlatformKey, PostPublicationStatus, RecurrentStoryFrequency } from '@malou-io/package-utils';

import { HeapService } from ':core/services/heap.service';
import { PlatformsService } from ':core/services/platforms.service';
import { RestaurantsService } from ':core/services/restaurants.service';
import { ToastService } from ':core/services/toast.service';
import { MediaService } from ':modules/media/media.service';
import { UpsertSocialPostService } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/upsert-social-post.service';
import { UpsertStoryState } from ':modules/stories/v2/components/upsert-story-modal/contexts/upsert-story.interface';
import { MAX_MEDIA_COUNT_IN_STORY } from ':modules/stories/v2/components/upsert-story-modal/upsert-story-modal.constants';
import { UpsertStoryService } from ':modules/stories/v2/components/upsert-story.service';
import { StoryForEdition } from ':modules/stories/v2/models/story-for-edition';
import { StoryForEditionUpdates } from ':modules/stories/v2/models/story-for-edition-updates';
import { IUpsertStory } from ':modules/stories/v2/models/upsert-story';
import { selectUserInfos } from ':modules/user/store/user.selectors';
import { SubmitPublicationStatus } from ':shared/components/posts-v2/posts-v2.interface';
import { Platform, UserTag } from ':shared/models';

const STORY_VIDEO_DURATION_RULES: Record<
    PlatformKey.INSTAGRAM | PlatformKey.FACEBOOK,
    { minDurationInSeconds: number; maxDurationInSeconds: number }
> = {
    [PlatformKey.INSTAGRAM]: {
        minDurationInSeconds: 3,
        maxDurationInSeconds: 60,
    },
    [PlatformKey.FACEBOOK]: {
        minDurationInSeconds: 3,
        maxDurationInSeconds: 90,
    },
};

@Injectable()
export class UpsertStoryContext {
    private readonly _mediaService = inject(MediaService);
    private readonly _restaurantsService = inject(RestaurantsService);
    private readonly _platformsService = inject(PlatformsService);
    private readonly _upsertStoryService = inject(UpsertStoryService);
    private readonly _upsertSocialPostService = inject(UpsertSocialPostService);
    private readonly _store = inject(Store);
    private readonly _heapService = inject(HeapService);
    private readonly _toastService = inject(ToastService);
    private readonly _translateService = inject(TranslateService);
    private readonly _destroyRef = inject(DestroyRef);

    readonly upsertStoryState = signalState<UpsertStoryState>(this._getInitialState());
    private readonly _pendingStoryUpdates = signal(new StoryForEditionUpdates({}));
    private readonly _allStoryUpdates = signal(new StoryForEditionUpdates({}));
    readonly initialStoryForEdition = signal<StoryForEdition | null>(null);
    readonly mode = signal<'edition' | 'creation' | null>(null);
    readonly postErrors = computed(() => this.getStoriesErrors());
    readonly hasErrorOccurredDuringInit = signal(false);
    readonly closeModal$ = new Subject<void>();
    private readonly _shouldAutoSave = computed(() => {
        const mode = this.mode();
        if (mode === 'creation') {
            return true;
        } else if (mode === 'edition') {
            return this.initialStoryForEdition()?.published === PostPublicationStatus.DRAFT;
        }
        return false;
    });
    readonly medias = computed(
        () => this._pendingStoryUpdates().medias ?? this._allStoryUpdates().medias ?? this.initialStoryForEdition()?.medias ?? []
    );
    readonly platformKeys = computed(
        () =>
            this._pendingStoryUpdates().platformKeys ??
            this._allStoryUpdates().platformKeys ??
            this.initialStoryForEdition()?.platformKeys ??
            []
    );
    readonly plannedPublicationDate = computed(
        () =>
            this._pendingStoryUpdates().plannedPublicationDate ??
            this._allStoryUpdates().plannedPublicationDate ??
            this.initialStoryForEdition()?.plannedPublicationDate ??
            undefined
    );
    readonly recurrentStoryFrequency = computed(
        () =>
            this._pendingStoryUpdates().recurrentStoryFrequency ??
            this._allStoryUpdates().recurrentStoryFrequency ??
            this.initialStoryForEdition()?.recurrentStoryFrequency ??
            RecurrentStoryFrequency.NONE
    );
    readonly userTagsList = computed(
        () =>
            this._pendingStoryUpdates().userTagsList ??
            this._allStoryUpdates().userTagsList ??
            this.initialStoryForEdition()?.userTagsList ??
            []
    );

    private readonly _disconnectedPlatforms$: BehaviorSubject<Platform[]> = new BehaviorSubject([]);
    private readonly _resetSubscription$ = new BehaviorSubject<void>(undefined);

    readonly profilePictureUrls = signal<Record<PlatformKey, string | undefined>>({} as Record<PlatformKey, string | undefined>);

    private readonly _uploadMediaCountTrackers = signal<Record<string, number>>({});
    readonly isMediaUploading = computed(() => Object.values(this._uploadMediaCountTrackers()).some((count) => count > 0));

    constructor() {
        effect(() => {
            this._pendingStoryUpdates(); // trigger
            untracked(() => this._autosave());
        });
    }

    async init(storyId: string | undefined, disconnectedPlatforms$: BehaviorSubject<Platform[]>, options?: { date?: Date }): Promise<void> {
        this.initialStoryForEdition.set(null);
        this.mode.set(storyId ? 'edition' : 'creation');

        this._setIsLoadingStory(true);
        this.hasErrorOccurredDuringInit.set(false);

        const currentRestaurantId = this._restaurantsService.currentRestaurant._id;

        this._initUserTagsHistory(currentRestaurantId);

        this._resetSubscription$.next();
        this._disconnectedPlatforms$.next(disconnectedPlatforms$.getValue());
        disconnectedPlatforms$.pipe(takeUntil(this._resetSubscription$)).subscribe((disconnectedPlatforms) => {
            this._disconnectedPlatforms$.next(disconnectedPlatforms);
        });

        let storyIdToFetch = storyId;
        if (!storyIdToFetch) {
            storyIdToFetch = await lastValueFrom(this._upsertStoryService.createStory$(currentRestaurantId, options));
        }
        this._upsertStoryService.getStoryForEdition$(storyIdToFetch).subscribe({
            next: (storyForEdition) => {
                if (
                    storyForEdition?.published === PostPublicationStatus.PUBLISHED ||
                    (storyForEdition?.published === PostPublicationStatus.PENDING && storyForEdition?.isPublishing)
                ) {
                    this.closeModal$.next();
                    return;
                }

                this.initialStoryForEdition.set(StoryForEdition.fromGetStoryForEditionResponseDto(storyForEdition));

                this._initConnectedStoriesPlatforms({ isNewStory: this.mode() === 'creation' });

                this._setIsLoadingStory(false);
            },
            error: (error) => {
                console.error(error);
                this.closeModal$.next();
            },
        });
    }

    deleteStory$(): Observable<void> {
        const storyId = this.initialStoryForEdition()?.id;
        if (!storyId) {
            return of();
        }
        return this._upsertStoryService.deleteStory$(storyId).pipe(
            catchError((error) => {
                console.error(error);
                this._toastService.openErrorToast(this._translateService.instant('social_posts.error_delete_post'));
                return of();
            })
        );
    }

    revertToInitialStory$(): Observable<void> {
        const update = new StoryForEditionUpdates({
            platformKeys: this.initialStoryForEdition()?.platformKeys,
            submitPublicationStatus: undefined,
            plannedPublicationDate: this.initialStoryForEdition()?.plannedPublicationDate,
            medias: this.initialStoryForEdition()?.medias,
            userTagsList: this.initialStoryForEdition()?.userTagsList,
            recurrentStoryFrequency: this.initialStoryForEdition()?.recurrentStoryFrequency,
            feedbacksId: this.initialStoryForEdition()?.feedbacks?.id,
        });
        this._pendingStoryUpdates.set(update);
        return this.save$();
    }

    save$(): Observable<void> {
        const storyForEdition = this.initialStoryForEdition();
        const pendingStoryUpdates = this._pendingStoryUpdates();
        if (!storyForEdition || pendingStoryUpdates.isEmpty()) {
            return of();
        }
        const currentUser = this._store.selectSignal(selectUserInfos);
        this._heapService.track(HeapEventName.STORIES_UPDATE, {
            userId: currentUser()?.id,
            email: currentUser()?.email,
            restaurantId: this._restaurantsService.currentRestaurant._id,
            storyId: storyForEdition.id,
            submitPublicationStatus: pendingStoryUpdates.submitPublicationStatus,
        });
        const dto = pendingStoryUpdates.toUpdateStoryBodyDto();
        this._allStoryUpdates.update((data) => data.clone(pendingStoryUpdates));
        this._pendingStoryUpdates.set(new StoryForEditionUpdates({}));
        return this._upsertStoryService.updateStory$({ storyId: storyForEdition.id }, dto).pipe(
            catchError((error) => {
                console.error(error);
                this._toastService.openErrorToast(this._translateService.instant('social_posts.update_post.error'));
                return of();
            })
        );
    }

    getStoriesErrors(): string[] {
        const errors: string[] = [];
        const medias = this.medias();
        if (medias.length > MAX_MEDIA_COUNT_IN_STORY) {
            errors.push(
                this._translateService.instant('stories.upsert_stories_modal.errors.too_many_stories_error', {
                    max_media_count_in_story: MAX_MEDIA_COUNT_IN_STORY,
                })
            );
        }

        if (medias.length === 0) {
            errors.push(this._translateService.instant('stories.upsert_stories_modal.errors.no_media_selected'));
        }

        const platformKeys = this.platformKeys();
        if (platformKeys.length === 0) {
            errors.push(this._translateService.instant('stories.upsert_stories_modal.errors.no_platform_selected'));
        }

        const videoMedias = medias
            .map((media) => media.editedMedia ?? media.uploadedMedia)
            .filter((media) => media.type === MediaType.VIDEO);
        if (videoMedias.length > 0) {
            const allMinDurations = platformKeys
                .filter(this._isKeyInStoryVideoDurationRules)
                .map((k) => STORY_VIDEO_DURATION_RULES[k].minDurationInSeconds);

            const computedMinDuration = Math.max(...allMinDurations); // we want to be the more restrictive possible to comply with all platforms

            const allMaxDurations = platformKeys
                .filter(this._isKeyInStoryVideoDurationRules)
                .map((k) => STORY_VIDEO_DURATION_RULES[k].maxDurationInSeconds);

            const computedMaxDuration = Math.min(...allMaxDurations); // we want to be the more restrictive possible to comply with all platforms
            if (
                videoMedias.some((media) => media.durationInSeconds < computedMinDuration || media.durationInSeconds > computedMaxDuration)
            ) {
                errors.push(
                    this._translateService.instant('stories.upsert_stories_modal.errors.video_duration_error', {
                        minDurationInSeconds: computedMinDuration,
                        maxDurationInSeconds: computedMaxDuration,
                    })
                );
            }
        }

        // Should be kept as the last check
        if (errors.length === 0) {
            if (this.isMediaUploading()) {
                errors.push(this._translateService.instant('stories.upsert_stories_modal.errors.media_uploading'));
            }
        }

        return errors;
    }

    updatePlatformKeys(platformKeys: PlatformKey[]): void {
        this._pendingStoryUpdates.update((data) => data.clone({ platformKeys }));
    }

    setDuplicateToOtherRestaurants(value: boolean): void {
        patchState(this.upsertStoryState, (state) => ({ ...state, duplicateToOtherRestaurants: value }));
    }

    updateSubmitPublicationStatus(value: SubmitPublicationStatus): void {
        this._pendingStoryUpdates.update((data) => data.clone({ submitPublicationStatus: value }));
    }

    updatePlannedPublicationDate(date: Date | undefined): void {
        this._pendingStoryUpdates.update((data) => data.clone({ plannedPublicationDate: date }));
    }

    updateRecurrentStoryFrequency(value: RecurrentStoryFrequency): void {
        this._pendingStoryUpdates.update((data) => data.clone({ recurrentStoryFrequency: value }));
    }

    updateFeedbacksId(feedbacksId: string | undefined): void {
        this._pendingStoryUpdates.update((data) => data.clone({ feedbacksId }));
    }

    reorderMedias(previousIndex: number, currentIndex: number): void {
        const reorderedMedias = [...this.medias()];
        moveItemInArray(reorderedMedias, previousIndex, currentIndex);
        this.updateMedias(reorderedMedias);
    }

    addMedia(media: GetStoryForEditionResponseDto['medias'][number], insertionIndex?: number): void {
        const medias = this.medias();
        const maxInsertionIndex = medias.length;
        const computedInsertionIndex = insertionIndex ? Math.max(Math.min(insertionIndex, maxInsertionIndex), 0) : maxInsertionIndex;
        const newMedias = [...medias.slice(0, computedInsertionIndex), media, ...medias.slice(computedInsertionIndex)];
        this.updateMedias(newMedias);
    }

    attachEditedMediaToUploadedMedia(
        uploadedMediaId: string,
        editedMedia: GetStoryMediaForEditionResponseDto,
        serializedPinturaEditorOptions: string | undefined
    ): void {
        const currentMedias = this.medias();

        const newMedias = currentMedias?.map((media) => {
            if (media.uploadedMedia.id === uploadedMediaId) {
                return { ...media, editedMedia, serializedPinturaEditorOptions };
            }
            return media;
        });
        this.updateMedias(newMedias ?? []);
    }

    async duplicateMedia(uploadedMediaId: string, insertionIndex: number): Promise<void> {
        const restaurantId = this._restaurantsService.currentRestaurant._id;

        try {
            const duplicationRes = await lastValueFrom(
                this._mediaService.duplicateMediaForPublication({ mediaId: uploadedMediaId }, { restaurantIds: [restaurantId] })
            );
            const media = await lastValueFrom(
                this._mediaService.getStoryMediaForEdition({ mediaId: duplicationRes.data[0].duplicatedMediaId })
            );
            this.addMedia({ uploadedMedia: media }, insertionIndex);
        } catch (error) {
            console.error(error);
            this._toastService.openErrorToast(this._translateService.instant('stories.error_duplicate_media'));
        }
    }

    removeMedia(uploadedMediaId: string): void {
        const newMedias = this.medias().filter((media) => media.uploadedMedia.id !== uploadedMediaId);
        this.updateMedias(newMedias);
    }

    updateMedias(medias: GetStoryForEditionResponseDto['medias']): void {
        const newUserTagsList: IUpsertStory['userTagsList'] = medias.map(() => []);
        const currentUserTagsLists = this.userTagsList();
        const currentMedias = this.medias();
        for (const [index, media] of medias.entries()) {
            const oldIndex = currentMedias.findIndex((currentMedia) => currentMedia.uploadedMedia.id === media.uploadedMedia.id);
            if (oldIndex !== -1) {
                newUserTagsList[index] = currentUserTagsLists[oldIndex];
            }
        }
        this._pendingStoryUpdates.update((data) => data.clone({ medias, userTagsList: newUserTagsList }));
    }

    addUserTag(mediaIdx: number, userTag: UserTag): void {
        const userTagsList = [...this.userTagsList()];
        const userTagsElement = userTagsList[mediaIdx];
        if (userTagsElement) {
            userTagsElement.push(userTag);
        } else {
            userTagsList[mediaIdx] = [userTag];
        }
        this.updateUserTagsList(userTagsList);
    }

    removeUserTag(mediaIdx: number, username: string): void {
        const userTagsList = [...this.userTagsList()];
        const userTagsElement = userTagsList[mediaIdx];
        if (userTagsElement) {
            userTagsList[mediaIdx] = userTagsElement.filter((userTag) => userTag.username !== username);
            this.updateUserTagsList(userTagsList);
        }
    }

    updateUserTagsList(userTagsList: (UserTagsList[] | null)[]): void {
        this._pendingStoryUpdates.update((data) => data.clone({ userTagsList }));
    }

    trackUploadMediaCount(key: string, uploadMediaCount: number): void {
        this._uploadMediaCountTrackers.update((state) => ({ ...state, [key]: Math.max(0, uploadMediaCount) }));
    }

    private _autosave(): void {
        if (this._shouldAutoSave()) {
            this.save$().subscribe();
        }
    }

    private _getInitialState(): UpsertStoryState {
        return {
            isLoadingStory: true,
            duplicateToOtherRestaurants: false,
            connectedSocialPlatforms: [],
            userTagsHistory: [],
        };
    }

    private _initConnectedStoriesPlatforms(options: { isNewStory: boolean }): void {
        const connectedPlatforms$ = this._upsertStoryService.getConnectedStoriesPlatforms$();

        combineLatest([this._disconnectedPlatforms$, connectedPlatforms$])
            .pipe(
                map(([disconnectedPlatforms, connectedPlatforms]) =>
                    connectedPlatforms.filter((platform) => !disconnectedPlatforms.some((p) => p.key === platform.key))
                ),
                tap((platforms) => {
                    const connectedPlatformKeys = platforms.map((platform) => platform.key);
                    patchState(this.upsertStoryState, (state) => ({
                        ...state,
                        connectedSocialPlatforms: platforms,
                    }));
                    if (options.isNewStory) {
                        this.updatePlatformKeys(connectedPlatformKeys);
                    }
                }),
                switchMap((platforms) =>
                    forkJoin(
                        platforms.map((platform) =>
                            forkJoin([
                                this._platformsService.getProfilePictureUrl(this._restaurantsService.currentRestaurant.id, platform.key),
                                of(platform.key),
                            ])
                        )
                    )
                ),
                takeUntilDestroyed(this._destroyRef)
            )
            .subscribe((results) => {
                this.profilePictureUrls.set(
                    results.reduce(
                        (acc, result) => ({ ...acc, [result[1]]: result[0].data.profilePictureUrl }),
                        {} as Record<PlatformKey, string | undefined>
                    )
                );
            });
    }

    private _initUserTagsHistory(restaurantId: string): void {
        this._upsertSocialPostService.getUserTagsHistory$(restaurantId).subscribe((result) => {
            patchState(this.upsertStoryState, (state) => ({ ...state, userTagsHistory: result.data }));
        });
    }

    private _setIsLoadingStory(isLoadingStory: boolean): void {
        patchState(this.upsertStoryState, (state) => ({ ...state, isLoadingStory }));
    }

    private _isKeyInStoryVideoDurationRules(v: PlatformKey): v is keyof typeof STORY_VIDEO_DURATION_RULES {
        return Object.keys(STORY_VIDEO_DURATION_RULES).includes(v);
    }
}
