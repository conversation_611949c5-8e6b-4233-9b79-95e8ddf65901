import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Store } from '@ngrx/store';
import { filter, map, Observable, take } from 'rxjs';

import { CreateStoryV2ResponseDto, GetStoryForEditionResponseDto, UpdateStoryBodyDto, UpdateStoryParamsDto } from '@malou-io/package-dto';
import {
    ApiResultV2,
    DeviceType,
    getFeatureFlaggedPlatforms,
    getPlatformKeysWithStories,
    isNotNil,
    PlatformKey,
} from '@malou-io/package-utils';

import { ExperimentationService } from ':core/services/experimentation.service';
import { environment } from ':environments/environment';
import { selectCurrentPlatforms } from ':modules/platforms/store/platforms.reducer';
import { Platform } from ':shared/models';

@Injectable({
    providedIn: 'root',
})
export class UpsertStoryService {
    private readonly _http = inject(HttpClient);
    private readonly _API_BASE_URL = `${environment.APP_MALOU_API_URL}/api/v1/stories`;
    private readonly _store = inject(Store);
    private readonly _experimentationService = inject(ExperimentationService);

    getStoryForEdition$(storyId: string): Observable<GetStoryForEditionResponseDto> {
        return this._http
            .get<ApiResultV2<GetStoryForEditionResponseDto>>(`${this._API_BASE_URL}/${storyId}/get-story-for-edition`)
            .pipe(map((res) => res.data));
    }

    createStory$(restaurantId: string, options?: { date?: Date }): Observable<CreateStoryV2ResponseDto> {
        return this._http
            .post<ApiResultV2<CreateStoryV2ResponseDto>>(`${this._API_BASE_URL}`, {
                restaurantId,
                createdFromDeviceType: DeviceType.DESKTOP,
                ...options,
            })
            .pipe(map((res) => res.data));
    }

    updateStory$(params: UpdateStoryParamsDto, story: UpdateStoryBodyDto): Observable<void> {
        return this._http.put<void>(`${this._API_BASE_URL}/${params.storyId}`, story);
    }

    deleteStory$(storyId: string): Observable<void> {
        return this._http.delete<void>(`${this._API_BASE_URL}/${storyId}`);
    }

    getConnectedStoriesPlatforms$(): Observable<Platform[]> {
        return this._store.select(selectCurrentPlatforms).pipe(
            filter(isNotNil),
            map((platforms) => {
                const platformKeysToHide = this._getFeatureFlaggedPlatformKeysToHide();
                return platforms.filter(
                    (platform) => getPlatformKeysWithStories().includes(platform.key) && !platformKeysToHide.includes(platform.key)
                );
            }),
            take(1)
        );
    }

    private _getFeatureFlaggedPlatformKeysToHide(): PlatformKey[] {
        const featureFlaggedPlatforms = getFeatureFlaggedPlatforms();
        const featureFlaggedPlatformKeysToHide = featureFlaggedPlatforms
            .filter((p) => !this._experimentationService.isFeatureEnabled(p.featureFlagKey!))
            .map((p) => p.key);
        return featureFlaggedPlatformKeysToHide;
    }
}
