import { AsyncPipe, NgTemplateOutlet } from '@angular/common';
import { HttpErrorResponse } from '@angular/common/http';
import { AfterViewInit, Component, computed, inject, OnDestroy, OnInit, Signal, signal, WritableSignal } from '@angular/core';
import { takeUntilDestroyed, toObservable, toSignal } from '@angular/core/rxjs-interop';
import { MatButtonModule } from '@angular/material/button';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatRippleModule } from '@angular/material/core';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { PageEvent } from '@angular/material/paginator';
import { MatTabsModule } from '@angular/material/tabs';
import { MatTooltipModule } from '@angular/material/tooltip';
import { ActivatedRoute, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { isEqual, sortBy, uniqBy } from 'lodash';
import { DateTime } from 'luxon';
import { InfiniteScrollModule } from 'ngx-infinite-scroll';
import { BehaviorSubject, combineLatest, EMPTY, forkJoin, Observable, of, Subject, throwError } from 'rxjs';
import { catchError, defaultIfEmpty, distinctUntilChanged, filter, map, switchMap, takeUntil, tap } from 'rxjs/operators';
import { v4 as uuidv4 } from 'uuid';

import {
    canDeleteStory,
    errorReplacer,
    getPlatformKeysWithStories,
    isNotNil,
    MalouPeriod,
    PlatformKey,
    PostPublicationStatus,
    PostSource,
} from '@malou-io/package-utils';

import { periodOptions } from ':core/constants';
import { selectPermissionsState } from ':core/credentials/store/permissions.reducer';
import { DialogService } from ':core/services/dialog.service';
import { PlatformsService } from ':core/services/platforms.service';
import { PostsService } from ':core/services/posts.service';
import { RestaurantsService } from ':core/services/restaurants.service';
import { ScreenSizeService } from ':core/services/screen-size.service';
import { ToastService } from ':core/services/toast.service';
import { selectCurrentPlatform } from ':modules/platforms/store/platforms.reducer';
import { defaultPublicationStatus } from ':modules/posts/posts.reducer';
import { selectCurrentlyPublishingPosts } from ':modules/posts/posts.selectors';
import { DuplicateStoriesService } from ':modules/stories/duplicate-stories.service';
import { UserState } from ':modules/user/store/user.reducer';
import { User } from ':modules/user/user';
import { GroupedDateFiltersComponent } from ':shared/components/grouped-date-filters/grouped-date-filters.component';
import { IsPublishedFiltersComponent } from ':shared/components/is-published-filters/is-published-filters.component';
import { DialogVariant } from ':shared/components/malou-dialog/malou-dialog.component';
import { PlatformLogoComponent } from ':shared/components/platform-logo/platform-logo.component';
import { PostSkeletonComponent } from ':shared/components/skeleton/templates/post-skeleton/post-skeleton.component';
import { AutoUnsubscribeOnDestroy } from ':shared/decorators/auto-unsubscribe-on-destroy.decorator';
import { DuplicationDestination } from ':shared/enums/duplication-destination.enum';
import { SelectionModelExtended } from ':shared/helpers/selection-model-extended';
import { TrackByFunctionFactory } from ':shared/helpers/track-by-functions';
import { KillSubscriptions } from ':shared/interfaces';
import {
    ActionsAfterEditStoriesClosed,
    AvailablePlatform,
    Pagination,
    Platform,
    Post,
    PostsFilters,
    PostWithJob,
    Restaurant,
    Story,
} from ':shared/models';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { ApplySelfPurePipe } from ':shared/pipes/apply-fn.pipe';
import { HttpErrorPipe } from ':shared/pipes/http-error.pipe';
import { Illustration, IllustrationPathResolverPipe } from ':shared/pipes/illustration-path-resolver.pipe';
import { CustomDialogService, DialogScreenSize } from ':shared/services/custom-dialog.service';
import { ScrollableContentService } from ':shared/services/scrollable-content.service';

import { EditStoryModalComponent } from './edit-story-modal/edit-story-modal.component';
import * as StoriesActions from './store/stories.actions';
import { initialState } from './store/stories.reducer';
import { selectStoriesSyncState } from './store/stories.selector';
import { StoryCarouselComponent } from './story-carousel/story-carousel.component';
import { StoryComponent } from './story/story.component';

interface PlatformsStore {
    platformsData: Record<string, Platform[]>;
}
interface AppState {
    stories: {
        filters: PostsFilters;
    };
    user: UserState;
    lastRefresh: {
        socialPosts: number;
    };
    platforms: PlatformsStore;
}

const extractIgUserNameFromIgLink = (igLink?: string): string | undefined => {
    if (!igLink) {
        return;
    }
    return igLink.split('/').pop();
};
const DEFAULT_PAGINATION = { pageSize: 24, pageNumber: 0, total: 0 };
const DEFAULT_PAGE_EVENT = { pageIndex: 0, pageSize: 24, length: 0 };
const DEFAULT_PERIOD = MalouPeriod.LAST_AND_COMING_SIX_MONTH;

@AutoUnsubscribeOnDestroy()
@Component({
    selector: 'app-stories',
    templateUrl: './stories.component.html',
    styleUrls: ['./stories.component.scss'],
    imports: [
        NgTemplateOutlet,
        MatTooltipModule,
        MatButtonModule,
        MatMenuModule,
        MatIconModule,
        MatRippleModule,
        InfiniteScrollModule,
        MatTabsModule,
        MatButtonToggleModule,
        TranslateModule,
        GroupedDateFiltersComponent,
        IsPublishedFiltersComponent,
        PostSkeletonComponent,
        StoryComponent,
        StoryCarouselComponent,
        PlatformLogoComponent,
        AsyncPipe,
        IllustrationPathResolverPipe,
        ApplySelfPurePipe,
    ],
})
export class StoriesComponent implements OnInit, AfterViewInit, OnDestroy, KillSubscriptions {
    private readonly _postsService = inject(PostsService);
    private readonly _restaurantsService = inject(RestaurantsService);
    private readonly _store = inject(Store<AppState>);
    private readonly _translate = inject(TranslateService);
    private readonly _router = inject(Router);
    private readonly _activatedRoute = inject(ActivatedRoute);
    private readonly _dialogService = inject(DialogService);
    private readonly _toastService = inject(ToastService);
    private readonly _httpErrorPipe = inject(HttpErrorPipe);
    private readonly _customDialogService = inject(CustomDialogService);
    private readonly _platformsService = inject(PlatformsService);
    private readonly _duplicateStoriesService = inject(DuplicateStoriesService);
    private readonly _scrollableContentService = inject(ScrollableContentService);
    readonly screenSizeService = inject(ScreenSizeService);

    readonly SvgIcon = SvgIcon;
    readonly trackByIdFn = TrackByFunctionFactory.get('_id');
    readonly trackByKeyFn = TrackByFunctionFactory.get('key');
    readonly storiesPlatformKeys = getPlatformKeysWithStories();

    restaurant: Restaurant;

    storiesToBeOpened$: BehaviorSubject<Post[]> = new BehaviorSubject([]);
    pagination: Pagination = DEFAULT_PAGINATION;
    pageEvent$: BehaviorSubject<PageEvent> = new BehaviorSubject(DEFAULT_PAGE_EVENT);
    restaurant$: Observable<Restaurant | null> = this._restaurantsService.restaurantSelected$;
    currentUser$: Observable<UserState> = this._store.select('user');
    currentUser: UserState;
    selectionModel = new SelectionModelExtended<Story>([], (item) => item._id);
    selectionCount = this.selectionModel.getCountAsSignal();
    stories = this.selectionModel.getSelectableValuesAsSignal();

    // true while the next page is being fetched from the API
    fetchingNextPage = signal(true);

    isLoadingStories = signal(true);

    canCreateStory = computed(() => this.connectedPlatformsAcceptingStories() !== undefined);

    loadedMoreWithPagination = false;

    shouldDisplayNoMoreResultsText = false;
    igPlatform$ = this._store.select(selectCurrentPlatform({ platformKey: PlatformKey.INSTAGRAM }));
    igPictureUrl$: Observable<string | null>;
    igUserName$: Observable<string | undefined> = this.igPlatform$.pipe(
        map((platform) => extractIgUserNameFromIgLink(platform?.socialLink))
    );
    killSubscriptions$ = new Subject<void>();
    PlatformKey = PlatformKey;
    isAnyPlatformConnected$ = this._store
        .select(selectPermissionsState)
        .pipe(
            map(({ data: permissions }) =>
                permissions?.filter((p) => getPlatformKeysWithStories().includes(p.key))?.some((perm) => !!perm.isValid)
            )
        );
    filters = initialState.filters;
    restaurantManagers: User[];

    // null while the list is loading
    currentStories: WritableSignal<Story[] | null> = signal(null);
    currentStories$ = toObservable(this.currentStories);

    Illustration = Illustration;
    DuplicationDestination = DuplicationDestination;
    synchronize$ = new Subject<void>();

    activeFiltersCount = 0;
    filters$: Observable<PostsFilters> = this._store
        .select((state) => state.stories.filters)
        .pipe(distinctUntilChanged((a, b) => JSON.stringify(a) === JSON.stringify(b)));
    period$: Observable<MalouPeriod> = this._store.select((state) => state.stories.filters.period) as Observable<MalouPeriod>;
    start$: Observable<Date> = this._store.select((state) => state.stories.filters.startDate) as Observable<Date>;
    end$: Observable<Date> = this._store.select((state) => state.stories.filters.endDate) as Observable<Date>;
    postFilterStatus$: Observable<string[]> = this._store.select((state) => state.stories.filters.publicationStatus) as Observable<
        string[]
    >;
    periodOptions = periodOptions.map((period) => period.key);
    platformsSelected$: Observable<string[]> = this._store.select((state) => state.stories.filters.platforms ?? []) as Observable<string[]>;
    platformsStore$: Observable<PlatformsStore> = this._store.select((state) => state.platforms);
    readonly availablePlatforms$: BehaviorSubject<AvailablePlatform[]> = new BehaviorSubject([]);
    readonly availablePlatforms = toSignal(this.availablePlatforms$, { initialValue: this.availablePlatforms$.value });
    isAnyPlatformConnected = false;

    connectedPlatformsAcceptingStories: Signal<Platform[] | undefined> = toSignal(
        this._platformsService.getConnectedPlatformsAcceptingStories()
    );

    constructor() {
        this._postsService.deletedPostEvent$.pipe(takeUntilDestroyed()).subscribe((deletedPostId) => {
            const stories = this.stories();
            if (!stories?.length) {
                return;
            }
            const filteredStories = this.stories().filter((p) => p._id !== deletedPostId);
            this.selectionModel.updateSelectableValues(filteredStories);
        });
    }

    ngOnInit(): void {
        this._scrollableContentService.scrollToTop();

        this.currentUser$.subscribe((user) => (this.currentUser = user));

        this.getAvailablePlatforms$().subscribe((platforms) => {
            this.availablePlatforms$.next(platforms);
            this.isAnyPlatformConnected = platforms.some((platform) => platform.connected);
        });

        this.igPictureUrl$ = this.igPlatform$.pipe(
            switchMap((platform) => {
                if (!platform) {
                    return of(null);
                }
                return this._postsService
                    .igSearch(extractIgUserNameFromIgLink(platform.socialLink) ?? '', platform._id)
                    .pipe(map((res) => res.data?.business_discovery?.profile_picture_url ?? null));
            }),
            takeUntil(this.killSubscriptions$)
        );

        this._activatedRoute.queryParamMap
            .pipe(
                map((params) => params.get('postId')),
                filter((postId): postId is string => !!postId),
                switchMap((postId) =>
                    this._postsService.getAllUnpublishedStoriesFromPostId(postId).pipe(
                        filter((res) => !!res),
                        map((res) =>
                            res.data.map((p) => {
                                const post = new Post(p);
                                post.initWorkingPic('small');
                                return post;
                            })
                        ),
                        map((posts) => posts.filter((p) => p.restaurantId === this.restaurant._id)),
                        tap((posts) => {
                            if (posts?.length) {
                                this.storiesToBeOpened$.next(posts);
                            }
                        }),
                        catchError((err): any => {
                            if (err.status === 404) {
                                this._toastService.openErrorToast(this._translate.instant('posts.posts_list.post_not_found'));
                                this._router.navigate(['./'], { relativeTo: this._activatedRoute, queryParams: null });
                                return EMPTY;
                            }
                            return throwError(() => err);
                        })
                    )
                ),
                takeUntil(this.killSubscriptions$)
            )
            .subscribe(() => {});

        this.restaurant$
            .pipe(
                switchMap((restaurant) => {
                    this.restaurant = restaurant ?? this.restaurant;
                    this.emptyPostsListAndShowLoader();
                    return this._restaurantsService.show(this.restaurant._id).pipe(map((res) => res.data));
                }),
                tap((restaurantWithManagers) => {
                    this.restaurantManagers = restaurantWithManagers?.managers.map((ru) => new User(ru.user));
                }),
                takeUntil(this.killSubscriptions$)
            )
            .subscribe({
                error: (err) => console.warn('err :>> ', err),
            });

        this.restaurant$
            .pipe(
                switchMap(() => this._store.select(selectStoriesSyncState)),
                takeUntil(this.killSubscriptions$)
            )
            .subscribe({
                next: (syncs) => {
                    const sync = syncs?.[this.restaurant?._id];
                    // if sync date is older than 1 hour, we refresh
                    if (!sync || sync < DateTime.local().minus({ hours: 1 }).toJSDate()) {
                        this.synchronize$.next();
                    } else {
                        this.pageEvent$.next(DEFAULT_PAGE_EVENT);
                    }
                },
                error: (err) => {
                    this._toastService.openErrorToast(this._httpErrorPipe.transform(err));
                },
            });

        this.synchronize$
            .pipe(
                switchMap(() =>
                    this._postsService.synchronizeStories(this.restaurant._id).pipe(
                        tap(({ data: storiesData }) => {
                            this._store.dispatch(
                                StoriesActions.editLastSyncDate({ restaurantId: this.restaurant._id, lastSyncDate: new Date() })
                            );
                            this.currentStories.set(
                                sortBy(
                                    storiesData.map((p) => new Story(p)).filter((story) => story.key === PlatformKey.INSTAGRAM),
                                    'socialCreatedAt'
                                )
                            );
                        })
                    )
                ),
                takeUntil(this.killSubscriptions$)
            )
            .subscribe({
                next: () => {
                    this.pageEvent$.next(DEFAULT_PAGE_EVENT);
                },
                error: (err) => {
                    this._toastService.openErrorToast(this._httpErrorPipe.transform(err));
                },
            });

        this.pageEvent$
            .pipe(
                tap(() => {
                    this.fetchingNextPage.set(true);
                    this.loadedMoreWithPagination = true;
                }),
                switchMap((pageEvent) => {
                    this.shouldDisplayNoMoreResultsText = false;
                    this.pagination.pageNumber = pageEvent.pageIndex;
                    this.pagination.pageSize = pageEvent.pageSize;
                    return this._postsService
                        .getRestaurantPostsPaginated(this.restaurant._id, this.pagination, {
                            ...this.filters,
                            category: PostSource.SOCIAL,
                            source: PostSource.SOCIAL,
                            isStory: true,
                        })
                        .pipe(map((res) => res.data));
                }),
                takeUntil(this.killSubscriptions$)
            )
            .subscribe({
                next: ({ posts: fetchedStoriesData, pagination }) => {
                    if (fetchedStoriesData.length === 0 && this.loadedMoreWithPagination) {
                        this.shouldDisplayNoMoreResultsText = true;
                    }
                    const fetchedStories = fetchedStoriesData.map((s) => new Story({ ...s }));
                    const currentStories = this.stories();
                    const toSet = uniqBy([...fetchedStories, ...currentStories], '_id').sort(
                        (p1, p2) => p2.getPostDate().getTime() - p1.getPostDate().getTime()
                    );
                    this.selectionModel.updateSelectableValues(toSet);

                    this.isLoadingStories.set(false);
                    this.pagination = pagination;
                    this.fetchingNextPage.set(false);
                },
                error: (err) => {
                    this.fetchingNextPage.set(false);
                    this._toastService.openErrorToast(this._httpErrorPipe.transform(err));
                },
            });

        this.storiesToBeOpened$
            .pipe(
                filter((posts) => !!posts?.length),
                map((posts) => posts.sort((a, b) => (a.plannedPublicationDate > b.plannedPublicationDate ? 1 : -1))),
                switchMap((posts) =>
                    this._customDialogService
                        .open<
                            EditStoryModalComponent,
                            {
                                posts: Post[];
                                isDisabled: boolean;
                                restaurantManagers: User[];
                            },
                            { next: ActionsAfterEditStoriesClosed }
                        >(
                            EditStoryModalComponent,
                            {
                                width: '100%',
                                height: undefined,
                                panelClass: 'malou-dialog-panel--full',
                                data: {
                                    posts,
                                    isDisabled: false,
                                    restaurantManagers: this.restaurantManagers,
                                },
                            },
                            { animateScreenSize: DialogScreenSize.ALL }
                        )
                        .afterClosed()
                        .pipe(
                            filter(isNotNil),
                            switchMap(({ next }) => {
                                this._router.navigate(['.'], { relativeTo: this._activatedRoute });
                                if (next.reload) {
                                    this.synchronize$.next();
                                }
                                return EMPTY;
                            })
                        )
                ),
                takeUntil(this.killSubscriptions$)
            )
            .subscribe({
                next: () => {},
            });

        this.filters$.pipe().subscribe((filters) => {
            this.activeFiltersCount = this._computeFilterCount(filters);
            this.filters = filters;
            this.emptyPostsListAndShowLoader();
            this.pageEvent$.next(DEFAULT_PAGE_EVENT);
        });

        this._store
            .select(selectCurrentlyPublishingPosts)
            .pipe(
                distinctUntilChanged((a, b) => isEqual(a, b)),
                takeUntil(this.killSubscriptions$)
            )
            .subscribe({
                next: (newPosts) => {
                    const newStories = newPosts.filter((post) => post.isStory);
                    if (this._areStoriesStillBeingProcessed(newStories)) {
                        const currentStories = this.stories();
                        const toSet = currentStories
                            .filter((p) => !newStories.map((currentPost) => currentPost._id).includes(p._id))
                            .concat(newStories.map((p) => new Story(p)))
                            .sort((p1, p2) => p2.getPostDate().getTime() - p1.getPostDate().getTime());
                        this.selectionModel.updateSelectableValues(toSet);
                        this.isLoadingStories.set(false);
                    } else {
                        this.synchronize$.next();
                    }
                },
            });
    }

    ngAfterViewInit(): void {
        this._scrollableContentService.moveArrow({ right: '33%' });
    }

    ngOnDestroy(): void {
        this._scrollableContentService.moveArrowToDefault();
    }

    emptyPostsListAndShowLoader(): void {
        this.selectionModel.updateSelectableValues([]);
        this.isLoadingStories.set(true);
        this.loadedMoreWithPagination = false;
    }

    onScrollDown(): void {
        this.emitPageEvent({
            pageIndex: this.pagination.pageNumber + 1,
            pageSize: this.pagination.pageSize,
            length: this.pagination.total,
        });
    }

    onEditStory(storyId: string): void {
        this._router.navigate(['./'], { queryParams: { postId: storyId }, relativeTo: this._activatedRoute });
    }

    onDeleteStory(storyId: string): void {
        this.selectionModel.unselectAll();
        this.selectionModel.select(this.stories().filter((story) => story._id === storyId));
        this.deleteSelectedStories();
    }

    isRemovable(post: Post): boolean {
        return post.published !== PostPublicationStatus.PUBLISHED;
    }

    resetFiltersDates(): void {
        this._store.dispatch({
            type: StoriesActions.resetStoriesFiltersDates.type,
        });
    }

    emitPageEvent($event: PageEvent): void {
        this.pageEvent$.next($event);
    }

    isForLater(post: PostWithJob): boolean {
        return !!post.job && !post.job?.lastRunAt;
    }

    clarifyError(error: string): string {
        if (typeof error === 'object') {
            error = JSON.stringify(error, errorReplacer);
        }
        if (!error) {
            return '';
        }
        if (error.match(/aspect ratio/)) {
            return this._translate.instant('social_posts.ratio_error');
        }
        if (error.match(/session has been invalidated/)) {
            return this._translate.instant('social_posts.reconnect_platform');
        }
        if (error.match(/An unknown error occurred/)) {
            return this._translate.instant('social_posts.facebook_error');
        }
        return error;
    }

    editPostsFilters($event: { [key: string]: any }): void {
        this._store.dispatch({ type: StoriesActions.editStoriesFilters.type, filters: $event });
    }

    resetFilters(): void {
        this._store.dispatch({ type: StoriesActions.editStoriesFilters.type, filters: initialState.filters });
    }

    synchronize(): void {
        this._store.dispatch(StoriesActions.editLastSyncDate({ lastSyncDate: null, restaurantId: this.restaurant._id }));
        this._restaurantsService.reloadSelectedRestaurant();
    }

    openSocialLink(post: Post): void {
        if (post?.socialLink) {
            window.open(post.socialLink, '_blank');
        }
    }

    createNewStory(): void {
        const malouStoryId = uuidv4();
        const platforms = this.connectedPlatformsAcceptingStories();
        if (!platforms) {
            // Can’t happen because the button to create new stories is disabled when
            // connectedPlatformsAcceptingStories() is undefined
            return;
        }
        this._postsService
            .createStory$(this.restaurant._id, {
                keys: platforms.map((p) => p.key),
                malouStoryId,
                plannedPublicationDate: DateTime.now().plus({ days: 1 }).toJSDate().toISOString(),
            })
            .subscribe({
                next: ({ data: post }) => {
                    this._router.navigate(['./'], { queryParams: { postId: post._id }, relativeTo: this._activatedRoute });
                },
                error: (err: HttpErrorResponse) => {
                    if (err.status === 404) {
                        this._toastService.openErrorToast(this._translate.instant('stories.stories_list.no_platform'));
                    } else {
                        this._toastService.openErrorToast(this._httpErrorPipe.transform(err));
                    }
                },
            });
    }

    getAvailablePlatforms$(): Observable<AvailablePlatform[]> {
        return combineLatest([this.platformsStore$, this._restaurantsService.restaurantSelected$, this.platformsSelected$]).pipe(
            filter(([platforms, restaurant]) => !!restaurant?._id && !!platforms.platformsData[restaurant._id]),
            map(([platforms, restaurant, platformSelected]) => {
                const connectedPlatforms = platforms.platformsData[(restaurant as Restaurant)._id].map((plat) => plat.key);
                return this.storiesPlatformKeys.map((p) => ({
                    key: p,
                    connected: connectedPlatforms.includes(p),
                    checked: platformSelected.includes(p),
                }));
            }),
            catchError((err) => {
                console.warn('err :>> ', err);
                return [];
            }),
            takeUntil(this.killSubscriptions$)
        );
    }

    toggleSelectedPlatforms(platform: string, currentPlatforms: AvailablePlatform[]): void {
        const platforms = currentPlatforms.map((p) => {
            if (p.key === platform) {
                p.checked = !p.checked;
            }
            return p;
        });

        this.availablePlatforms$.next(platforms);
        this._store.dispatch({
            type: StoriesActions.editStoriesFilters.type,
            filters: { ...this.filters, platforms: platforms.filter((p) => p.checked).map((p) => p.key) },
        });
    }

    emptySelectedPosts(): void {
        this.selectionModel.unselectAll();
    }

    getCombinedActionsBtnText(): string {
        const selectionCount = this.selectionModel.getCount();
        return selectionCount === 0
            ? this._translate.instant('social_posts.select')
            : this._translate.instant('social_posts.combined_actions', { number: selectionCount });
    }

    getCombinedActionsBtnTextSmall(): string {
        const selectionCount = this.selectionModel.getCount();
        return `(${selectionCount})`;
    }

    storyChecked(checked: boolean, story: Story): void {
        if (checked) {
            this.selectionModel.select(story);
        } else {
            this.selectionModel.unselect(story);
        }
    }

    deleteSelectedStories(): void {
        // why use selection instead of just toSelect ? v2 needs to refactor this
        const toSelect = this.selectionModel.getSelection().filter((p) => this._canStoryBeDeleted(p));
        this.selectionModel.unselectAll();
        this.selectionModel.select(toSelect);
        if (this.selectionModel.getCount() === 0) {
            this._toastService.openErrorToast(this._translate.instant('stories.cannot_delete_published_stories'));
            return;
        }
        this._dialogService.open({
            title: this._translate.instant('stories.delete_stories'),
            message: this._translate.instant('stories.want_delete_stories'),
            variant: DialogVariant.INFO,
            primaryButton: {
                label: this._translate.instant('common.delete'),
                action: () => {
                    const selection = this.selectionModel.getSelection();
                    forkJoin(
                        selection
                            .filter((story) => story.published === PostPublicationStatus.PENDING)
                            .map((story) => this._postsService.cancelStory$({ malouStoryId: story.malouStoryId }))
                    )
                        .pipe(
                            defaultIfEmpty(null),
                            switchMap(() => this._postsService.deletePosts(selection.map((p) => p._id)))
                        )
                        .subscribe({
                            next: () => {
                                const toSet = this.stories().filter(
                                    (p) =>
                                        !this.selectionModel
                                            .getSelection()
                                            .map((s) => s._id)
                                            .includes(p._id)
                                );
                                this.selectionModel.updateSelectableValues(toSet);
                                this.selectionModel.unselectAll();
                                this._toastService.openSuccessToast(this._translate.instant('stories.stories_deleted'));
                            },
                            error: (err) => {
                                console.warn('err :>> ', err);
                                this.selectionModel.unselectAll();
                                this._toastService.openErrorToast(this._httpErrorPipe.transform(err));
                            },
                        });
                },
            },
            secondaryButton: {
                label: this._translate.instant('common.cancel'),
            },
        });
    }

    private _computeFilterCount(postsFilters: PostsFilters): number {
        const isDefaultPeriod = postsFilters.period === DEFAULT_PERIOD;
        const isDefaultStatuses = postsFilters.publicationStatus?.length === defaultPublicationStatus.length;
        const isDefaultPlatforms = postsFilters.platforms?.length === getPlatformKeysWithStories().length;
        return [!isDefaultPeriod, !isDefaultStatuses, !isDefaultPlatforms].filter(Boolean).length;
    }

    private _areStoriesStillBeingProcessed(posts: Post[]): boolean {
        return posts.some((story) => story.published === PostPublicationStatus.PENDING);
    }

    private _canStoryBeDeleted(post: Story): boolean {
        return post.published !== PostPublicationStatus.PUBLISHED || canDeleteStory(post.key);
    }

    onDuplicateStories(destination: DuplicationDestination, stories: Story[]): void {
        this._duplicateStoriesService.duplicateStories(
            destination,
            stories,
            this.restaurant._id,
            (duplicatedStories) => this._onDuplicationSuccess(duplicatedStories),
            (err) => this._onDuplicationError(err)
        );
    }

    private _onDuplicationSuccess(duplicatedStories: Story[]): void {
        if (duplicatedStories.length) {
            this._toastService.openSuccessToast(this._translate.instant('stories.stories_duplicated'));
            const toSet = this.stories()
                .concat(duplicatedStories.filter((story) => story.restaurantId === this.restaurant._id))
                .sort((p1, p2) => p2.getPostDate().getTime() - p1.getPostDate().getTime());
            this.selectionModel.updateSelectableValues(toSet);
        }
        this.selectionModel.unselectAll();
    }

    private _onDuplicationError = (error: unknown): void => {
        console.warn('err :>>', error);
        this.selectionModel.unselectAll();
        this._toastService.openErrorToast(this._translate.instant('stories.duplication_failed'));
    };
}
