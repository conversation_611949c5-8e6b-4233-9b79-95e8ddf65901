#!/bin/bash

dir="./src/app"

# Use find to locate all SCSS files under the directory
files=$(find "$dir" -type f -name "*.scss")
# Loop through each file
for file in $files
do
  # Use grep to check if the file contains an scss variable like "$malou-*"
  
  # check if grep<PERSON><PERSON><PERSON> is not empty
#   if grep -q "\$malou-" "$file"
#   then
#     import=""
#     # then insert this line at the top of the file @import 'src/styles/malou/_malou_variables.scss';
#     sed -i '' '1i\
# @import "src/styles/malou/_malou_variables.scss";
# ' "$file"
#     echo 'nice'
  if grep -q "\@extend \.malou-spin" "$file"
  then
    import=""
    # then insert this line at the top of the file @import 'src/styles/malou/_malou_variables.scss';
    sed -i '' '1i\
@import "src/styles/malou/_malou_animations.scss";
' "$file"
    echo "nice: $file"
  fi

done
