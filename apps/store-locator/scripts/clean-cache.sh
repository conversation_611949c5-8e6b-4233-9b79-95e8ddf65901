#!/bin/bash

echo "🔍 Analyzing cache..."

# Count before cleanup
TOTAL_IMAGES_BEFORE=$(find node_modules/.astro -type f \( -name "*.webp" -o -name "*.avif" -o -name "*.jpg" -o -name "*.png" \) 2>/dev/null | wc -l)
TOTAL_JSON_BEFORE=$(find node_modules/.astro -type f -name "*.json" 2>/dev/null | wc -l)
SIZE_BEFORE=$(du -sh node_modules/.astro 2>/dev/null | cut -f1)

echo "Before cleanup:"
echo "🏞️ Images: $TOTAL_IMAGES_BEFORE"
echo "📊 Metadata files: $TOTAL_JSON_BEFORE"
echo "📁 Total size: $SIZE_BEFORE"

# Get list of images referenced in built HTML/CSS/JS
echo "🔍 Get list of generated images..."
USED_IMAGES=$(grep -roh '/_astro/[^"'\'']*\.\(webp\|avif\|jpg\|png\)' dist/ 2>/dev/null | sed 's|/_astro/||g' | sort -u || true)

# Navigate to cache directory
cd node_modules/.astro

DELETED_IMAGES=0
DELETED_JSON=0

# Delete unused image files
echo "🗑️ Deleting unused images in cache..."
for cached_file in $(find . -type f \( -name "*.webp" -o -name "*.avif" -o -name "*.jpg" -o -name "*.png" \)); do
    filename=$(basename "$cached_file")

    if ! echo "$USED_IMAGES" | grep -q "$filename"; then
        rm "$cached_file"
        DELETED_IMAGES=$((DELETED_IMAGES + 1))
        
        # Also delete corresponding .json metadata file
        json_file="${cached_file}.json"
        if [ -f "$json_file" ]; then
            rm "$json_file"
            DELETED_JSON=$((DELETED_JSON + 1))
        fi
    fi
done

# Clean empty directories
find . -type d -empty -delete 2>/dev/null || true

cd ../..

# Count after cleanup
TOTAL_IMAGES_AFTER=$(find node_modules/.astro -type f \( -name "*.webp" -o -name "*.avif" -o -name "*.jpg" -o -name "*.png" \) 2>/dev/null | wc -l)
TOTAL_JSON_AFTER=$(find node_modules/.astro -type f -name "*.json" 2>/dev/null | wc -l)
SIZE_AFTER=$(du -sh node_modules/.astro 2>/dev/null | cut -f1)

echo ""
echo "🧹 Cleanup complete!"
echo "🏞️ Deleted images: $DELETED_IMAGES"
echo "📊 Deleted metadata: $DELETED_JSON"
echo ""
echo "After cleanup:"
echo "🏞️ Images: $TOTAL_IMAGES_AFTER"
echo "📊 Metadata files: $TOTAL_JSON_AFTER"
echo "📁 Total size: $SIZE_AFTER"
