/**
    Custom styles for the map component
**/

.ripple-loader {
    position: absolute;
    width: 250px;
    height: 250px;
    background: rgba(0, 123, 255, 0.4);
    border-radius: 50%;
    animation: ripple-loader 1.8s infinite ease-out;
    opacity: 0;
}

/* Stagger delays for multiple waves */
.ripple-loader:nth-child(1) {
    animation-delay: 0.7s;
    opacity: 0;
}
.ripple-loader:nth-child(2) {
    animation-delay: 1.2s;
    opacity: 0;
}
.ripple-loader:nth-child(3) {
    animation-delay: 1.8s;
    opacity: 0;
}

@keyframes ripple-loader {
    0% {
        transform: scale(0.3);
        opacity: 1;
    }
    100% {
        transform: scale(3);
        opacity: 0;
    }
}

#zoom-warning.visible {
    opacity: 1;
}
