export enum AgendaJobName {
    AUTO_REPLY_TO_REVIEW = 'auto reply to review',
    CHECK_IF_WRONG_ERROR_AND_UPDATE_ACCORDINGLY = 'check if wrong error and update accordingly',
    CLEAN_CATEGORIES = 'CLEAN_CATEGORIES',
    DAILY_SEND_UPCOMING_HOLIDAYS_EMAIL_NOTIFICATIONS = 'daily send upcoming holidays email notifications',
    DAILY_SEND_UPCOMING_HOLIDAYS_WEB_NOTIFICATIONS = 'daily send upcoming holidays web notifications',
    DAILY_SEND_UPCOMING_POSTS_SUGGESTION_EMAIL_NOTIFICATIONS = 'daily send upcoming posts suggestion email notifications',
    DAILY_SEND_UPCOMING_POSTS_SUGGESTION_WEB_NOTIFICATIONS = 'daily send upcoming posts suggestion web notifications',
    FETCH_AND_MATCH_FB_POST_TIMED_OUT = 'fetch and match fb post timed out',
    FETCH_MESSAGES = 'fetch messages',
    FETCH_POST_AND_CHECK_ERRORS = 'fetch post and check errors',
    INCREMENT_GIFT_STOCKS_FOR_EXPIRED_DRAWS = 'increment gift stocks for expired draws',
    INSERT_DAILY_FOLLOWERS = 'insert daily followers',
    INSERT_WEEKLY_PLATFORM_RATINGS = 'insert weekly platform ratings',
    MATCH_REVIEW_TO_SCAN = 'MATCH_REVIEW_TO_SCAN',
    PUBLISH_POST = 'publish post',
    PREPARE_POST = 'prepare post',
    INITIALIZE_STORY = 'initialize story',
    PREPARE_STORY = 'prepare story',
    PUBLISH_POST_ON_PLATFORM = 'PUBLISH_POST_ON_PLATFORM',
    PUBLISH_STORY = 'publish story',
    RESET_RESTAURANTS_AI_CREDITS = 'reset restaurants ai credits',
    RETRY_REPLY_TO_REVIEW = 'retry reply to review',
    SAVE_BOOKMARKED_POST = 'save bookmarked post',
    SEND_DAILY_REVIEWS_REPORTS = 'send daily reviews reports',
    SEND_DAILY_UNREAD_MESSAGES_NOTIFICATIONS = 'send daily unread messages notifications',
    SEND_FEEDBACK_NOTIFICATION_EMAIL = 'send feedback notification email',
    SEND_GIFT_EXPIRES_SOON_EMAILS = 'send gift expires soon emails',
    SEND_INFORMATION_UPDATES = 'SEND_INFORMATION_UPDATES',
    SEND_MAPSTR_REMINDER_EMAIL = 'send mapstr reminder email',
    SEND_WHEEL_OF_FORTUNE_LIVE_TOMORROW_EMAIL = 'send wheel of fortune live tomorrow email',
    // TODO: delete this line when feature toggle 'release-new-semantic-analysis' is removed
    START_REVIEWS_ANALYSES = 'start reviews analyses',
    UPDATE_ALL_REVIEWS = 'update all reviews',
    UPDATE_LATEST_REVIEWS = 'update latest reviews',
    UPDATE_PLATFORM_COMMENTS = 'update platform comments',
    UPDATE_ROI_ACTIVATION = 'update roi activation',
    UPDATE_WEEKLY_GEOSAMPLES = 'update weekly geosamples',
    UPDATE_WHEEL_OF_FORTUNE_TOTEMS_REDIRECTION = 'update wheel of fortune totems redirection',
    UPDATE_YEXT_ADD_REQUEST_STATUS = 'update yext add request status',
    YEXT_DELETE_LOCATION = 'yext delete location',
    WEEKLY_SQUAD_REACH_STATS_FOR_PRODUCT_MANAGERS = 'weekly squad reach stats for product managers',
    WEEKLY_SEND_NOTIFICATIONS_SUMMARY = 'weekly send notifications summary',
    DAILY_CHECK_PLATFORM_CONNECTION_STATE = 'daily check platform connection state',
    STORE_LOCATOR_DEPLOYMENT = 'STORE_LOCATOR_DEPLOYMENT',
    STORE_LOCATOR_CONTENT_GENERATION = 'STORE_LOCATOR_CONTENT_GENERATION',
    STORE_LOCATOR_PUBLICATION = 'STORE_LOCATOR_PUBLICATION',
    DEPLOY_ALL_STORE_LOCATORS = 'DEPLOY_ALL_STORE_LOCATORS',
    GENERATE_NORMALIZED_VIDEO = 'GENERATE_NORMALIZED_VIDEO',
    MONTHLY_RETRY_YEXT_CANCELED_ADD_REQUESTS = 'MONTHLY_RETRY_YEXT_CANCELED_ADD_REQUESTS',
    NOTIFY_EXPERIMENTATION_FEATURE_LIST = 'NOTIFY_EXPERIMENTATION_FEATURE_LIST',
    EXTEND_SEVENROOMS_CREDENTIAL_EXPIRATION = 'EXTEND_SEVENROOMS_CREDENTIAL_EXPIRATION',
    EXTEND_DOORDASH_CREDENTIAL = 'EXTEND_DOORDASH_CREDENTIAL',
    TOPIC_PRUNING = 'TOPIC_PRUNING',
    PUBLISH_STORY_ON_PLATFORM = 'PUBLISH_STORY_ON_PLATFORM',
    INITIALIZE_RESTAURANTS_THE_FORK_CLIENTS = 'INITIALIZE_RESTAURANTS_THE_FORK_CLIENTS',
}
