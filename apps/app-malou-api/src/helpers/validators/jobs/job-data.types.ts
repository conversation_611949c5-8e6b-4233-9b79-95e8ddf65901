import { z } from 'zod';

import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { locationDeletionValidator } from ':helpers/validators/jobs/location-deletion.job.validator';
import { updateYextAddRequestStatusValidator } from ':helpers/validators/jobs/update-yext-add-request-status.job.validator';
import { generateNormalizedVideoValidator } from ':modules/media/jobs/generate-normalized-video.job';
import { publishPostOnPlatformValidator } from ':modules/posts/jobs/publish-post-on-platform';
import { deployStoreLocatorValidator } from ':modules/store-locator/jobs/deploy-store-locator';
import { generateContentStoreLocatorAttributesValidator } from ':modules/store-locator/jobs/generate-store-locator-content';
import { publishStoreLocatorAttributesValidator } from ':modules/store-locator/jobs/publish-store-locator';

import {
    checkIfWrongErrorAndUpdateAccordinglyValidator,
    fetchAndMatchFbPostTimedOutValidator,
    fetchPostAndCheckErrorsValidator,
    matchReviewToScanValidator,
    preparePostValidator,
    publishStoryOnPlatformValidator,
    sendWofLiveTomorrowEmailValidator,
} from './posts-jobs.validators';
import { autoReplyToReviewValidator, retryReplyToReviewValidator } from './reviews-jobs.validator';

export interface AgendaJobData extends Record<AgendaJobName, any> {
    [AgendaJobName.PREPARE_POST]: z.output<typeof preparePostValidator>;
    [AgendaJobName.FETCH_POST_AND_CHECK_ERRORS]: z.infer<typeof fetchPostAndCheckErrorsValidator>;
    [AgendaJobName.CHECK_IF_WRONG_ERROR_AND_UPDATE_ACCORDINGLY]: z.infer<typeof checkIfWrongErrorAndUpdateAccordinglyValidator>;
    [AgendaJobName.FETCH_AND_MATCH_FB_POST_TIMED_OUT]: z.infer<typeof fetchAndMatchFbPostTimedOutValidator>;
    [AgendaJobName.PUBLISH_POST_ON_PLATFORM]: z.infer<typeof publishPostOnPlatformValidator>;
    [AgendaJobName.AUTO_REPLY_TO_REVIEW]: z.infer<typeof autoReplyToReviewValidator>;
    [AgendaJobName.RETRY_REPLY_TO_REVIEW]: z.infer<typeof retryReplyToReviewValidator>;
    [AgendaJobName.SAVE_BOOKMARKED_POST]: any;
    [AgendaJobName.UPDATE_PLATFORM_COMMENTS]: any;
    [AgendaJobName.SEND_FEEDBACK_NOTIFICATION_EMAIL]: any;
    [AgendaJobName.INSERT_DAILY_FOLLOWERS]: any;
    [AgendaJobName.INSERT_WEEKLY_PLATFORM_RATINGS]: any;
    [AgendaJobName.UPDATE_WEEKLY_GEOSAMPLES]: any;
    [AgendaJobName.FETCH_MESSAGES]: any;
    [AgendaJobName.SEND_DAILY_UNREAD_MESSAGES_NOTIFICATIONS]: any;
    [AgendaJobName.INITIALIZE_STORY]: any;
    [AgendaJobName.PREPARE_STORY]: any;
    [AgendaJobName.PUBLISH_STORY]: any;
    [AgendaJobName.SEND_DAILY_REVIEWS_REPORTS]: any;
    // TODO: delete this line when feature toggle 'release-new-semantic-analysis' is removed
    [AgendaJobName.START_REVIEWS_ANALYSES]: any;
    [AgendaJobName.UPDATE_ALL_REVIEWS]: any;
    [AgendaJobName.UPDATE_LATEST_REVIEWS]: any;
    [AgendaJobName.SEND_WHEEL_OF_FORTUNE_LIVE_TOMORROW_EMAIL]: z.infer<typeof sendWofLiveTomorrowEmailValidator>;
    [AgendaJobName.UPDATE_WHEEL_OF_FORTUNE_TOTEMS_REDIRECTION]: any;
    [AgendaJobName.SEND_GIFT_EXPIRES_SOON_EMAILS]: any;
    [AgendaJobName.INCREMENT_GIFT_STOCKS_FOR_EXPIRED_DRAWS]: any;
    [AgendaJobName.MATCH_REVIEW_TO_SCAN]: z.infer<typeof matchReviewToScanValidator>;
    [AgendaJobName.UPDATE_YEXT_ADD_REQUEST_STATUS]: z.infer<typeof updateYextAddRequestStatusValidator>;
    [AgendaJobName.YEXT_DELETE_LOCATION]: z.infer<typeof locationDeletionValidator>;
    [AgendaJobName.SEND_MAPSTR_REMINDER_EMAIL]: z.infer<typeof locationDeletionValidator>;
    [AgendaJobName.STORE_LOCATOR_DEPLOYMENT]: z.infer<typeof deployStoreLocatorValidator>;
    [AgendaJobName.STORE_LOCATOR_CONTENT_GENERATION]: z.infer<typeof generateContentStoreLocatorAttributesValidator>;
    [AgendaJobName.STORE_LOCATOR_PUBLICATION]: z.infer<typeof publishStoreLocatorAttributesValidator>;
    [AgendaJobName.GENERATE_NORMALIZED_VIDEO]: z.infer<typeof generateNormalizedVideoValidator>;
    [AgendaJobName.MONTHLY_RETRY_YEXT_CANCELED_ADD_REQUESTS]: any;
    [AgendaJobName.NOTIFY_EXPERIMENTATION_FEATURE_LIST]: any;
    [AgendaJobName.EXTEND_SEVENROOMS_CREDENTIAL_EXPIRATION]: any;
    [AgendaJobName.EXTEND_DOORDASH_CREDENTIAL]: any;
    [AgendaJobName.PUBLISH_STORY_ON_PLATFORM]: z.infer<typeof publishStoryOnPlatformValidator>;
    [AgendaJobName.INITIALIZE_RESTAURANTS_THE_FORK_CLIENTS]: any;
}
