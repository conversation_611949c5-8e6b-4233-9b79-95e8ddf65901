import 'reflect-metadata';

import ':env';

import { container, singleton } from 'tsyringe';

import { PlatformKey, platformsKeys, yextPublishers } from '@malou-io/package-utils';

import ':plugins/db';
import { GoogleSheetsService } from ':services/google-sheets/google-sheets.service';

@singleton()
class CreatePlatformUpdatesDocumentTask {
    private readonly _GSHEET_ID = '1hPQtwJFw1JXFsjkVB0ZQRHlHYZLoWBUhWmJvonUUqaE';

    constructor(private readonly _googleSheetsService: GoogleSheetsService) {}

    async execute() {
        const googleDoc = await this._googleSheetsService.loadGoogleSheet(this._GSHEET_ID);
        let googleSheet = googleDoc.sheetsByTitle['Platforms2'];
        if (!googleSheet) {
            googleSheet = await googleDoc.addSheet({
                title: 'Platforms2',
                headerValues: ['name', 'connection', 'maj', 'fields', 'delay', 'oauth'],
            });
        }

        const rows: { name: string; connection: string; maj: string; fields: string; delay: string; oauth: string }[] = [];

        // Handle Malou platforms
        for (const platformDefinition of Object.values(platformsKeys).filter((platform) => platform.shouldCompareInformation)) {
            const updatedFields = platformDefinition.informationSent.sort((a, b) => a.localeCompare(b)).join(', ');

            const platformType = this._getPlatformType(platformDefinition.key);
            const connection = this._getConnectionType(platformType);
            const maj = platformType === 'auto' ? 'API' : 'Manuel (Ops Malou)';

            const name = platformDefinition.yextPublisherId
                ? yextPublishers[platformDefinition.yextPublisherId]?.name
                : platformDefinition.fullName;

            rows.push({
                name,
                connection,
                maj,
                fields: updatedFields,
                delay: platformDefinition.updateTime ?? 'n/a',
                oauth: platformDefinition.oauth === true ? 'yes' : 'no',
            });
        }

        // Handle Yext platforms not already handled
        for (const yextPublisher of Object.entries(yextPublishers).map(([, publisher]) => publisher)) {
            const fields = Object.entries(yextPublisher.updateFields)
                .filter(([key, value]) => value)
                .map(([key]) => key)
                .sort((a, b) => a.localeCompare(b));

            if (fields.length === 0) {
                continue;
            }

            rows.push({
                name: yextPublisher.name,
                connection: 'Via plateforme',
                maj: 'Yext',
                fields: fields.join(', '),
                delay: yextPublisher.updateTime ?? 'n/a',
                oauth: 'no',
            });
        }

        rows.sort((a, b) => a.name.localeCompare(b.name));

        await googleSheet.clearRows();
        await googleSheet.addRows(rows);
    }

    private _getPlatformType(platformKey: PlatformKey): 'auto' | 'credentials' | 'manual' {
        if (
            [
                PlatformKey.FACEBOOK,
                PlatformKey.GMB,
                PlatformKey.UBEREATS,
                PlatformKey.FOURSQUARE,
                PlatformKey.OPENTABLE,
                PlatformKey.ABC,
            ].includes(platformKey)
        ) {
            return 'auto';
        }

        if ([PlatformKey.LAFOURCHETTE].includes(platformKey)) {
            return 'credentials';
        }

        return 'manual';
    }

    private _getConnectionType(platformType: string): string {
        if (platformType === 'auto') {
            return 'Via plateforme';
        }

        if (platformType === 'credentials') {
            return 'Via identifiants';
        }

        return 'Via <EMAIL>';
    }
}

const task = container.resolve(CreatePlatformUpdatesDocumentTask);

task.execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error('err :>>', error);
        process.exit(1);
    });
