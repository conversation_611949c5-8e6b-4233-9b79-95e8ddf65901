import 'reflect-metadata';

import ':env';

import { Locale } from '@malou-io/package-utils';

import { AWSSESEmailTemplateName } from ':helpers/enums/AWS-SES-email-template.enum';
import { getAWSSESEmailTemplateName } from ':helpers/utils';
import ':modules/mailing/handlebars-registers';
import { getVerificationEmailHTML } from ':modules/mailing/review-booster/use-cases';
import { createVerificationEmailInSES } from ':plugins/ses';

async function createVerificationEmail(lang: Locale): Promise<void> {
    const { emailHTML, subject } = getVerificationEmailHTML(lang);
    const emailHTMLcleansedForSES = emailHTML
        .replace(/<a\b[^>]*>[\s\S]*?<\/a>/gi, '') // Remove <a> tags and their contents
        .replace(/<meta\b[^>]*>/gi, '') // Remove <meta> tags
        .replace(/<style\b[^>]*>[\s\S]*?<\/style>/gi, '')
        .replace(/\s*(?:border|role|cellpadding|cellspacing|data-id|align)\s*=\s*("[^"]*"|'[^']*'|[^>\s]*)/gi, '');
    const templateName = getAWSSESEmailTemplateName(`${AWSSESEmailTemplateName.VERIFICATION_EMAIL}_${lang}`);
    const from = lang === Locale.FR ? "L'équipe Malou" : 'Malou team';
    const createdEmailTemplate = await createVerificationEmailInSES(templateName, emailHTMLcleansedForSES, subject, from);
    console.log('Verification email template created', createdEmailTemplate);
}

async function main(): Promise<void> {
    for (const lang of ['fr', 'en', 'it', 'es']) {
        await createVerificationEmail(lang as Locale);
    }
}

main()
    .then(() => process.exit(0))
    .catch((e) => {
        console.error('e :>> ', e);
        process.exit(1);
    });
