/* Use this task as a playground to run stuff using any app logic.
You can also just copy paste to another file to keep track of previous run tasks */
import 'reflect-metadata';

import ':env';

import { container, singleton } from 'tsyringe';

import { AgendaSingleton } from ':helpers/classes/agenda-singleton';
import { CreateNewReviewsNotificationUseCase } from ':modules/notifications/use-cases/create-new-reviews-notification/create-new-reviews-notification.use-case';
import ':plugins/db';

@singleton()
class DefaultTask {
    constructor(
        private readonly _agendaSingleton: AgendaSingleton,
        private readonly _createNewReviewsNotificationUseCase: CreateNewReviewsNotificationUseCase
    ) {}

    async execute() {
        await this._createNewReviewsNotificationUseCase.execute(['683a74734c3025cd669c0c09'], '5f05ea004af60fbf3e16e23d');
        // await this._agendaSingleton.now(AgendaJobName.STORE_LOCATOR_DEPLOYMENT, {
        //     configurationId: '',
        //     status: StoreLocatorJobStatus.PENDING,
        // });
    }
}

const task = container.resolve(DefaultTask);
task.execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error('err :>>', error);
        process.exit(1);
    });
