import 'reflect-metadata';

import { Agenda } from 'agenda';
import { randomUUID } from 'crypto';
import { autoInjectable, container } from 'tsyringe';
import { z } from 'zod';

import { PostModel, toDbId } from '@malou-io/package-models';

import '../../env';
import { AgendaJobName } from '../../helpers/enums/agenda-job-name.enum';
import '../../plugins/db';

const validator = z.array(z.object({ _id: z.string(), restaurantIds: z.array(z.string()).min(2) }));
type Res = z.infer<typeof validator>;

@autoInjectable()
class DeDuplicateMalouStoryIdsTask {
    constructor() {}

    async updateJobsWithNewMalouStoryId(params: { restaurantId: string; oldMalouStoryId: string; newMalouStoryId: string }): Promise<void> {
        const agenda = await AgendaSingleton.getInstance();
        const agendaCollection = agenda._collection;
        const update = await agendaCollection.updateMany(
            {
                name: { $in: [AgendaJobName.INITIALIZE_STORY, AgendaJobName.PREPARE_STORY] },
                'data.restaurantId': params.restaurantId,
                'data.malouStoryId': params.oldMalouStoryId,
            },
            {
                $set: { 'data.malouStoryId': params.newMalouStoryId },
            }
        );
        if (!update.acknowledged) {
            console.log('NOT acknowledged');
        }
        if (update.modifiedCount) {
            console.log('MODIFIED update.modifiedCount', {
                modifiedCount: update.modifiedCount,
                oldMalouStoryId: params.oldMalouStoryId,
                newMalouStoryId: params.newMalouStoryId,
                restaurantId: params.restaurantId,
            });
        }
    }

    async updateStories(params: { restaurantId: string; oldMalouStoryId: string; newMalouStoryId: string }): Promise<void> {
        const update = await PostModel.updateMany(
            { malouStoryId: params.oldMalouStoryId, restaurantId: toDbId(params.restaurantId) },
            { malouStoryId: params.newMalouStoryId }
        );
        if (!update.acknowledged) {
            console.log('NOT acknowledged');
        }
        if (update.modifiedCount) {
            console.log('MODIFIED update.modifiedCount', {
                modifiedCount: update.modifiedCount,
                oldMalouStoryId: params.oldMalouStoryId,
                newMalouStoryId: params.newMalouStoryId,
                restaurantId: params.restaurantId,
            });
        }
    }

    async createNewMalouStoryIds(element: Res[number]): Promise<void> {
        for (const restaurantId of element.restaurantIds) {
            const newMalouStoryId = randomUUID();
            await this.updateJobsWithNewMalouStoryId({ restaurantId, newMalouStoryId, oldMalouStoryId: element._id });
            await this.updateStories({ restaurantId, newMalouStoryId, oldMalouStoryId: element._id });
        }
    }

    async execute() {
        const pipeline = [
            { $match: { malouStoryId: { $ne: null } } }, // malouStoryId is indexed and returns approx 33k docs
            { $group: { _id: '$malouStoryId', restaurantIds: { $addToSet: { $toString: '$restaurantId' } } } },
            { $match: { restaurantIds: { $not: { $size: 1 } } } },
        ];
        const resRaw = await PostModel.aggregate(pipeline);
        const res = validator.parse(resRaw);

        console.log('RES LENGTH', res.length);

        for (const element of res) {
            console.log('Processing malou story id: ', element._id);
            await this.createNewMalouStoryIds(element);
        }
    }
}

class AgendaSingleton {
    private static _instance: Agenda | undefined;
    static async getInstance(): Promise<Agenda> {
        if (!AgendaSingleton._instance) {
            AgendaSingleton._instance = await AgendaSingleton._init();
        }
        return AgendaSingleton._instance;
    }

    private static async _init(): Promise<Agenda> {
        return new Promise((resolve, reject) => {
            const connectionOpts = { db: { address: process.env.MONGODB_AGENDA_URI, collection: 'agendaJobs' } };
            const agenda = new Agenda(connectionOpts);
            agenda.on('ready', async function () {
                resolve(agenda);
            });
            agenda.on('error', async function (err) {
                reject(new Error('error agenda'));
            });
        });
    }
}

const task = container.resolve(DeDuplicateMalouStoryIdsTask);
task.execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error('err :>>', error);
        process.exit(1);
    });
