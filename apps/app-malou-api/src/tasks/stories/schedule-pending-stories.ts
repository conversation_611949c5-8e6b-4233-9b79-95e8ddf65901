import 'reflect-metadata';

import ':env';

import { autoInjectable, container } from 'tsyringe';
import { z } from 'zod';

import { PostModel } from '@malou-io/package-models';
import { PlatformKey } from '@malou-io/package-utils';

import PostsUseCases from ':modules/posts/posts.use-cases';
import ':plugins/db';

const validator = z.array(z.object({ _id: z.string(), keys: z.array(z.array(z.nativeEnum(PlatformKey))) }));
type Res = z.infer<typeof validator>;

@autoInjectable()
class SchedulePendingStories {
    constructor(private readonly _postsUseCases: PostsUseCases) {}

    async scheduleStory(params: { malouStoryId: string; keysArray: PlatformKey[][] }): Promise<void> {
        const keys = Array.from(new Set(params.keysArray.flat()));
        console.log('keys', keys, 'malouStoryId', params.malouStoryId);
        await this._postsUseCases.schedulePrepareStory(keys, params.malouStoryId);
    }

    async execute() {
        const pipeline = [
            {
                $match: {
                    published: 'pending',
                    isStory: true,
                    plannedPublicationDate: {
                        $gte: new Date(),
                    },
                },
            },
            {
                $group: {
                    _id: '$malouStoryId',
                    keys: {
                        $push: '$keys',
                    },
                },
            },
        ];
        const resRaw = await PostModel.aggregate(pipeline);
        const res = validator.parse(resRaw);

        console.log('RES LENGTH', res.length);

        for (const element of res) {
            await this.scheduleStory({ keysArray: element.keys, malouStoryId: element._id });
        }
    }
}

const task = container.resolve(SchedulePendingStories);
task.execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error('err :>>', error);
        process.exit(1);
    });
