import { Job } from 'agenda';
import { container } from 'tsyringe';

import { AgendaSingleton } from ':helpers/classes/agenda-singleton';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { StoriesRepository } from ':modules/stories/repository/stories.repository';
import { ScheduleStoryPublicationService } from ':modules/stories/services/schedule-story-publication.service';

/**
 * This job was used to cancel v1 jobs and create v2 jobs.
 * Don't know why but the v1 jobs are not deleted from the database, so delete them manually.
 */
async function main() {
    const agenda = container.resolve(AgendaSingleton);
    const scheduleStoryPublicationService = container.resolve(ScheduleStoryPublicationService);
    const storiesRepository = container.resolve(StoriesRepository);
    const jobs: Job<{ malouStoryId: string; restaurantId: string }>[] = await agenda.jobs({ name: AgendaJobName.INITIALIZE_STORY });

    for (const job of jobs) {
        console.log('Processing job', job.attrs._id?.toString());
        const malouStoryId = job.attrs?.data?.malouStoryId;
        if (!malouStoryId) {
            console.log('No malouStoryId', job.attrs._id?.toString());
            continue;
        }
        const story = await storiesRepository.findOne({ filter: { malouStoryId } });
        if (!story) {
            console.log('No story found for malouStoryId', malouStoryId, 'for job', job.attrs._id?.toString());
            continue;
        }

        if (!story.plannedPublicationDate) {
            console.log('No plannedPublicationDate for story', story._id?.toString(), 'for job', job.attrs._id?.toString());
            continue;
        }
        await scheduleStoryPublicationService.scheduleStoryPublication(story._id.toString(), story.plannedPublicationDate);
    }
}

main()
    .then(() => console.log('Success'))
    .catch((err) => console.error('Error', err));
