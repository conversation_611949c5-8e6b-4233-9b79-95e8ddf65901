import { EntityConstructor } from '@malou-io/package-utils';

import { TheForkReservation } from ':modules/clients/provider-clients/providers/thefork/thefork.interfaces';

export type ProviderVisitProps = EntityConstructor<ProviderVisit>;

export class ProviderVisit {
    providerVisitId: string;
    restaurantId: string;
    visitDate: Date;
    providerVisitFields: any;

    constructor(props: ProviderVisitProps) {
        this.providerVisitId = props.providerVisitId;
        this.restaurantId = props.restaurantId;
        this.visitDate = props.visitDate;
        this.providerVisitFields = props.providerVisitFields;
    }

    static fromTheForkVisit(data: TheForkReservation, restaurantId: string): ProviderVisit {
        return new ProviderVisit({
            providerVisitId: data.reservationUuid,
            restaurantId,
            visitDate: new Date(data.mealDate),
            providerVisitFields: data,
        });
    }
}
