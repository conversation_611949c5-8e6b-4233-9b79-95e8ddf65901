import { singleton } from 'tsyringe';

import { Platform<PERSON>ey, ProviderClientSource } from '@malou-io/package-utils';

@singleton()
export class ProviderClientsMapper {
    fromPlatformKeyToProviderClientSource(platformKey: PlatformKey): ProviderClientSource | undefined {
        return {
            [PlatformKey.COMO]: ProviderClientSource.COMO,
            [PlatformKey.LAFOURCHETTE]: ProviderClientSource.LAFOURCHETTE,
            [PlatformKey.ZENCHEF]: ProviderClientSource.ZENCHEF,
            [PlatformKey.PRIVATE]: ProviderClientSource.MALOU, // TODO CRM - is MALOU always ok or should we check for WHEEL_OF_FORTUNE ?
        }[platformKey];
    }
}
