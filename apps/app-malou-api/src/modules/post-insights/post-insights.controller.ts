import { NextFunction, Request, Response } from 'express';
import { singleton } from 'tsyringe';

import {
    GetPostInsightsBodyDto,
    getPostInsightsBodyValidator,
    GetPostInsightsParamsDto,
    getPostInsightsParamsValidator,
    PostInsightsDto,
} from '@malou-io/package-dto';
import { ApiResult } from '@malou-io/package-utils';

import { Body, Params } from ':helpers/decorators/validators';

import { postInsightsDtoMapper } from './post-insights.mapper.dto';
import { PostInsightsUseCases } from './post-insights.use-cases';

@singleton()
export class PostInsightsController {
    constructor(
        private _postInsightsUseCases: PostInsightsUseCases,
        private _postInsightsDtoMapper: postInsightsDtoMapper
    ) {}

    @Params(getPostInsightsParamsValidator)
    @Body(getPostInsightsBodyValidator)
    async handleGetPlatformStoriesInsights(
        req: Request<GetPostInsightsParamsDto, any, GetPostInsightsBodyDto>,
        res: Response<ApiResult<PostInsightsDto[]>>,
        next: NextFunction
    ) {
        try {
            const { restaurantId, platformKey } = req.params;
            const { socialIds } = req.body;

            const insights = await this._postInsightsUseCases.getStoriesInsights(restaurantId, platformKey, socialIds);
            const data = insights.map((insight) => this._postInsightsDtoMapper.toPostInsightsDto(insight));

            return res.json({ msg: 'insights fetched', data });
        } catch (err) {
            next(err);
        }
    }

    @Params(getPostInsightsParamsValidator)
    @Body(getPostInsightsBodyValidator)
    async handleFetchPlatformStoriesInsights(
        req: Request<GetPostInsightsParamsDto, any, GetPostInsightsBodyDto>,
        res: Response<ApiResult<PostInsightsDto[]>>,
        next: NextFunction
    ) {
        try {
            const { restaurantId, platformKey } = req.params;
            const { socialIds } = req.body;

            const insights = await this._postInsightsUseCases.fetchStoriesInsights(restaurantId, platformKey, socialIds);
            const data = insights.map((insight) => this._postInsightsDtoMapper.toPostInsightsDto(insight));

            return res.json({ msg: 'insights fetched', data });
        } catch (err) {
            next(err);
        }
    }
}
