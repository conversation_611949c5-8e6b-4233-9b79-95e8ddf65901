import { singleton } from 'tsyringe';

import * as facebookCredentialsUseCases from ':modules/credentials/platforms/facebook/facebook.use-cases';

import { FbGraphApiResponseIgStoryInsights } from '../post-insights.interface';

@singleton()
export class InstagramPostInsightsUseCases {
    fetchStoriesInsightsBySocialIds = (credentialId: string, socialIds: string[]): Promise<FbGraphApiResponseIgStoryInsights[]> =>
        Promise.all(socialIds.map((socialId) => facebookCredentialsUseCases.igGetStoryInsights(credentialId, socialId)));
}
