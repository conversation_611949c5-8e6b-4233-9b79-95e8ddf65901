import { Router } from 'express';
import { singleton } from 'tsyringe';

import AbstractRouter from ':helpers/abstracts/abstract-router';
import { authorize } from ':plugins/passport';

import { PostInsightsController } from './post-insights.controller';

@singleton()
export default class PostInsightsRouter extends AbstractRouter {
    constructor(private _postInsightsController: PostInsightsController) {
        super();
    }

    init(): Router {
        this.router.post('/restaurants/:restaurant_id/platforms/:platform_key/active_stories_insights', authorize(), (req, res, next) =>
            this._postInsightsController.handleFetchPlatformStoriesInsights(req, res, next)
        );

        this.router.post('/restaurants/:restaurant_id/platforms/:platform_key/stories_insights', authorize(), (req, res, next) =>
            this._postInsightsController.handleGetPlatformStoriesInsights(req, res, next)
        );

        return this.router;
    }
}
