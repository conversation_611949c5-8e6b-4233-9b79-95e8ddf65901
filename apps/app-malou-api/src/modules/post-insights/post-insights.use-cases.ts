import assert from 'node:assert/strict';
import { autoInjectable, container } from 'tsyringe';

import { IPostInsight } from '@malou-io/package-models';
import { MalouErrorCode, PlatformKey } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { Platform } from ':modules/platforms/platforms.entity';
import PlatformsRepository from ':modules/platforms/platforms.repository';

import { InstagramPostInsightsUseCases } from './instagram/instagram-post-insights.use-cases';
import { PostInsightsMapperFactory } from './post-insights.mapper';
import { PostInsightsRepository } from './post-insights.repository';

@autoInjectable()
export class PostInsightsUseCases {
    constructor(
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _postInsightsRepository: PostInsightsRepository
    ) {}

    fetchStoriesInsights = async (restaurantId: string, platformKey: PlatformKey, socialIds: string[]): Promise<IPostInsight[]> => {
        const platform = await this._platformsRepository.getPlatformByRestaurantIdAndPlatformKey(restaurantId, platformKey);
        if (!platform) {
            throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, {
                metadata: {
                    restaurantId,
                    platformKey,
                },
            });
        }
        const { key, credentials } = platform;
        const credentialId = credentials?.[0];
        if (!credentialId) {
            throw new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND);
        }
        const platformSocialId = platform.socialId;
        assert(platformSocialId, 'Missing socialId on platform');

        const storiesInsights = await PostInsightsUseCasesFactory.createUseCases(key).fetchStoriesInsightsBySocialIds(
            credentialId,
            socialIds
        );

        const postInsightsMapper = PostInsightsMapperFactory.createMapper(platform.key);
        const mappedStoriesInsights = storiesInsights.map((insights) =>
            postInsightsMapper.mapToMalouPostInsights(insights, platformSocialId, platform.key)
        );

        return Promise.all(
            mappedStoriesInsights.map((insights) =>
                this._postInsightsRepository.upsert({
                    filter: { socialId: insights.socialId, platformKey: insights.platformKey },
                    update: insights,
                    options: { lean: true },
                })
            )
        );
    };

    getStoriesInsights = async (restaurantId: string, platformKey: PlatformKey, socialIds: string[]) => {
        const platform: Pick<Platform, 'socialId'> | null = await this._platformsRepository.getPlatformByRestaurantIdAndPlatformKey(
            restaurantId,
            platformKey,
            {
                socialId: true,
            }
        );
        if (!platform) {
            throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, {
                metadata: {
                    restaurantId,
                    platformKey,
                },
            });
        }
        const { socialId } = platform;

        return this._postInsightsRepository.find({
            filter: { platformSocialId: socialId, socialId: { $in: socialIds } },
            options: { lean: true },
        });
    };
}

class PostInsightsUseCasesFactory {
    static createUseCases(platformKey: PlatformKey): PlatformPostInsightsUseCases {
        if (platformKey === PlatformKey.INSTAGRAM) {
            return container.resolve(InstagramPostInsightsUseCases);
        }

        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            metadata: {
                platformKey,
            },
        });
    }
}

export type PlatformPostInsightsUseCases = InstagramPostInsightsUseCases;
