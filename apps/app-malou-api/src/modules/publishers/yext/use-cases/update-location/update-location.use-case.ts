import { singleton } from 'tsyringe';

import { HeapEventName, InformationUpdateProvider } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { RestaurantPopulatedToPublish } from ':modules/platforms/use-cases/publish-on-connected-platforms/publish-on-connected-platforms.use-case';
import { ReservationsService } from ':modules/publishers/yext/services/reservations.service';
import { YextGetEntitiesService } from ':modules/publishers/yext/services/yext-get-entities.service';
import { YextEntityMapper } from ':modules/publishers/yext/use-cases/update-location/yext-entity.mapper';
import { HeapAnalyticsService } from ':plugins/heap-analytics';
import { YextProvider } from ':providers/yext/yext.provider';

@singleton()
export default class UpdateLocationUseCase {
    constructor(
        private readonly _yextProvider: YextProvider,
        private readonly _reservationsService: ReservationsService,
        private readonly _yextGetEntitiesService: YextGetEntitiesService,
        private readonly _heapService: HeapAnalyticsService
    ) {}

    async execute(restaurant: RestaurantPopulatedToPublish): Promise<void> {
        const restaurantId = restaurant._id.toString();
        const { yextAccount, yextLocation } = await this._yextGetEntitiesService.execute({ restaurantId });
        const yextAccountId = yextAccount.partnerAccountId;
        const locationId = yextLocation.partnerLocationId;

        const yextMapper = new YextEntityMapper(restaurant);
        const canMakeReservations = await this._reservationsService.canMakeReservations(restaurantId);
        const mappedRestaurant = yextMapper.mapRestaurantToYextEntity(canMakeReservations);

        try {
            logger.info(`[PUBLISH INFOS] [YEXT] About to update data`, {
                restaurantId,
                mappedRestaurant,
            });
            this._heapService.track({
                eventName: HeapEventName.PLATFORM_UPDATE_PER_PLATFORM_START,
                identity: restaurantId,
                properties: {
                    restaurantId,
                    provider: InformationUpdateProvider.YEXT,
                    platformKey: 'yext',
                },
            });

            await this._yextProvider.updateEntity({ accountId: yextAccountId, locationId, entity: mappedRestaurant });

            logger.info(`[PUBLISH INFOS] [YEXT] Updated data`, {
                restaurantId,
                mappedRestaurant,
            });
            this._heapService.track({
                eventName: HeapEventName.PLATFORM_UPDATE_PER_PLATFORM_END,
                identity: restaurantId,
                properties: {
                    status: 'success',
                    restaurantId,
                    provider: InformationUpdateProvider.YEXT,
                    platformKey: 'yext',
                },
            });
        } catch (err) {
            logger.error(`[PUBLISH INFOS] [YEXT] Update failed`, {
                restaurantId,
                mappedRestaurant,
                err,
            });
            this._heapService.track({
                eventName: HeapEventName.PLATFORM_UPDATE_PER_PLATFORM_END,
                identity: restaurantId,
                properties: {
                    status: 'fail',
                    restaurantId,
                    provider: InformationUpdateProvider.YEXT,
                    platformKey: 'yext',
                },
            });

            throw err;
        }
    }
}
