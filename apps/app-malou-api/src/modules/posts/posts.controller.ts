import { ForbiddenError, subject } from '@casl/ability';
import { NextFunction, Request, Response } from 'express';
import { FacebookApiException } from 'fb';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import {
    CancelStoryPayloadDto,
    cancelStoryPayloadValidator,
    CreatePostBodyDto,
    createPostBodyValidator,
    CreatePostParamsDto,
    createPostParamsValidator,
    CreateStoryBodyDto,
    createStoryBodyValidator,
    CreateStoryDto,
    createStoryParamsValidator,
    DuplicatePostBodyDto,
    duplicatePostBodyValidator,
    DuplicateSeoPostWithAiBodyDto,
    duplicateSeoPostWithAiBodyValidator,
    DuplicateSocialPostWithAiBodyDto,
    duplicateSocialPostWithAiBodyValidator,
    GetPostsWithInsightsBodyDto,
    getPostsWithInsightsBodyValidator,
    GetRestaurantPostsBodyDto,
    getRestaurantPostsBodyValidator,
    GetRestaurantPostsResponseDto,
    GetTiktokQueryCreatorInfoDto,
    GetTop3PostsInsightsBodyDto,
    getTop3PostsInsightsBodyDtoValidator,
    GetTop3PostsInsightsResponseDto,
    PlatformKeysBodyDto,
    platformKeysBodyValidator,
    postsResizeAttachmentsValidator,
    PrepareStoryPayloadDto,
    prepareStoryPayloadValidator,
    ResizePostAttachmentsDto,
    RestaurantIdParamsTransformDto,
    restaurantIdParamsTransformValidator,
    SwapPlannedPublicationDatesPayloadDto,
    swapPlannedPublicationDatesPayloadValidator,
    UpdatePostParamsDto,
    updatePostParamsValidator,
    UpdatePostPayloadDto,
    updatePostPayloadValidator,
} from '@malou-io/package-dto';
import { IPost, IPostWithAttachments, toDbId, toDbIds } from '@malou-io/package-models';
import {
    ApiResult,
    ApiResultV2,
    CaslAction,
    CaslSubject,
    HeapEventName,
    MalouErrorCode,
    PlatformKey,
    PostPublicationStatus,
    PostSource,
} from '@malou-io/package-utils';

import { AgendaSingleton } from ':helpers/classes/agenda-singleton';
import { MalouError } from ':helpers/classes/malou-error';
import { Body, Params } from ':helpers/decorators/validators';
import { PostsFilters } from ':helpers/filters/posts-filter';
import { logger } from ':helpers/logger';
import { Pagination } from ':helpers/pagination';
import { booleanStringToBoolean } from ':helpers/utils';
import { RequestWithPermissions } from ':helpers/utils.types';
import { Media } from ':modules/media/entities/media.entity';
import { TiktokQueryCreatorInfoUseCase } from ':modules/posts/platforms/tiktok/use-cases/tiktok-query-creator-info.use-case';
import { DuplicateSocialPostWithAiUseCase } from ':modules/posts/use-cases/duplicate-social-post-with-ai/duplicate-social-post-with-ai.use-case';
import { GetTop3PostsInsightsUseCase } from ':modules/posts/use-cases/get-top-3-posts-insights/get-top-3-posts-insights.use-case';
import { SynchronizePostsUseCase } from ':modules/posts/use-cases/synchronize-posts/synchronize-posts.use-case';
import {
    UpdatePostAndPublicationStatusUseCase,
    ErrorCode as UpdatePostErrorCode,
} from ':modules/posts/use-cases/update-post-and-publication-status/update-post-and-publication-status.use-cases';
import { HeapAnalyticsService } from ':plugins/heap-analytics';

import { MediasRepository } from '../media/repository/medias.repository';
import PlatformsRepository from '../platforms/platforms.repository';
import PostsRepository from './posts.repository';
import PostsUseCases from './posts.use-cases';
import { DuplicateSeoPostWithAiUseCase } from './use-cases/duplicate-seo-post-with-ai/duplicate-seo-post-with-ai.use-case';
import { SwapPlannedPublicationDatesUseCase } from './use-cases/swap-planned-publication-dates.use-case';

@singleton()
export default class PostsController {
    constructor(
        private readonly _synchronizePostsUseCases: SynchronizePostsUseCase,
        private readonly _postsUseCases: PostsUseCases,
        private readonly _getTop3PostsInsightsUseCase: GetTop3PostsInsightsUseCase,
        private readonly _updatePostAndPublicationStatusUseCase: UpdatePostAndPublicationStatusUseCase,
        private readonly _postsRepository: PostsRepository,
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _mediasRepository: MediasRepository,
        private readonly _agendaSingleton: AgendaSingleton,
        private readonly _duplicateSeoPostWithAiUseCase: DuplicateSeoPostWithAiUseCase,
        private readonly _duplicateSocialPostWithAiUseCase: DuplicateSocialPostWithAiUseCase,
        private readonly _eventTrackerService: HeapAnalyticsService,
        private readonly _swapPlannedPublicationDatesUseCase: SwapPlannedPublicationDatesUseCase,
        private readonly _tiktokQueryCreatorInfoUseCase: TiktokQueryCreatorInfoUseCase
    ) {}

    // TODO: add validators + DTO en sortie + security check ?
    // + passr en try catch
    async handleSearchIgAccounts(req: Request, res: Response, next: NextFunction) {
        const { text } = req.query;
        const { platform_id: platformId } = req.params;
        const { _id: userId } = req.user;
        return this._postsUseCases
            .getPlatformPostUseCases(PlatformKey.INSTAGRAM)
            .searchAccounts(text, platformId, userId)
            .then((result) => {
                if (result) {
                    return res.json({ data: result });
                }
            })
            .catch((err) => next(err));
    }

    // TODO: add validators + DTO en sortie + security check ?
    // + passer en try catch
    async handleGetIgOembed(req: Request, res: Response, next: NextFunction) {
        const { social_link: socialLink, platform_id: platformId } = req.params;
        return this._postsUseCases
            .getPlatformPostUseCases(PlatformKey.INSTAGRAM)
            .oembed(socialLink, platformId)
            .then((result) => {
                if (result) {
                    return res.json({ data: result });
                }
            })
            .catch(next);
    }

    // TODO: ajout d'authorization user for posts
    @Params(restaurantIdParamsTransformValidator)
    @Body(platformKeysBodyValidator)
    async handleSynchronizePosts(
        req: Request<RestaurantIdParamsTransformDto, any, PlatformKeysBodyDto>,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;
            const { platformKeys } = req.body;
            await this._synchronizePostsUseCases.execute(restaurantId, platformKeys);
            return res.json({ msg: 'Posts synchronized.' });
        } catch (err: any) {
            if (err instanceof FacebookApiException) {
                return next(
                    new MalouError(MalouErrorCode.FACEBOOK_API_EXCEPTION, {
                        message: err?.message,
                        metadata: err,
                    })
                );
            }

            next(err);
        }
    }

    // TODO: add security check ?
    @Params(restaurantIdParamsTransformValidator)
    @Body(getRestaurantPostsBodyValidator)
    async handleGetRestaurantPosts(
        req: Request<any, RestaurantIdParamsTransformDto, any, GetRestaurantPostsBodyDto>,
        res: Response<ApiResultV2<GetRestaurantPostsResponseDto>>,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;
            const { text, startDate, endDate, sortBy, sortOrder, publicationStatus, platforms, source, isStory, reelsInFeed } =
                req.body.posts;
            const { pageNumber, pageSize, total, skip } = req.body.pagination;

            const pagination = new Pagination({
                pageNumber,
                pageSize,
                total,
                skip,
            });
            const filters = new PostsFilters({
                startDate,
                endDate,
                sortBy,
                sortOrder,
                restaurantId,
                platforms: platforms ?? [],
                publicationStatus,
                text,
                source,
                isStory,
                reelsInFeed,
            });
            const [posts, postCount, jobAttributes] = await Promise.all([
                this._postsUseCases.getRestaurantPostsPaginated({ pagination, filters }),
                this._postsUseCases.getRestaurantPostsCount({ filters }),
                this._postsUseCases.getPostJobAttributesForRestaurantId(restaurantId),
            ]);

            pagination.total = postCount;

            return res.status(200).json({ data: { posts, pagination, jobs: jobAttributes } });
        } catch (error) {
            next(error);
        }
    }

    // TODO: this endpoint is for the Mobile App, it should be removed later [@Mobile]
    async handleGetRestaurantPostsForMobile(req: Request, res: Response, next: NextFunction) {
        try {
            const { restaurant_id: restaurantId } = req.params;
            const {
                text,
                start_date: startDate,
                end_date: endDate,
                sort_by: sortBy,
                sort_order: sortOrder,
                page_number: pageNumber,
                page_size: pageSize,
                skip,
                publication_status: publicationStatus,
                platforms,
                total,
                source,
                is_story: isStory,
                reels_in_feed: reelsInFeed,
            } = req.query as any;
            const pagination = new Pagination({
                pageNumber,
                pageSize,
                total,
                skip,
            });
            const filters = new PostsFilters({
                startDate,
                endDate,
                sortBy,
                sortOrder,
                restaurantId,
                platforms: platforms ?? [],
                publicationStatus,
                text,
                source,
                isStory: isStory ? booleanStringToBoolean(isStory) : undefined,
                reelsInFeed: reelsInFeed ? booleanStringToBoolean(reelsInFeed) : undefined,
            });
            const [posts, postCount, jobAttributes] = await Promise.all([
                this._postsUseCases.getRestaurantPostsPaginated({ pagination, filters }),
                this._postsUseCases.getRestaurantPostsCount({ filters }),
                this._postsUseCases.getPostJobAttributesForRestaurantId(restaurantId),
            ]);

            pagination.total = postCount;

            return res.status(200).json({ msg: 'posts paginated !', data: { posts, pagination, jobs: jobAttributes } });
        } catch (error) {
            next(error);
        }
    }

    // TODO: add validators + DTO en sortie + security check ?
    async handleGetPost(req: Request, res: Response, next: NextFunction) {
        try {
            const postId = req.params.post_id;
            const { with_jobs: withJobs } = req.query;
            const post = await this._postsRepository.findOne({
                filter: { _id: toDbId(postId) },
                options: {
                    lean: true,
                    populate: [{ path: 'attachments' }, { path: 'feedback' }, { path: 'thumbnail' }],
                },
            });

            if (!post) {
                throw new MalouError(MalouErrorCode.POST_NOT_FOUND, {
                    metadata: { postId },
                });
            }

            let jobs;
            if (withJobs) {
                const agenda = await this._agendaSingleton.getInstance();
                jobs = await agenda.jobs({ 'data.postId': post._id });
            }
            return res.json({ data: { ...post, jobs: jobs?.map((job) => job.attrs) } });
        } catch (err) {
            next(err);
        }
    }

    // TODO: add validators + DTO en sortie + security check ?
    async handleGetPostStories(req: Request<{ post_id: string }>, res: Response, next: NextFunction) {
        try {
            const postId = req.params.post_id;
            const post = await this._postsRepository.findOne({
                filter: { _id: toDbId(postId) },
                options: { lean: true, populate: [{ path: 'attachments' }] },
            });

            if (!post) {
                throw new MalouError(MalouErrorCode.POST_NOT_FOUND, {
                    metadata: { postId },
                });
            }

            if (!post.malouStoryId) {
                return res.json({ data: [post] });
            }

            const storiesRelatedPosts = await this._postsRepository.find({
                filter: { malouStoryId: post.malouStoryId, published: { $ne: PostPublicationStatus.PUBLISHED } },
                options: { lean: true, populate: [{ path: 'attachments' }] },
            });
            return res.json({ data: storiesRelatedPosts });
        } catch (err) {
            next(err);
        }
    }

    // TODO: add validators + DTO en sortie + security check ?
    async handleRefreshPost(req: Request, res: Response, next: NextFunction) {
        try {
            const { post_id: postId } = req.params;
            const post = await this._postsUseCases.refresh(postId);
            return res.json({ msg: 'Success', data: post });
        } catch (err) {
            next(err);
        }
    }

    // TODO: add validators + DTO en sortie + security check ?
    async handleDeletePostJobs(req: Request, res: Response, next: NextFunction) {
        try {
            const { post_id: postId } = req.params;
            const agenda = await this._agendaSingleton.getInstance();
            await agenda.cancel({ 'data.postId': postId });
            return res.json({ msg: 'Jobs deleted for post' });
        } catch (err) {
            next(err);
        }
    }

    @Params(createStoryParamsValidator)
    @Body(createStoryBodyValidator)
    async handleCreateStory(req: Request<CreateStoryDto, any, CreateStoryBodyDto>, res: Response<ApiResultV2>, next: NextFunction) {
        try {
            const { restaurantId } = req.params;
            logger.info('[CREATE_STORY] creating', { ...req.body, restaurantId });
            const { malouStoryId, keys, attachmentId, plannedPublicationDate, duplicatedFromRestaurantId, createdFromDeviceType } =
                req.body;
            const user = req.user;

            const storyData = {
                malouStoryId,
                keys,
                attachments: attachmentId ? [toDbId(attachmentId)] : [],
                restaurantId: toDbId(restaurantId),
                source: PostSource.SOCIAL,
                isStory: true,
                published: PostPublicationStatus.DRAFT,
                createdFromDeviceType: createdFromDeviceType ?? undefined,
            };

            if (keys.length === 1) {
                const key = keys[0];
                const platform = await this._platformsRepository.findOne({
                    filter: { restaurantId: toDbId(restaurantId), key },
                    options: { lean: true },
                });

                if (!platform) {
                    throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, {
                        message: 'Platform missing',
                        metadata: {
                            restaurantId,
                            key,
                        },
                    });
                }

                Object.assign(storyData, { platformId: platform._id });
            }

            if (attachmentId) {
                const media = await this._mediasRepository.findById(attachmentId);
                assert(media, 'Media not found');
                if (media.deletedAt) {
                    logger.info('[CREATE_STORY] the attached media does not exist or has been deleted', {
                        mediaId: attachmentId,
                        restaurantId,
                    });
                    throw new MalouError(MalouErrorCode.MEDIA_DELETED_FOR_POST);
                }
                const mediaDoc = this._mediasRepository.toDocument(media);
                const postType = this._postsUseCases.getStoryTypeFromAttachments([mediaDoc]);
                if (postType) {
                    Object.assign(storyData, { postType });
                }
            }

            if (plannedPublicationDate) {
                Object.assign(storyData, { plannedPublicationDate: new Date(plannedPublicationDate) });
            }
            const otherPosts = await this._postsRepository.find({
                filter: { malouStoryId },
                options: { lean: true },
            });
            for (const otherPost of otherPosts) {
                if (otherPost.published === PostPublicationStatus.PUBLISHED) {
                    throw new MalouError(MalouErrorCode.STORY_ALREADY_PUBLISHED);
                }
                if (otherPost.published === PostPublicationStatus.PENDING && !attachmentId) {
                    throw new MalouError(MalouErrorCode.MEDIA_REQUIRED_BECAUSE_THE_STORY_IS_PLANNED_FOR_PUBLICATION);
                }
            }

            if (duplicatedFromRestaurantId) {
                Object.assign(storyData, { duplicatedFromRestaurantId });
            }

            const sortDate = plannedPublicationDate ? new Date(plannedPublicationDate) : new Date();
            const newStory = await this._postsRepository.create({
                data: {
                    ...storyData,
                    author: { name: user.name, _id: user._id },
                    sortDate,
                },
            });
            const populatedStory = await this._postsRepository.findOne({
                filter: { _id: newStory._id },
                options: { populate: [{ path: 'attachments' }, { path: 'thumbnail' }] },
            });
            logger.info('[CREATE_STORY] created', populatedStory);
            return res.json({ data: populatedStory });
        } catch (err) {
            next(err);
        }
    }

    // TODO: add security check ?
    @Body(prepareStoryPayloadValidator)
    async handlePrepareStory(req: Request<{}, {}, PrepareStoryPayloadDto>, res: Response, next: NextFunction) {
        try {
            const { keys, malouStoryId } = req.body;
            await this._postsUseCases.schedulePrepareStory(keys, malouStoryId);
            return res.json({ msg: 'Stories prepared' });
        } catch (err) {
            next(err);
        }
    }

    // TODO: add security check ?
    @Body(cancelStoryPayloadValidator)
    async handleCancelStory(req: Request<any, any, CancelStoryPayloadDto>, res: Response, next: NextFunction) {
        try {
            const { malouStoryId } = req.body;
            await this._postsUseCases.cancelStory(malouStoryId);
            return res.json({ msg: 'Stories cancelled' });
        } catch (err) {
            next(err);
        }
    }

    @Params(createPostParamsValidator)
    @Body(createPostBodyValidator)
    async handleCreatePost(req: Request<CreatePostParamsDto, any, CreatePostBodyDto>, res: Response, next: NextFunction) {
        try {
            const { restaurantId } = req.params;
            const { post, keys } = req.body;
            const { user } = req;

            const isDraft = true;
            const newPost = await this._postsUseCases.createPost(
                {
                    ...post,
                    restaurantId: toDbId(restaurantId),
                    author: { name: user.name, _id: user._id },
                    createdFromDeviceType: post.createdFromDeviceType ?? undefined,
                },
                isDraft,
                keys
            );

            return res.json({ msg: 'Success', data: newPost });
        } catch (err) {
            next(err);
        }
    }

    @Params(createPostParamsValidator)
    @Body(duplicatePostBodyValidator)
    async handleDuplicatePost(
        req: RequestWithPermissions<CreatePostParamsDto, any, DuplicatePostBodyDto>,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;
            const { post, keys, draft, duplicatePostId } = req.body;
            const { user } = req;
            assert(user, 'Missing user');
            if (!draft) {
                switch (keys[0]) {
                    case PlatformKey.GMB:
                        assert(req.userRestaurantsAbility);
                        ForbiddenError.from(req.userRestaurantsAbility).throwUnlessCan(
                            CaslAction.PUBLISH,
                            subject(CaslSubject.SEO_POST, { restaurantId })
                        );
                        break;
                    default:
                        assert(req.userRestaurantsAbility);
                        ForbiddenError.from(req.userRestaurantsAbility).throwUnlessCan(
                            CaslAction.PUBLISH,
                            subject(CaslSubject.SOCIAL_POST, { restaurantId })
                        );
                        break;
                }
            }
            const newPost = await this._postsUseCases.duplicatePost(
                {
                    ...post,
                    restaurantId: toDbId(restaurantId),
                    author: { name: user.name, _id: user._id },
                },
                draft,
                keys,
                duplicatePostId
            );
            logger.info('[DUPLICATE_POST]', { originalPostId: duplicatePostId, newPostId: newPost._id });
            const scheduledPost = await this._postsUseCases.schedulePreparePost({ userId: user._id, post: newPost });
            if (!draft) {
                const originalPostRestaurantId = await this._postsRepository
                    .findById(duplicatePostId)
                    .then((result) => result?.restaurantId);
                assert(originalPostRestaurantId, 'Original post not found');
                this._eventTrackerService.track({
                    eventName: HeapEventName.DUPLICATE_POSTS,
                    identity: user._id.toString(),
                    properties: {
                        duplicatedFromRestaurantId: originalPostRestaurantId,
                        toRestaurantIds: [restaurantId],
                        platforms: keys,
                        userEmail: user.email,
                    },
                });
            }
            return res.json({ msg: 'Success', data: scheduledPost });
        } catch (error) {
            next(error);
        }
    }

    @Body(duplicateSeoPostWithAiBodyValidator)
    async handleDuplicateSeoPostWithAi(
        req: RequestWithPermissions<any, any, DuplicateSeoPostWithAiBodyDto>,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { postIdToDuplicate, restaurantIds, postDestination } = req.body;
            assert(req.user, 'Missing user');
            const { _id: userId } = req.user;
            const duplicatedCaptionsDetails = await this._duplicateSeoPostWithAiUseCase.execute({
                postIdToDuplicate,
                restaurantIds,
                postDestination,
                userId: userId.toString(),
            });
            return res.json({ msg: 'Success', data: duplicatedCaptionsDetails });
        } catch (error) {
            next(error);
        }
    }

    /** Despite its name, this route does not actually duplicate posts. */
    @Body(duplicateSocialPostWithAiBodyValidator)
    async handleDuplicateSocialPostWithAi(
        req: RequestWithPermissions<any, any, DuplicateSocialPostWithAiBodyDto>,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { postRefToDuplicate, restaurantIds, postDestination } = req.body;
            assert(req.user, 'Missing user');
            const { _id: userId } = req.user;

            const duplicatedCaptionsDetails = await this._duplicateSocialPostWithAiUseCase.execute({
                postRefToDuplicate,
                restaurantIds,
                userId: userId.toString(),
                postDestination,
            });
            return res.json({ msg: 'Success', data: duplicatedCaptionsDetails });
        } catch (error) {
            next(error);
        }
    }

    @Params(updatePostParamsValidator)
    @Body(updatePostPayloadValidator)
    async handleUpdatePost(
        req: RequestWithPermissions<UpdatePostParamsDto, never, UpdatePostPayloadDto>,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { postId } = req.params;
            logger.info('[UPDATE_POST] updating', { postId, update: req.body });
            const post = await this._postsRepository.findOne({ filter: { _id: toDbId(postId) }, options: { lean: true } });
            assert(post, 'Post not found');

            assert(req.userRestaurantsAbility);
            ForbiddenError.from(req.userRestaurantsAbility).throwUnlessCan(
                CaslAction.CREATE,
                subject(CaslSubject.POST, { restaurantId: post.restaurantId, keys: post.keys })
            );
            if (!post) {
                throw new MalouError(MalouErrorCode.POST_NOT_FOUND, {
                    message: 'Post not found',
                    metadata: { postId },
                });
            }

            const postData = req.body;
            const updatedPost = await this._postsUseCases.updatePost(postId, postData);
            logger.info('[UPDATE_POST] updated', { postId });

            return res.json({ msg: 'Post updated.', data: updatedPost });
        } catch (err) {
            next(err);
        }
    }

    @Params(updatePostParamsValidator)
    async handlePreparePost(
        req: RequestWithPermissions<UpdatePostParamsDto> & {
            currentPost?: { published: PostPublicationStatus; key: any };
        },
        res: Response,
        next: NextFunction
    ) {
        try {
            const { postId } = req.params;
            const { currentPost, user } = req;

            assert(currentPost, 'Missing current post');
            const { published, key } = currentPost;

            const { post: postData, draft, keys } = req.body;
            const { restaurantId } = req.query;
            assert(user, 'Missing user');

            assert(req.userRestaurantsAbility);
            ForbiddenError.from(req.userRestaurantsAbility).throwUnlessCan(
                CaslAction.UPDATE,
                subject(CaslSubject.POST, { restaurantId, keys })
            );

            if (!draft || (published !== PostPublicationStatus.DRAFT && draft)) {
                // if trying to schedule or unschedule a post
                switch (keys[0] || key) {
                    case PlatformKey.GMB:
                        assert(req.userRestaurantsAbility);
                        ForbiddenError.from(req.userRestaurantsAbility).throwUnlessCan(
                            CaslAction.PUBLISH,
                            subject(CaslSubject.SEO_POST, { restaurantId })
                        );
                        break;
                    default:
                        assert(req.userRestaurantsAbility);
                        ForbiddenError.from(req.userRestaurantsAbility).throwUnlessCan(
                            CaslAction.PUBLISH,
                            subject(CaslSubject.SOCIAL_POST, { restaurantId })
                        );
                        break;
                }
            }

            logger.info('[PREPARE_POST] preparing', { postId, update: postData });
            if (!postData.plannedPublicationDate) {
                postData.plannedPublicationDate = null;
            } else {
                postData.plannedPublicationDate = new Date(postData.plannedPublicationDate);
            }
            const result = await this._updatePostAndPublicationStatusUseCase.execute({
                postId,
                update: postData,
                isDraft: draft,
                keys,
            });
            if (result.isErr()) {
                if (result.error === UpdatePostErrorCode.POST_NOT_FOUND) {
                    throw new MalouError(MalouErrorCode.POST_NOT_FOUND, {
                        metadata: { postId },
                    });
                }
                const exhaustiveCheck: never = result.error;
                assert.fail(`unhandled error ${exhaustiveCheck}`);
            }
            let updatedPost = result.value;

            if (!this._postsUseCases.hasBeenPosted(updatedPost.published)) {
                // TODO remove this when Tiktok is ready
                if ((updatedPost.keys ?? []).includes(PlatformKey.TIKTOK) && draft === false) {
                    await this._postsUseCases.preparePost({ userId: user._id.toString(), postId });
                } else {
                    updatedPost = await this._postsUseCases.schedulePreparePost({ userId: user._id.toString(), post: updatedPost });
                }
            }
            logger.info('[PREPARE_POST] prepared', { postId });
            return res.json({ msg: 'Post updated.', data: updatedPost });
        } catch (err) {
            next(err);
        }
    }

    @Body(swapPlannedPublicationDatesPayloadValidator)
    async swapPlannedPublicationDates(
        req: RequestWithPermissions<{}, {}, SwapPlannedPublicationDatesPayloadDto>,
        res: Response,
        next: NextFunction
    ) {
        try {
            assert(req.user, 'Missing user');
            assert(req.userRestaurantsAbility);
            await this._swapPlannedPublicationDatesUseCase.execute(req.user._id.toString(), req.userRestaurantsAbility, req.body);
            res.json({ msg: 'Publication dates swapped successfully' });
        } catch (err) {
            next(err);
        }
    }

    // TODO: add validators
    async handleDeletePost(req: RequestWithPermissions, res: Response, next: NextFunction) {
        try {
            const { post_id: postId } = req.params;
            const { restaurantId } = req.query;
            const {
                user,
                currentPost: { keys = [], key, published },
            } = req as any;
            logger.info('[DELETE_POST] deleting', { postId });
            assert(req.userRestaurantsAbility);
            ForbiddenError.from(req.userRestaurantsAbility).throwUnlessCan(
                CaslAction.DELETE,
                subject(CaslSubject.POST, { restaurantId, keys })
            );
            if (published !== PostPublicationStatus.DRAFT) {
                switch (keys?.[0] || key) {
                    case PlatformKey.GMB:
                        assert(req.userRestaurantsAbility);
                        ForbiddenError.from(req.userRestaurantsAbility).throwUnlessCan(
                            CaslAction.PUBLISH,
                            subject(CaslSubject.SEO_POST, { restaurantId })
                        );
                        break;
                    default:
                        assert(req.userRestaurantsAbility);
                        ForbiddenError.from(req.userRestaurantsAbility).throwUnlessCan(
                            CaslAction.PUBLISH,
                            subject(CaslSubject.SOCIAL_POST, { restaurantId })
                        );
                        break;
                }
            }
            await this._postsUseCases.deletePostById({ user, postId });
            logger.info('[DELETE_POST] deleted', { postId });
            return res.status(200).json({ msg: 'Post deleted.' });
        } catch (err) {
            next(err);
        }
    }

    // TODO: add validators
    async handleDeletePosts(req: RequestWithPermissions, res: Response, next: NextFunction) {
        try {
            const { restaurantId } = req.query;
            const { ids: postIds } = req.body;
            const {
                user,
                currentPost: { keys, key },
                isSomePostsPublished,
            } = req as any;
            assert(req.userRestaurantsAbility);
            ForbiddenError.from(req.userRestaurantsAbility).throwUnlessCan(
                CaslAction.DELETE,
                subject(CaslSubject.POST, { restaurantId, keys })
            );
            if (isSomePostsPublished) {
                switch (keys?.[0] || key) {
                    case PlatformKey.GMB:
                        assert(req.userRestaurantsAbility);
                        ForbiddenError.from(req.userRestaurantsAbility).throwUnlessCan(
                            CaslAction.PUBLISH,
                            subject(CaslSubject.SEO_POST, { restaurantId })
                        );
                        break;
                    default:
                        assert(req.userRestaurantsAbility);
                        ForbiddenError.from(req.userRestaurantsAbility).throwUnlessCan(
                            CaslAction.PUBLISH,
                            subject(CaslSubject.SOCIAL_POST, { restaurantId })
                        );
                        break;
                }
            }
            const promises = postIds.map((postId) => this._postsUseCases.deletePostById({ user, postId }));
            await Promise.all(promises);
            return res.status(200).json({ msg: 'Posts deleted.' });
        } catch (err) {
            next(err);
        }
    }

    async handleGetCompetitorsPosts(req: Request, res: Response, next: NextFunction) {
        try {
            const { platform_id: platformId } = req.params;
            const platform = await this._platformsRepository.findOne({ filter: { _id: toDbId(platformId) }, options: { lean: true } });
            assert(platform, 'Platform not found');
            const competitors = await this._postsUseCases.getCompetitorsPosts(platform);
            return res.status(200).json({ data: competitors });
        } catch (err) {
            next(err);
        }
    }

    async handleGetLastSocialPosts(req: Request, res: Response, next: NextFunction) {
        try {
            const { restaurant_id: restaurantId } = req.params;
            // need check socialCreatedAt !null because some restaurants have wrong mapped posts, its a unknown bug that we need to fix
            const posts = await this._postsRepository.find({
                filter: {
                    restaurantId,
                    published: PostPublicationStatus.PUBLISHED,
                    socialCreatedAt: { $ne: null },
                },
                options: { populate: [{ path: 'attachments' }, { path: 'feedback' }, { path: 'thumbnail' }] },
            });
            const lastPost =
                posts?.length !== 0
                    ? posts.sort((a, b) => (b.socialCreatedAt ?? new Date()).getTime() - (a.socialCreatedAt ?? new Date()).getTime())[0]
                    : posts;
            return res.status(200).json({ data: lastPost });
        } catch (err) {
            next(err);
        }
    }

    async handleGetPostsByDateRange(req: Request, res: Response, next: NextFunction) {
        try {
            const { restaurant_id: restaurantId, start_date: startDate, end_date: endDate } = req.params;
            const posts = await this._postsUseCases.getPostsBetweenDays(restaurantId, startDate, endDate);
            const agenda = await this._agendaSingleton.getInstance();
            const jobs = await agenda.jobs({ 'data.postId': { $in: posts.map((p) => p._id) } });
            return res.status(200).json({ data: { posts, jobs } });
        } catch (err) {
            next(err);
        }
    }

    async handleGetPostsBySocialIds(req: Request, res: Response, next: NextFunction) {
        try {
            const { ids, platformsIds } = req.body;
            const posts = await this._postsRepository.find({
                filter: { socialId: { $in: toDbIds(ids) }, platformId: { $in: platformsIds } },
                options: { populate: [{ path: 'attachments' }, { path: 'feedback' }, { path: 'thumbnail' }] },
            });
            return res.json({ msg: 'Posts retrieved', data: posts });
        } catch (err) {
            next(err);
        }
    }

    async handleGetLastIgPosts(req: Request, res: Response, next: NextFunction) {
        try {
            const { restaurant_id: restaurantId, number } = req.params;
            const mappedPosts = await this._postsUseCases.fetchLastIgPosts(restaurantId, number);
            const promises = mappedPosts.map((post) =>
                this._postsRepository.upsert({
                    filter: {
                        socialId: post.socialId,
                        platformId: post.platformId,
                    },
                    update: post,
                })
            );
            const posts = await Promise.all(promises);
            return res.json({ msg: 'Last posts retrieved', data: posts });
        } catch (err) {
            next(err);
        }
    }

    async handleGetPostByBindingId(
        req: Request<null, null, null, { binding_id: string; binding_id_key: string }>,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { binding_id: bindingId, binding_id_key: bindingIdKey } = req.query;
            const filter = { [bindingIdKey]: bindingId };
            const posts = await this._postsRepository.find({
                filter,
                options: {
                    lean: true,
                    populate: [{ path: 'attachments' }, { path: 'feedback' }, { path: 'thumbnail' }],
                },
            });
            const agenda = await this._agendaSingleton.getInstance();
            const jobs = await agenda.jobs({ 'data.postId': { $in: posts.map((p) => p._id) } });
            return res.json({
                msg: 'Success',
                data: posts.map((p) => ({
                    ...p,
                    jobs: jobs.filter((job) => p._id === job.attrs?.data?.postId)?.map((job) => job.attrs),
                })),
            });
        } catch (err) {
            next(err);
        }
    }

    async handleGetPostsByPlatform(req: Request, res: Response, next: NextFunction) {
        try {
            const { restaurant_id: restaurantId, platform_key: platformKey } = req.params;
            const posts = await this._postsRepository.find({
                filter: {
                    restaurantId,
                    $or: [{ key: platformKey }, { keys: { $elemMatch: { $eq: platformKey } } }],
                },
                options: { populate: [{ path: 'attachments' }, { path: 'feedback' }, { path: 'thumbnail' }] },
            });
            const jobAttributes = await this._postsUseCases.getPostJobAttributesForRestaurantId(restaurantId);
            return res.json({ message: 'Posts retrieved', data: { posts, jobs: jobAttributes } });
        } catch (err) {
            next(err);
        }
    }

    async handleGetPostsByFeedbackType(req: Request, res: Response, next: NextFunction) {
        try {
            const { restaurant_id: restaurantId, type } = req.params;
            const filter = { restaurantId, feedbackId: { $ne: null } };
            if (type !== 'all') {
                Object.assign(filter, { source: type });
            }
            const posts = await this._postsRepository.find({ filter, projection: { _id: 1 } });

            return res.json({ msg: 'Posts with feedbacks', data: posts });
        } catch (err) {
            next(err);
        }
    }

    async handleGetPostsByMedia(req: Request, res: Response, next: NextFunction) {
        try {
            const { restaurant_id: restaurantId } = req.params;
            const { mediaIds } = req.body;
            const posts = await this._postsUseCases.getRestaurantPostsByMediaIds(restaurantId, mediaIds);
            return res.json({ msg: 'Posts with media', data: posts.flat() });
        } catch (err) {
            next(err);
        }
    }

    async handleUpdatePostMedias(req: Request, res: Response, next: NextFunction) {
        try {
            const { posts, mediaIds } = req.body;
            logger.info('[UPDATE_POST_MEDIAS]', { posts, mediaIds });
            if (!posts?.length) {
                return res.json({ msg: 'Posts updated', data: [] });
            }
            const promises = posts.map((post) =>
                this._postsRepository.findOneAndUpdate({
                    filter: { _id: post._id },
                    update: { attachments: post.attachments?.filter((att) => !mediaIds.includes(att)) ?? [] },
                    options: { lean: true },
                })
            );
            const updatedPosts = Promise.all(promises);
            logger.info('[UPDATE_POST_MEDIAS] updated', { posts, mediaIds });
            return res.json({ msg: 'Posts updated', data: updatedPosts });
        } catch (err) {
            next(err);
        }
    }

    @Params(restaurantIdParamsTransformValidator)
    @Body(getPostsWithInsightsBodyValidator)
    async handleFetchPostsWithInsights(
        req: Request<RestaurantIdParamsTransformDto, any, GetPostsWithInsightsBodyDto>,
        // TODO: Add a better response via the dto [@hamza]
        res: Response<
            ApiResultV2<
                Record<
                    PlatformKey,
                    {
                        data?: IPost[];
                        error?: boolean;
                        message?: string;
                    }
                >[]
            >
        >,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;
            const { endDate, platformKeys, startDate } = req.body;
            assert(startDate, 'Missing start date');
            assert(endDate, 'Missing end date');
            const postWithInsights = await this._postsUseCases.fetchPostsWithInsights({
                restaurantId,
                startDate: startDate.toString(),
                endDate: endDate.toString(),
                platformKeys,
            });
            return res.status(200).json({ data: postWithInsights });
        } catch (err) {
            next(err);
        }
    }

    @Body(getTop3PostsInsightsBodyDtoValidator)
    async handleGetTop3PostsInsightsV2(
        req: Request<any, any, GetTop3PostsInsightsBodyDto>,
        res: Response<ApiResultV2<GetTop3PostsInsightsResponseDto[]>>,
        next: NextFunction
    ) {
        try {
            const result = await this._getTop3PostsInsightsUseCase.execute(req.body);
            return res.status(200).json({ data: result });
        } catch (e) {
            next(e);
        }
    }

    async handleSynchronizeStories(req: Request, res: Response<ApiResult<IPostWithAttachments[]>>, next: NextFunction) {
        try {
            const restaurantId = req.params.restaurant_id;
            const currentStories = await this._postsUseCases.synchronizeRestaurantStories(restaurantId);
            return res.json({ msg: 'Stories synchronized.', data: currentStories });
        } catch (err) {
            next(err);
        }
    }

    async handleGetStoriesByAuthorCount(req: Request, res: Response<ApiResult<number>>, next: NextFunction) {
        try {
            const userId = req.user._id;

            const createdStoriesCount = await this._postsUseCases.countStoriesByAuthor(userId);

            return res.status(200).json({ data: createdStoriesCount });
        } catch (err) {
            next(err);
        }
    }

    @Body(postsResizeAttachmentsValidator)
    async handleResizeAttachments(req: Request<any, any, ResizePostAttachmentsDto>, res: Response, next: NextFunction) {
        try {
            const { postMedias, postSource, options, postType } = req.body;

            const newMedias = await this._postsUseCases.resizeAttachmentsAndCreateNewPostMedias(
                postMedias.map((media) => new Media(media as any)), // this shitty any is because of strict false... I'm sorry
                postSource,
                options,
                postType
            );

            return res.status(201).json({ msg: 'Attachments resized', data: newMedias });
        } catch (err) {
            next(err);
        }
    }

    /**
     *
     * Tiktok
     *
     */
    @Params(restaurantIdParamsTransformValidator)
    async tiktokQueryCreatorInfo(
        req: Request<RestaurantIdParamsTransformDto, any, any>,
        res: Response<ApiResultV2<GetTiktokQueryCreatorInfoDto>>,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;
            const data = await this._tiktokQueryCreatorInfoUseCase.execute({ restaurantId });

            return res.json({ data });
        } catch (err) {
            next(err);
        }
    }
}
