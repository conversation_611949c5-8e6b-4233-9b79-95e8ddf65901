import { singleton } from 'tsyringe';

import { DbId, IPostWithAttachments } from '@malou-io/package-models';
import { isNotNil, MalouErrorCode, MediaType } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { PlatformPostGetter } from ':modules/posts/platforms/getter';
import PostsRepository from ':modules/posts/posts.repository';

@singleton()
export class PublishStoryUseCase {
    constructor(
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _postsRepository: PostsRepository,
        private readonly _platformPostGetter: PlatformPostGetter
    ) {}

    async execute(
        storyData: { plannedPublicationDate: Date; postId: DbId; creationId: string; type: MediaType },
        platformId: DbId
    ): Promise<IPostWithAttachments | undefined> {
        logger.info('[PUBLISH_STORY] publishing', { postId: storyData.postId });
        const platform = await this._platformsRepository.getPlatformById(platformId.toString());

        if (!platform) {
            throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, {
                metadata: { platformId, useCase: 'PublishStoryUseCase' },
                message: 'Platform not found in PublishStoryUseCase',
            });
        }

        const malouStory = await this._platformPostGetter
            .getPlatformPostUseCases(platform.key)
            .publishStory(platform._id.toString(), storyData.creationId, storyData.type, storyData.postId.toString());

        if (malouStory && Object.keys(malouStory)?.length) {
            try {
                const post = await this._postsRepository.upsert({
                    filter: { _id: storyData.postId },
                    update: malouStory,
                    options: { populate: [{ path: 'attachments' }] },
                });
                logger.info('[PUBLISH_STORY] published', post);
                return post;
            } catch (err) {
                const error = err as Error;
                logger.error('[PUBLISH_STORY] error while updating post', {
                    postId: storyData.postId,
                    error: error.message,
                });
                const isDuplicatedPost = ('code' in error && error.code === 11000) || JSON.stringify(error).match(/duplicate key error/);

                if (isDuplicatedPost) {
                    const postOrNull = await this._handlePostSyncedBeforePublicationEnding({
                        socialId: malouStory.socialId,
                        postId: storyData.postId,
                        platformId: platform._id,
                    });
                    if (isNotNil(postOrNull)) {
                        return postOrNull;
                    }
                }
                throw new MalouError(MalouErrorCode.PUBLISH_STORY_ERROR, {
                    metadata: { postId: storyData.postId, useCase: 'PublishStoryUseCase' },
                    message: 'Failed to upsert post after publishing story',
                });
            }
        }
    }

    private async _handlePostSyncedBeforePublicationEnding({
        socialId,
        postId,
        platformId,
    }: {
        socialId?: string;
        postId: DbId;
        platformId: DbId;
    }): Promise<IPostWithAttachments | null> {
        logger.warn('[PUBLISH_STORY] duplicate key error, fetching post', {
            postId,
            platformId,
        });
        if (!socialId) {
            logger.error('[PUBLISH_STORY] duplicate key error, no socialId provided', { postId, platformId });
            return null;
        }
        const post = await this._postsRepository.findOne({
            filter: {
                socialId,
                platformId,
            },
            options: { populate: [{ path: 'attachments' }] },
        });
        if (!post) {
            logger.error('[PUBLISH_STORY] duplicate key error, no published post found in DB', { socialId, postId, platformId });
            return null;
        }
        // Get the pending post on Malou
        const pendingPostOnMalou = await this._postsRepository.findOne({
            filter: { _id: postId, published: 'pending' },
        });
        if (!pendingPostOnMalou) {
            logger.error('[PUBLISH_STORY] duplicate key error, no pending post found in DB', { postId });
            return post;
        }
        // Update the post with the pending post data related to Malou
        const updatedPostWithMalouData = await this._postsRepository.findOneAndUpdate({
            filter: { _id: post._id },
            update: {
                attachments: pendingPostOnMalou.attachments,
                shouldDuplicateInOtherPlatforms: pendingPostOnMalou.shouldDuplicateInOtherPlatforms,
                author: pendingPostOnMalou.author,
                malouStoryId: pendingPostOnMalou.malouStoryId,
                instagramCollaboratorsUsernames: pendingPostOnMalou.instagramCollaboratorsUsernames,
                createdFromDeviceType: pendingPostOnMalou.createdFromDeviceType,
            },
            options: { populate: [{ path: 'attachments' }] },
        });
        // Delete the pending post
        await this._postsRepository.deleteOne({ filter: { _id: postId } });
        return updatedPostWithMalouData;
    }
}
