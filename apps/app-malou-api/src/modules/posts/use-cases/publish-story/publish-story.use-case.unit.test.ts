import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import { newDbId } from '@malou-io/package-models';
import { MalouErrorCode, MediaType, PostPublicationStatus } from '@malou-io/package-utils';

import ':helpers/tests/testing-utils';
import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultPlatform } from ':modules/platforms/tests/platform.builder';
import { getDefaultPost } from ':modules/posts/tests/posts.builder';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';

import { PlatformPostGetter } from '../../platforms/getter';
import PostsRepository from '../../posts.repository';
import { PublishStoryUseCase } from './publish-story.use-case';

describe('PublishStoryUseCase', () => {
    beforeAll(async () => {
        registerRepositories(['PostsRepository', 'PlatformsRepository', 'RestaurantsRepository']);
    });

    it('should throw if platform not found', async () => {
        const publishStoryUseCase = container.resolve(PublishStoryUseCase);

        const testCase = new TestCaseBuilderV2<'restaurants' | 'platforms' | 'posts'>({
            seeds: {
                restaurants: {
                    data: () => [getDefaultRestaurant().build()],
                },
                platforms: {
                    data: () => [],
                },
                posts: {
                    data: (deps) => [getDefaultPost().restaurantId(deps.restaurants()[0]._id).build()],
                },
            },
            expectedErrorCode: MalouErrorCode.PLATFORM_NOT_FOUND,
        });

        await testCase.build();
        const expectedErrorCode = testCase.getExpectedErrorCode();
        const seededObjects = testCase.getSeededObjects();
        const post = seededObjects.posts[0];

        await expect(
            publishStoryUseCase.execute(
                {
                    plannedPublicationDate: post.plannedPublicationDate!,
                    postId: post._id,
                    creationId: 'creationId',
                    type: MediaType.PHOTO,
                },
                newDbId()
            )
        ).rejects.toThrow(
            expect.objectContaining({
                malouErrorCode: expectedErrorCode,
            })
        );
    });

    it('should return nothing if publishStory returns empty object', async () => {
        const publishStoryUseCase = container.resolve(PublishStoryUseCase);
        const platformPostGetter = container.resolve(PlatformPostGetter);
        platformPostGetter.getPlatformPostUseCases = jest.fn().mockReturnValue({
            publishStory: jest.fn().mockResolvedValue({}),
        });

        const testCase = new TestCaseBuilderV2<'restaurants' | 'platforms' | 'posts'>({
            seeds: {
                restaurants: {
                    data: () => [getDefaultRestaurant().build()],
                },
                platforms: {
                    data: (deps) => [getDefaultPlatform().restaurantId(deps.restaurants()[0]._id).build()],
                },
                posts: {
                    data: (deps) => [getDefaultPost().restaurantId(deps.restaurants()[0]._id).build()],
                },
            },
        });

        await testCase.build();
        const seededObjects = testCase.getSeededObjects();
        const post = seededObjects.posts[0];
        const platform = seededObjects.platforms[0];

        const result = await publishStoryUseCase.execute(
            {
                plannedPublicationDate: post.plannedPublicationDate!,
                postId: post._id,
                creationId: 'creationId',
                type: MediaType.PHOTO,
            },
            platform._id
        );
        expect(result).toBeUndefined();
    });

    it('should call publish story and return correct story', async () => {
        const publishStoryUseCase = container.resolve(PublishStoryUseCase);
        const platformPostGetter = container.resolve(PlatformPostGetter);

        const testCase = new TestCaseBuilderV2<'restaurants' | 'platforms' | 'posts'>({
            seeds: {
                restaurants: {
                    data: () => [getDefaultRestaurant().build()],
                },
                platforms: {
                    data: (deps) => [getDefaultPlatform().restaurantId(deps.restaurants()[0]._id).build()],
                },
                posts: {
                    data: (deps) => [
                        getDefaultPost()
                            .restaurantId(deps.restaurants()[0]._id)
                            .platformId(deps.platforms()[0]._id)
                            .published(PostPublicationStatus.PENDING)
                            .plannedPublicationDate(DateTime.now().toJSDate())
                            .build(),
                    ],
                },
            },
        });
        await testCase.build();
        const seededObjects = testCase.getSeededObjects();
        const post = seededObjects.posts[0];
        const platform = seededObjects.platforms[0];

        const publishStorySpy = jest.spyOn(platformPostGetter, 'getPlatformPostUseCases').mockReturnValue({
            ...platformPostGetter.getPlatformPostUseCases(platform.key),
            publishStory: jest.fn().mockResolvedValue({
                ...post,
                socialId: 'story_social_id',
                published: PostPublicationStatus.PUBLISHED,
            }),
        });

        const result = await publishStoryUseCase.execute(
            {
                plannedPublicationDate: post.plannedPublicationDate!,
                postId: post._id,
                creationId: 'creationId',
                type: MediaType.PHOTO,
            },
            platform._id
        );
        expect(result).toBeDefined();
        expect(result?.socialId).toBe('story_social_id');
        expect(result?.published).toBe(PostPublicationStatus.PUBLISHED);
        expect(publishStorySpy).toHaveBeenCalled();
    });

    it('if story is already existing in db because sync was done in parallel, it should remove the pending version and update the published one with the pending version data', async () => {
        const publishStoryUseCase = container.resolve(PublishStoryUseCase);
        const platformPostGetter = container.resolve(PlatformPostGetter);

        const testCase = new TestCaseBuilderV2<'restaurants' | 'platforms' | 'posts'>({
            seeds: {
                restaurants: {
                    data: () => [getDefaultRestaurant().build()],
                },
                platforms: {
                    data: (deps) => [getDefaultPlatform().restaurantId(deps.restaurants()[0]._id).build()],
                },
                posts: {
                    data: (deps) => {
                        const restaurantId = deps.restaurants()[0]._id;
                        const platformId = deps.platforms()[0]._id;
                        return [
                            getDefaultPost()
                                ._id(newDbId())
                                .restaurantId(restaurantId)
                                .platformId(platformId)
                                .published(PostPublicationStatus.PENDING)
                                .plannedPublicationDate(DateTime.now().toJSDate())
                                .author({
                                    name: 'pending_author',
                                    _id: newDbId(),
                                })
                                .malouStoryId('malou_story_id')
                                .shouldDuplicateInOtherPlatforms(true)
                                .instagramCollaboratorsUsernames(['user1'])
                                .build(),
                            getDefaultPost()
                                ._id(newDbId())
                                .restaurantId(restaurantId)
                                .platformId(platformId)
                                .published(PostPublicationStatus.PUBLISHED)
                                .plannedPublicationDate(DateTime.now().toJSDate())
                                .socialId('story_social_id')
                                .author({
                                    name: 'published_author',
                                    _id: newDbId(),
                                })
                                .malouStoryId('old_story_id')
                                .shouldDuplicateInOtherPlatforms(false)
                                .instagramCollaboratorsUsernames(['user2'])
                                .build(),
                        ];
                    },
                },
            },
        });

        await testCase.build();

        const seededObjects = testCase.getSeededObjects();
        const pendingPost = seededObjects.posts.find((p) => p.published === PostPublicationStatus.PENDING)!;
        const publishedPost = seededObjects.posts.find((p) => p.published === PostPublicationStatus.PUBLISHED)!;

        platformPostGetter.getPlatformPostUseCases = jest.fn().mockReturnValue({
            publishStory: jest.fn().mockResolvedValue({
                ...pendingPost,
                socialId: publishedPost.socialId,
                published: PostPublicationStatus.PUBLISHED,
            }),
        });

        const result = await publishStoryUseCase.execute(
            {
                plannedPublicationDate: pendingPost.plannedPublicationDate!,
                postId: pendingPost._id,
                creationId: 'creationId',
                type: MediaType.PHOTO,
            },
            seededObjects.platforms[0]._id
        );
        expect(result).toBeDefined();
        expect(result?._id.toString()).toBe(publishedPost._id.toString());
        expect(result?.author?.name).toBe(pendingPost.author?.name);
        expect(result?.malouStoryId).toBe(pendingPost.malouStoryId);
        expect(result?.shouldDuplicateInOtherPlatforms).toBe(pendingPost.shouldDuplicateInOtherPlatforms);
        expect(result?.instagramCollaboratorsUsernames).toEqual(pendingPost.instagramCollaboratorsUsernames);

        // Check that the pending post is deleted
        const postsRepository = container.resolve(PostsRepository);
        const deletedPending = await postsRepository.findOne({ filter: { _id: pendingPost._id } });
        expect(deletedPending).toBeNull();
    });
});
