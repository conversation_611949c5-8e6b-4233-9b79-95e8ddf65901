import { JobAttributes } from 'agenda';
import axios from 'axios';
import fs from 'fs';
import { compact, intersection, omit, sortBy, uniq } from 'lodash';
import { DateTime } from 'luxon';
import assert from 'node:assert/strict';
import { autoInjectable, delay, inject } from 'tsyringe';
import { v4 } from 'uuid';

import { PostDto, UpdatePostPayloadDto } from '@malou-io/package-dto';
import {
    DbId,
    ID,
    IFeedback,
    IMedia,
    IPlatform,
    IPopulatedPost,
    IPost,
    IPostWithAttachments,
    IPostWithAttachmentsAndThumbnail,
    IUser,
    newDbId,
    OverwriteOrAssign,
    toDbId,
} from '@malou-io/package-models';
import {
    CallToActionType,
    facebookNewPhotoLegend,
    formatSocialNetworkPostText,
    getOccurrencesBetween,
    getPlatformKeysWithStories,
    getSocialPlatformKeysWithPost,
    getTypeFromMimetype,
    isNotNil,
    MalouErrorCode,
    MediaCategory,
    MediaType,
    PlatformKey,
    PostPublicationStatus,
    PostSource,
    postsUpdateTexts,
    PostType,
    PushNotificationSubject,
    RecurrentStoryFrequency,
    removeHashtagsFromText,
    SocialAttachmentsMediaTypes,
} from '@malou-io/package-utils';

import { AgendaSingleton } from ':helpers/classes/agenda-singleton';
import { MalouError } from ':helpers/classes/malou-error';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { PlatformInsightFiltersApiFactory } from ':helpers/filters/platform-insight-filters-api-factory';
import { PostsFilters } from ':helpers/filters/posts-filter';
import { logger } from ':helpers/logger';
import { Pagination } from ':helpers/pagination';
import { FacebookErrorCode, isFacebookError, isHashtagList, removeNullOrUndefinedField } from ':helpers/utils';
import { CommentsRepository } from ':modules/comments/comments.repository';
import * as facebookCredentialsUseCases from ':modules/credentials/platforms/facebook/facebook.use-cases';
import { Media } from ':modules/media/entities/media.entity';
import { MediasUseCases } from ':modules/media/medias.use-cases';
import { MediasRepository } from ':modules/media/repository/medias.repository';
import { MediaUploaderService } from ':modules/media/services/media-uploader/media-uploader.service';
import { NotificationsJobsUseCases } from ':modules/notifications/jobs/notifications-jobs.use-cases';
import { CreatePostErrorNotificationProducer } from ':modules/notifications/queues/create-post-error-notification/create-post-error-notification.producer';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { FacebookPostsUseCases } from ':modules/posts/platforms/facebook/use-cases';
import { GmbPostsUseCases } from ':modules/posts/platforms/gmb/use-cases';
import { InstagramPostMapper } from ':modules/posts/platforms/instagram/instagram-post-mapper';
import { InstagramPostsUseCases } from ':modules/posts/platforms/instagram/use-cases';
import { MapstrPostsUseCases } from ':modules/posts/platforms/mapstr/mapstr-post.use-cases';
import { TiktokPostsUseCases } from ':modules/posts/platforms/tiktok/tiktok-post.use-cases';
import { MalouPostData, PlatformPostUseCases } from ':modules/posts/posts.interface';
import PostsRepository from ':modules/posts/posts.repository';
import { sendPublishPost } from ':modules/posts/queues/publish-post/publish-post.producer';
import { FormatterType, MediaFormatter } from ':modules/posts/services/formatters/media-formatter.port';
import {
    MediaStoredObjectWithType,
    TransformMediaForPublicationService,
} from ':modules/posts/v2/services/transform-media-for-publication/transform-media-for-publication.service';
import { PushNotificationsUseCases, PushNotificationTypes } from ':modules/push-notifications/push-notifications.usecases';
import { lookupMediasStage } from ':modules/stories/repository/stories.pipeline';
import { UsersRepository } from ':modules/users/users.repository';
import { SlackChannel, SlackService } from ':services/slack.service';

@autoInjectable()
export default class PostsUseCases {
    constructor(
        @inject(delay(() => PostsRepository))
        private readonly _postsRepository: PostsRepository,
        @inject(delay(() => PlatformsRepository))
        private readonly _platformsRepository: PlatformsRepository,
        @inject(delay(() => MediasRepository))
        private readonly _mediasRepository: MediasRepository,
        @inject(delay(() => CommentsRepository))
        private readonly _commentsRepository: CommentsRepository,
        @inject(delay(() => FacebookPostsUseCases))
        private readonly _facebookPostsUseCases: PlatformPostUseCases,
        @inject(delay(() => InstagramPostsUseCases))
        private readonly _instagramPostsUseCases: PlatformPostUseCases,
        private readonly _mapstrPostsUseCases: MapstrPostsUseCases,
        @inject(delay(() => TiktokPostsUseCases))
        private readonly _tiktokPostsUseCases: TiktokPostsUseCases,
        private readonly _gmbPostsUseCases: GmbPostsUseCases,
        private readonly _agendaSingleton: AgendaSingleton,
        private readonly _mediasUseCases: MediasUseCases,
        private readonly _usersRepository: UsersRepository,
        private readonly _pushNotificationsUseCases: PushNotificationsUseCases,
        private readonly _notificationsJobsUseCases: NotificationsJobsUseCases,
        private readonly _mediaUploaderService: MediaUploaderService,
        private readonly _createPostErrorNotificationProducer: CreatePostErrorNotificationProducer,
        private readonly _slackService: SlackService,
        private readonly _transformMediaForPublicationService: TransformMediaForPublicationService
    ) {}

    /**
     * Schedule a post's publication by adding an agenda job to be run on post.publicationDate
     * If post was already scheduled for publication, deletes previous jobs
     */
    async schedulePreparePost<Post extends Pick<IPost, 'restaurantId' | 'published' | '_id' | 'plannedPublicationDate'>>({
        userId,
        post,
    }: {
        userId: ID;
        post: Post;
    }): Promise<Post> {
        const agenda = await this._agendaSingleton.getInstance();
        await agenda.cancel({ name: AgendaJobName.PREPARE_POST, 'data.postId': toDbId(post._id) });
        await agenda.cancel({ name: AgendaJobName.PUBLISH_POST, 'data.postId': toDbId(post._id) });
        if (post.published === PostPublicationStatus.DRAFT) {
            await this._mediasUseCases.updateMediaPostIds(post._id, { $pull: { postIds: post._id } });
            return post;
        }
        if (post.plannedPublicationDate) {
            await this._mediasUseCases.updateMediaPostIds(post._id, { $addToSet: { postIds: post._id } });
            const jobData = {
                userId: toDbId(userId),
                postId: toDbId(post._id),
            };
            logger.info('[SCHEDULE_PREPARE_POST] scheduling post:', { jobData, post });
            await this._agendaSingleton.schedule(post.plannedPublicationDate, AgendaJobName.PREPARE_POST, jobData);
        }
        return post;
    }

    /**
     * fetch post from platform
     */
    fetchPost = async ({ postId }: { postId: ID }): Promise<IPostWithAttachments> => {
        const post = await this._postsRepository.findOne({
            filter: { _id: toDbId(postId) },
            options: { populate: [{ path: 'attachments' }, { path: 'feedback' }] },
        });
        assert(post, 'Post not found');

        if (!post.key) {
            throw new MalouError(MalouErrorCode.INVALID_DATA, {
                metadata: {
                    postId,
                },
            });
        }

        let malouPost = {};
        try {
            malouPost = await this.getPlatformPostUseCases(post.key).fetchPost({ post });
        } catch (error) {
            logger.warn('[FETCH_POST_FAILED]', { postId, error });
        }

        return this._postsRepository.upsert({
            filter: { _id: toDbId(postId) },
            update: malouPost,
            options: { lean: true, populate: [{ path: 'attachments' }] },
        });
    };

    /**
     * Publish post on platform
     *
     * @deprecated Will be replaced by CreateAndSendPostOnPlatformsUseCase
     */
    preparePost = async ({ userId, postId }: { userId: ID; postId: ID }): Promise<any> => {
        logger.info('[PostsUseCases] preparePost (v1)', { userId, postId });
        const post = await this._postsRepository.findOne({
            filter: { _id: toDbId(postId) },
            options: { populate: [{ path: 'attachments' }], lean: true },
        });
        if (!post) {
            throw new MalouError(MalouErrorCode.POST_NOT_FOUND, {
                metadata: {
                    postId,
                },
            });
        }

        if (post.published === PostPublicationStatus.DRAFT) {
            return;
        }

        const keys = compact(post.keys ?? [post.key]);

        const { attachments } = post;
        const cleanAttachments: { isTransformed: boolean; media: IMedia }[] = [];

        const FEATURE_ACTIVE_DATE = DateTime.fromISO('2024-09-17T14:00:00.000Z');
        for (const medium of attachments) {
            let cleanMedium = medium;
            if (medium.isV2) {
                const isReel = post.postType === PostType.REEL;
                const formatResult: MediaStoredObjectWithType = isReel
                    ? await this._transformMediaForPublicationService.formatMediaForReel(medium)
                    : (await this._transformMediaForPublicationService.formatMediasForPost([medium], PlatformKey.INSTAGRAM))[0];
                const newId = newDbId();
                cleanMedium = await this._mediasRepository.create({
                    data: {
                        ...omit(medium, ['storedObjects', 'isV2', 'timelinePreviewFrames256h']),
                        _id: newId,
                        socialId: newId.toString(),
                        urls: { original: formatResult.storedObject.publicUrl },
                        dimensions: { original: formatResult.dimensions },
                        originalMediaId: medium._id,
                        thumbnail:
                            medium.type === MediaType.VIDEO
                                ? (medium.storedObjects?.thumbnail1024Outside.publicUrl ?? medium.thumbnail)
                                : undefined,
                        sizes: {},
                    },
                    options: { lean: true },
                });
                logger.info('[PostsUseCases] preparePost (v1): created v1 media from v2 media', {
                    newMediaId: cleanMedium._id,
                    oldMediaId: medium._id,
                });
                cleanAttachments.push({ isTransformed: true, media: cleanMedium });
            } else {
                cleanAttachments.push({ isTransformed: false, media: cleanMedium });
            }
        }
        if (DateTime.fromJSDate(post.createdAt) > FEATURE_ACTIVE_DATE && post.postType !== PostType.REEL) {
            post.attachments = await Promise.all(
                cleanAttachments.map(async (cleanAttachment) => {
                    if (cleanAttachment.isTransformed) {
                        return cleanAttachment.media;
                    } else {
                        return (
                            await this.resizeAttachmentsAndCreateNewPostMedias([new Media(cleanAttachment.media as any)], post.source)
                        )[0] as any;
                    }
                })
            );
        } else {
            post.attachments = cleanAttachments.map((cleanAttachment) => ({ ...cleanAttachment.media, id: cleanAttachment.media._id }));
        }

        for (const key of keys) {
            delete post.socialId;
            const platform = await this._platformsRepository.findOne({
                filter: { restaurantId: post.restaurantId, key },
                options: { lean: true },
            });
            const firstPhotoMedia = post.attachments.find((media) => media.type === MediaType.PHOTO);
            const postAttachments = key === PlatformKey.MAPSTR ? [firstPhotoMedia].filter((e) => !!e) : post.attachments;
            const postType: PostType = key === PlatformKey.MAPSTR ? PostType.IMAGE : post.postType;
            const postText: string =
                key === PlatformKey.MAPSTR
                    ? removeHashtagsFromText(post.text ?? '', post.hashtags?.selected?.map((h) => h.text) ?? [])
                    : (post.text ?? '');
            const sortDate = post.socialCreatedAt ?? post.plannedPublicationDate ?? new Date();
            const newPost = await this._postsRepository.create({
                data: {
                    ...omit(post, '_id'),
                    tries: 0,
                    text: postText,
                    attachments: postAttachments?.map((a: any) => toDbId(a.id)),
                    postType,
                    key,
                    keys: [key],
                    platformId: platform?._id,
                    sortDate,
                    isPublishing: true,
                    createdFromDeviceType: post.createdFromDeviceType ?? undefined,
                },
            });
            logger.info('[PostsUseCases] preparePost (v1): created post', { newPostId: newPost._id });
            const populatedPost = await this._postsRepository.findOne({
                filter: { _id: newPost._id },
                options: { populate: [{ path: 'attachments' }, { path: 'thumbnail' }], lean: true },
            });
            assert(populatedPost, 'Post not found');

            if (post.plannedPublicationDate) {
                assert(populatedPost.restaurantId, "Post's restaurantId not found");
                await sendPublishPost({
                    post: { _id: populatedPost._id.toString() },
                    restaurantId: populatedPost.restaurantId.toString(),
                    userId: userId.toString(),
                });
            }
        }
        logger.info('[PostsUseCases] preparePost (v1): success', { userId, post });
        return this._postsRepository.deleteOne({ filter: { _id: postId } });
    };

    /**
     * Publish post on platform
     */
    publishPost = async ({ postId }: { postId: string }): Promise<IPostWithAttachments | undefined> => {
        const post = await this._postsRepository.findOne({
            filter: { _id: toDbId(postId) },
            projection: { _id: 1, text: 1, restaurantId: 1 },
            options: { populate: [{ path: 'attachments' }, { path: 'thumbnail' }], lean: true },
        });
        assert(post, 'Post not found');
        try {
            await this._resizeReelBeforePublish(postId);
        } catch (error: any) {
            logger.error('[RESIZE_REEL_BEFORE_PUBLISH_FAILED]', { postId, error });
            this._slackService.sendAlert({
                channel: SlackChannel.POSTS_V1_ALERTS,
                data: {
                    err: error,
                    restaurantId: post.restaurantId.toString(),
                    metadata: { message: 'RESIZE_REEL_BEFORE_PUBLISH_FAILED', postId: post._id.toString() },
                },
            });
            if (error instanceof MalouError) {
                throw error;
            }
            // Need to map the error to a standard Error because cloudinary can throw some weirdly formatted errors
            throw new Error(error.message ?? error.error?.message);
        }

        const updatedPostByResize = await this._postsRepository.findOne({
            filter: { _id: toDbId(postId) },
            options: { populate: [{ path: 'attachments' }, { path: 'thumbnail' }], lean: true },
        });
        assert(updatedPostByResize, 'Post not found');
        assert(updatedPostByResize.key, "Post's key not found");

        const malouPost = await this.getPlatformPostUseCases(updatedPostByResize.key).publish({ post: updatedPostByResize });
        if (malouPost && Object.keys(malouPost)?.length) {
            return this._postsRepository.upsert({
                filter: { _id: toDbId(postId) },
                update: malouPost,
                options: { populate: [{ path: 'attachments' }] },
            });
        }
    };

    getCompetitorsPosts = async (platform: IPlatform): Promise<IPost[]> => {
        const credentialId = platform.credentials?.[0];
        if (!credentialId) {
            throw new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND);
        }
        return this.getPlatformPostUseCases(platform.key).getCompetitorsPosts(credentialId, platform.socialId, platform.watchedAccounts);
    };

    getPostJobAttributesForRestaurantId = async (restaurantId: string): Promise<JobAttributes[]> => {
        const agenda = await this._agendaSingleton.getInstance();
        const jobs = await agenda.jobs(
            { name: { $in: [AgendaJobName.PUBLISH_POST, AgendaJobName.PREPARE_POST] }, 'data.restaurantId': toDbId(restaurantId) },
            { _id: -1 }
        );
        return (jobs || []).map((j) => j.attrs);
    };

    /**
     * Create one post for each platform key specified and return array of unpublished posts
     */
    createPost = async (post: Partial<IPost>, isDraft: boolean, keys: string[]): Promise<IPostWithAttachmentsAndThumbnail> => {
        logger.info('[CREATE_POST] creating', post);

        // some attachment IDs are null…
        const attachmentIds: DbId[] = post.attachments?.filter(isNotNil) ?? [];

        if (attachmentIds.length) {
            const deletedMedia = await this._mediasRepository.findDeletedMedia(attachmentIds.map((id) => id.toString()));
            if (deletedMedia) {
                logger.info('[CREATE_POST] the attached media does not exist or has been deleted', {
                    mediaId: deletedMedia.id,
                    restaurantId: post.restaurantId,
                });
                throw new MalouError(MalouErrorCode.MEDIA_DELETED_FOR_POST);
            }
        }

        if (keys?.includes(PlatformKey.GMB)) {
            Object.assign(post, { source: PostSource.SEO });
        }
        if (intersection(keys, getSocialPlatformKeysWithPost()).length > 0) {
            post.text = formatSocialNetworkPostText(post.text ?? '');
            Object.assign(post, { source: PostSource.SOCIAL });
        }
        Object.assign(post, { keys: keys ?? [], bindingId: v4() });
        if (isDraft) {
            Object.assign(post, { published: PostPublicationStatus.DRAFT });
        }
        const sortDate = post.socialCreatedAt ?? post.plannedPublicationDate ?? new Date();
        const newPost = await this._postsRepository.create({
            data: { ...post, sortDate },
            options: {
                lean: true,
            },
        });
        logger.info('[CREATE_POST] created', newPost);

        return this._postsRepository.findOneOrFail({
            filter: { _id: newPost._id },
            options: { populate: [{ path: 'attachments' }, { path: 'thumbnail' }], lean: true },
        });
    };

    duplicatePost = async (
        post: Partial<IPost>,
        isDraft: boolean,
        keys: string[],
        duplicatePostId: string
    ): Promise<IPostWithAttachmentsAndThumbnail> => {
        const originalPost = await this._postsRepository.findOne({
            filter: { _id: duplicatePostId },
            options: { lean: true },
        });
        // META bug : reels with copyrighted audio don't have media_url
        // https://developers.facebook.com/support/bugs/404495981650858/
        if (
            originalPost &&
            originalPost.key === PlatformKey.INSTAGRAM &&
            originalPost.postType === PostType.REEL &&
            originalPost.socialAttachments?.[0]?.type !== SocialAttachmentsMediaTypes.VIDEO &&
            originalPost.published === PostPublicationStatus.PUBLISHED
        ) {
            throw new MalouError(MalouErrorCode.REEL_WITHOUT_VIDEO, {
                metadata: { postId: duplicatePostId },
                message: "[DUPLICATE POST ERROR] Can't duplicate a reel without video",
            });
        }
        const updatedOriginalPost = await this._postsRepository.findOneAndUpdate({
            filter: { _id: duplicatePostId },
            update: { shouldDuplicateInOtherPlatforms: false },
            options: { lean: true },
        });
        assert(updatedOriginalPost, 'Post not found');
        post.duplicatedFromRestaurantId = updatedOriginalPost.restaurantId;
        return this.createPost(post, isDraft, keys);
    };

    mapPayloadToPostData = (update: UpdatePostPayloadDto): Partial<IPost> => {
        const mappedUpdate = {
            ...update,
            plannedPublicationDate: update.plannedPublicationDate ? DateTime.fromISO(update.plannedPublicationDate).toJSDate() : null,
            platformId: update.platformId ? toDbId(update.platformId) : undefined,
            attachments: update.attachments ? update.attachments.map((a) => toDbId(a)) : undefined,
            feedbackId: update.feedbackId ? toDbId(update.feedbackId) : undefined,
            thumbnail: update.thumbnail ? toDbId(update.thumbnail) : undefined,
            callToAction: update.callToAction
                ? {
                      actionType: (update.callToAction?.actionType as CallToActionType) ?? CallToActionType.NONE,
                      url: update.callToAction?.url ?? '',
                  }
                : null,
            event: update.event
                ? {
                      title: update.event?.title ?? '',
                      startDate: update.event?.startDate
                          ? DateTime.fromISO(update.event?.startDate).toJSDate()
                          : DateTime.now().minus({ days: 1 }).toJSDate(),
                      endDate: update.event?.endDate
                          ? DateTime.fromISO(update.event?.endDate).toJSDate()
                          : DateTime.now().minus({ days: 0 }).toJSDate(),
                  }
                : undefined,
            offer: update.offer
                ? {
                      couponCode: update.offer?.couponCode ?? '',
                      onlineUrl: update.offer?.onlineUrl ?? '',
                      termsConditions: update.offer?.termsConditions ?? '',
                  }
                : undefined,
            location: update.location
                ? {
                      id: update.location?.id ?? '',
                      name: update.location?.name ?? '',
                      link: update.location?.link ?? '',
                      location: update.location?.location
                          ? {
                                city: update.location?.location?.city ?? '',
                                country: update.location?.location?.country ?? '',
                                latitude: update.location?.location?.latitude ?? 0,
                                longitude: update.location?.location?.longitude ?? 0,
                                street: update.location?.location?.street ?? '',
                                zip: update.location?.location?.zip ?? '',
                            }
                          : undefined,
                  }
                : undefined,
            userTagsList: update.userTagsList
                ? update.userTagsList?.map((uts) =>
                      uts.map((ut) => ({
                          username: ut?.username ?? '',
                          x: ut?.x ?? 0,
                          y: ut?.y ?? 0,
                      }))
                  )
                : undefined,
            title: update.title,
            createdFromDeviceType: update.createdFromDeviceType ?? undefined,
        };
        return removeNullOrUndefinedField(mappedUpdate);
    };

    updatePost = async (postId: ID, update: UpdatePostPayloadDto): Promise<IPostWithAttachments> => {
        const post = await this._postsRepository.findOne({
            filter: { _id: toDbId(postId) },
            options: { lean: true, populate: [{ path: 'attachments' }, { path: 'thumbnail' }] },
        });
        assert(post, 'Post not found');

        const mappedUpdate = this.mapPayloadToPostData(update);
        if (mappedUpdate.text) {
            if (intersection(post.keys ?? [], getSocialPlatformKeysWithPost()).length > 0) {
                mappedUpdate.text = formatSocialNetworkPostText(update.text ?? '');
            }
        }
        const medias = await this._mediasRepository.find({ filter: { _id: { $in: mappedUpdate?.attachments ?? [] } } });
        for (const media of medias) {
            if (media.deletedAt) {
                logger.info('[UPDATE_POST] the attached media does not exist or has been deleted', {
                    mediaId: media._id,
                    restaurantId: post.restaurantId,
                });
                throw new MalouError(MalouErrorCode.MEDIA_DELETED_FOR_POST);
            }
        }
        const postType = post.isStory ? this.getStoryTypeFromAttachments(medias) : this.getPostTypeFromAttachments(medias);
        if (postType) {
            mappedUpdate.postType = postType;
        }
        if (this.hasBeenPosted(post.published)) {
            assert(post.socialId, "Post's socialId not found");
            assert(post.key, "Post's key not found");
            const updated = await this.updatePostOnPlatform(toDbId(post.socialId), post.key, {
                ...post,
                ...mappedUpdate,
                attachments: post.attachments,
                thumbnail: post.thumbnail,
            });
            Object.assign(mappedUpdate, { ...(updated || {}) });
        }

        // Use updateOne to have the sortDate computation
        await this._postsRepository.updateOne({
            filter: { _id: toDbId(postId) },
            update: mappedUpdate,
        });

        const updatedPost = await this._postsRepository.findOne({
            filter: { _id: toDbId(postId) },
            options: { lean: true, populate: [{ path: 'attachments' }] },
        });

        if (!updatedPost) {
            throw new MalouError(MalouErrorCode.POST_NOT_FOUND, {
                metadata: {
                    postId,
                },
            });
        }

        return updatedPost;
    };

    updatePostOnPlatform = async (
        socialId: ID,
        platformKey: PlatformKey,
        update: Partial<IPostWithAttachmentsAndThumbnail>
    ): Promise<IPost> => {
        const mappedPost = await this.getPlatformPostUseCases(platformKey)?.updatePost({ post: { ...update, socialId } });
        return { ...update, ...mappedPost };
    };

    deletePostById = async ({ user, postId }: { user: IUser; postId: string }): Promise<any> => {
        logger.info('[DELETE_POST_BY_ID]', { postId });

        try {
            const post = await this._postsRepository.findOne({
                filter: { _id: toDbId(postId) },
                options: { populate: [{ path: 'attachments' }, { path: 'feedback' }] },
            });

            if (!post) {
                throw new MalouError(MalouErrorCode.POST_NOT_FOUND, {
                    metadata: {
                        postId,
                    },
                });
            }

            const agenda = await this._agendaSingleton.getInstance();
            await agenda.cancel({ name: AgendaJobName.PREPARE_POST, 'data.postId': post?._id });
            await agenda.cancel({ name: AgendaJobName.PUBLISH_POST, 'data.postId': post?._id });
            if (post?.malouStoryId) {
                await agenda.cancel({ name: AgendaJobName.INITIALIZE_STORY, 'data.malouStoryId': post.malouStoryId });
            }
            if (this.hasBeenPosted(post.published) && post.key) {
                await this.getPlatformPostUseCases(post.key).deletePost({ user, post });
            }
        } catch (err: any) {
            if (
                (!err.message || !err.message.includes('not_found')) &&
                !err.message?.includes(MalouErrorCode.PLATFORM_NOT_FOUND) &&
                !err.message?.includes('Unsupported delete request.')
            ) {
                throw err;
            }
        }
        await this._mediasUseCases.updateMediaPostIds(postId, { $pull: { postIds: postId } });
        return this._postsRepository.deleteOne({ filter: { _id: toDbId(postId) } });
    };

    deleteManyPostsAndHandleSideEffects = async (filter: any): Promise<any> => {
        const posts = await this._postsRepository.find({
            filter,
            options: { populate: [{ path: 'attachments' }, { path: 'thumbnail' }, { path: 'feedback' }] },
        });
        logger.info('[DELETE_MANY_POSTS_AND_HANDLE_SIDE_EFFECTS]', { postIds: posts.map((p) => p._id) });

        await this._postsRepository.deleteMany({ filter });

        const commentsFilter = { postSocialId: { $in: posts.map(({ socialId }) => socialId) } };
        return this._commentsRepository.deleteMany({ filter: commentsFilter });
    };

    getRestaurantPostsPaginated = async ({
        pagination,
        filters,
    }: {
        pagination: Pagination;
        filters: PostsFilters;
    }): Promise<PostDto[]> => {
        const posts = await this._postsRepository.getRestaurantPostsPaginated({ pagination, filters });
        return posts;
    };

    getRestaurantPostsCount = ({ filters }: { filters: PostsFilters }): Promise<number> =>
        this._postsRepository.getRestaurantPostsCount({ filters });

    fetchPostsWithInsights = ({
        restaurantId,
        startDate,
        endDate,
        platformKeys: keys,
    }: {
        restaurantId: string;
        startDate: string;
        endDate: string;
        platformKeys: PlatformKey[];
    }): Promise<Record<PlatformKey, { data?: IPost[]; error?: boolean; message?: string }>[]> => {
        const promises: Promise<Record<PlatformKey, { data?: IPost[]; error?: boolean; message?: string }>>[] = [];
        for (const key of keys) {
            const filters = PlatformInsightFiltersApiFactory.createPlatformInsightFiltersApi(key, { startDate, endDate });
            const insights = this.getPlatformPostUseCases(key)
                .fetchPostsWithInsights(restaurantId, filters)
                .then((res) => ({ [key]: { data: res } }))
                .catch((e) => ({ [key]: { error: true, message: e?.message ?? String(e), stack: e?.stack } }));
            promises.push(insights);
        }
        return Promise.all(promises);
    };

    getPlatformPostUseCases = (key: PlatformKey): PlatformPostUseCases => {
        const platformUseCases = {
            [PlatformKey.FACEBOOK]: this._facebookPostsUseCases,
            [PlatformKey.GMB]: this._gmbPostsUseCases,
            [PlatformKey.INSTAGRAM]: this._instagramPostsUseCases,
            [PlatformKey.MAPSTR]: this._mapstrPostsUseCases,
            [PlatformKey.TIKTOK]: this._tiktokPostsUseCases,
        }[key];

        if (!platformUseCases) {
            throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
                metadata: {
                    platformKey: key,
                },
            });
        }

        return platformUseCases;
    };

    refresh = async (postId: string) => {
        const post = await this._postsRepository.findOne({
            filter: { _id: toDbId(postId) },
            options: { lean: true, populate: [{ path: 'attachments' }, { path: 'feedback' }] },
        });

        if (post?.published === PostPublicationStatus.PUBLISHED) {
            const freshPost = await this.fetchPost({ postId });
            logger.info('[REFRESH_POST] published', { postId });
            return freshPost;
        } else {
            logger.info('[REFRESH_POST] unpublished', { postId });
        }
        return post;
    };

    getPostsBetweenDays = async (
        restaurantId: string,
        startDate: string,
        endDate: string
    ): Promise<
        OverwriteOrAssign<
            IPopulatedPost,
            {
                medias: { uploadedMedia: IMedia; editedMedia?: IMedia }[];
            }
        >[]
    > => {
        const startDay = new Date(startDate);
        const endDay = new Date(endDate);
        const matchStage = {
            $match: {
                restaurantId: toDbId(restaurantId),
                $or: [{ plannedPublicationDate: { $gte: startDay, $lte: endDay } }, { socialCreatedAt: { $gte: startDay, $lte: endDay } }],
                text: { $nin: postsUpdateTexts.GMB },
            },
        };
        const lookupFeedbacksStages = [
            {
                $lookup: {
                    from: 'feedbacks',
                    localField: 'feedbackId',
                    foreignField: '_id',
                    as: 'feedback',
                },
            },
            {
                $unwind: {
                    path: '$feedback',
                    preserveNullAndEmptyArrays: true,
                },
            },
        ];
        const thumbnailLookupStages = [
            {
                $lookup: {
                    from: 'media',
                    localField: 'thumbnail',
                    foreignField: '_id',
                    as: 'thumbnail',
                },
            },
            {
                $unwind: {
                    path: '$thumbnail',
                    preserveNullAndEmptyArrays: true,
                },
            },
        ];

        const lookupAttachmentsStage = [
            {
                $lookup: {
                    from: 'media',
                    localField: 'attachments',
                    foreignField: '_id',
                    as: 'attachments_docs',
                },
            },
            // This is a trick to keep the original order of attachments
            {
                $addFields: {
                    attachments: {
                        $map: {
                            input: '$attachments', // original array of ObjectIds
                            as: 'id',
                            in: {
                                $arrayElemAt: ['$attachments_docs', { $indexOfArray: ['$attachments_docs._id', '$$id'] }],
                            },
                        },
                    },
                },
            },
            { $project: { attachments_docs: 0 } },
        ] as const;

        const posts = (await this._postsRepository.aggregate([
            matchStage,
            ...lookupAttachmentsStage,
            ...lookupMediasStage,
            ...lookupFeedbacksStages,
            ...thumbnailLookupStages,
        ])) as unknown as OverwriteOrAssign<
            IPopulatedPost,
            {
                medias: { uploadedMedia: IMedia; editedMedia?: IMedia }[];
            }
        >[];

        // Get recurrent stories that are outside the range but will be published in the range
        const matchRecurrentStoryStage = {
            $match: {
                restaurantId: toDbId(restaurantId),
                isStory: true,
                recurrentStoryFrequency: { $exists: true, $ne: RecurrentStoryFrequency.NONE },
                published: { $in: [PostPublicationStatus.PENDING, PostPublicationStatus.DRAFT] },
                plannedPublicationDate: { $exists: true, $ne: null, $lte: endDay },
            },
        };

        const pipeline = [
            matchRecurrentStoryStage,
            ...lookupAttachmentsStage,
            ...lookupMediasStage,
            ...lookupFeedbacksStages,
            ...thumbnailLookupStages,
        ];
        const recurrentStories = (await this._postsRepository.aggregate(pipeline)) as OverwriteOrAssign<
            IPopulatedPost,
            {
                medias: { uploadedMedia: IMedia; editedMedia?: IMedia }[];
            }
        >[];

        // Filter out stories that are in recurrent stories to prevent duplicates
        const recurrentStoriesIds = recurrentStories.map((story) => story._id.toString());
        const filteredPosts = posts.filter((post) => {
            return !recurrentStoriesIds.includes(post._id.toString());
        });

        for (const story of recurrentStories) {
            const occurrencesInPeriod = getOccurrencesBetween(
                startDay,
                endDay,
                story.recurrentStoryFrequency!,
                story.plannedPublicationDate!
            );
            for (const occurrence of occurrencesInPeriod) {
                filteredPosts.push({
                    ...story,
                    plannedPublicationDate: occurrence,
                });
            }
        }
        return filteredPosts;
    };

    getPublishedPostsForPerformanceReport = async ({
        restaurantIds,
        startDate,
        endDate,
        platformKey,
    }: {
        restaurantIds: DbId[];
        startDate: Date;
        endDate: Date;
        platformKey: PlatformKey;
    }) => {
        return this._postsRepository.find({
            filter: {
                restaurantId: { $in: restaurantIds },
                socialCreatedAt: { $gte: startDate, $lte: endDate },
                key: platformKey,
                published: PostPublicationStatus.PUBLISHED,
                ...(platformKey === PlatformKey.GMB && {
                    source: PostSource.SEO,
                    text: { $nin: postsUpdateTexts.GMB },
                }),
            },
        });
    };

    getPlannedPostsForPerformanceReport = async ({
        restaurantIds,
        startDate,
        endDate,
        platformKey,
    }: {
        restaurantIds: DbId[];
        startDate: Date;
        endDate: Date;
        platformKey: PlatformKey;
    }) => {
        return this._postsRepository.find({
            filter: {
                restaurantId: { $in: restaurantIds },
                plannedPublicationDate: { $gte: startDate, $lte: endDate },
                published: PostPublicationStatus.PENDING,
                keys: platformKey,
            },
        });
    };

    async countStoriesByAuthor(authorId: ID): Promise<number> {
        return this._postsRepository.countDocuments({ filter: { isStory: true, 'author._id': authorId } });
    }

    fetchLastIgPosts = async (restaurantId: string, maxPostsNumber: string): Promise<MalouPostData[]> => {
        const platform = await this._platformsRepository.getPlatformByRestaurantIdAndPlatformKey(restaurantId, PlatformKey.INSTAGRAM);
        if (!platform) {
            return [];
        }
        const credentialId = platform.credentials?.[0];
        if (!credentialId) {
            throw new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND);
        }
        const postMapper = new InstagramPostMapper();
        assert(platform.socialId, 'Missing socialId on platform');
        const result = await facebookCredentialsUseCases.igGetPagePosts(credentialId, platform.socialId, parseInt(maxPostsNumber, 10));
        const igPosts = result?.media?.data;
        if (!igPosts) {
            return [];
        }
        const mappedPosts = igPosts.map((post) => postMapper.mapToMalouPost({ post, platform }));
        return mappedPosts;
    };

    upsertPostFromFacebookWebhook = async (mappedPost: MalouPostData): Promise<IPost> => {
        if (mappedPost.key === PlatformKey.FACEBOOK) {
            const postBySocialId = await this._postsRepository.findOne({
                filter: {
                    restaurantId: mappedPost.restaurantId,
                    socialId: mappedPost.socialId,
                },
            });
            if (postBySocialId) {
                return this._postsRepository.upsertPostByRestaurantIdAndSocialId(mappedPost);
            }
            if (
                mappedPost.text?.length &&
                !facebookNewPhotoLegend.filter((fakeLegendRegex) => mappedPost.text?.match(fakeLegendRegex)).length
            ) {
                const postByText = await this._postsRepository.findOne({
                    filter: {
                        restaurantId: mappedPost.restaurantId,
                        key: PlatformKey.FACEBOOK,
                        text: mappedPost.text,
                        socialCreatedAt: mappedPost.socialCreatedAt,
                    },
                });
                if (postByText) {
                    return this._postsRepository.upsertPostByTextAndSocialCreatedAt(mappedPost);
                }
            }
        }
        return this._postsRepository.upsertPostByRestaurantIdAndSocialId(mappedPost);
    };

    /**
     * first comment from author if matches a hashtags list
     */
    getPostFirstCommentFromAuthor = async ({ post }: { post: IPost | any }): Promise<string | undefined> => {
        try {
            const platform = await this._platformsRepository.getPlatformById(post.platformId);
            const credentialId = platform?.credentials?.[0];
            if (!credentialId) {
                throw new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND);
            }
            switch (post.key) {
                case PlatformKey.FACEBOOK:
                    assert(platform.socialId, 'Missing socialId on platform');
                    const fetchedPostFb = await facebookCredentialsUseCases.getPost(credentialId, post.socialId, platform.socialId);
                    const firstCommentFb = sortBy(fetchedPostFb.comments?.data, 'created_time')?.[0];
                    return firstCommentFb?.from?.id === fetchedPostFb.from?.id && isHashtagList(firstCommentFb.message ?? '')
                        ? firstCommentFb.message
                        : undefined;
                case PlatformKey.INSTAGRAM:
                    assert(platform.socialId, 'Missing socialId on platform');
                    const fetchedPostIg = await facebookCredentialsUseCases.igGetPost(credentialId, post.socialId, platform.socialId);
                    const firstCommentIg = sortBy(fetchedPostIg.comments?.data, 'timestamp')?.[0];
                    return firstCommentIg?.username === fetchedPostIg.username && isHashtagList(firstCommentIg.text)
                        ? firstCommentIg.text
                        : undefined;
                default:
                    return;
            }
        } catch (e) {
            logger.warn('[GET_FIRST_COMMENT_ERROR]', post?.key, e);
        }
    };

    hasBeenPosted = (status: PostPublicationStatus): boolean =>
        status === PostPublicationStatus.PUBLISHED || status === PostPublicationStatus.REJECTED;

    sendPublicationFailedEmail = async (postId: ID): Promise<void> => {
        const post = await this._postsRepository.findOne({
            filter: { _id: toDbId(postId) },
            options: { lean: true, populate: [{ path: 'feedback' }, { path: 'attachments' }] },
        });
        assert(post, 'Post not found');

        if (post.author?._id) {
            await this._createPostErrorNotificationProducer.execute({ postId: post._id.toString() });
        } else {
            logger.warn('[sendPublicationFailedEmail] Missing author id in post', { postId });
        }
    };

    sendPublicationFailedPushNotification = async (
        post: { restaurantId: string; _id: string },
        involvedPeopleIds: string[]
    ): Promise<void> => {
        try {
            const usersToNotify = await this._usersRepository.getUsersByIds(involvedPeopleIds);

            usersToNotify.forEach(async (user) => {
                const title = await this._notificationsJobsUseCases.getTranslatedTitleFromType(
                    PushNotificationTypes.ERROR_PUBLISHING_POST,
                    {
                        language: user.defaultLanguage,
                        restaurantId: post.restaurantId.toString(),
                    }
                );
                const text = await this._notificationsJobsUseCases.getTranslatedTextFromType(
                    PushNotificationTypes.ERROR_PUBLISHING_POST,
                    '',
                    {
                        language: user.defaultLanguage,
                        restaurantId: post.restaurantId.toString(),
                    }
                );
                this._pushNotificationsUseCases
                    .sendPushNotification(
                        [user],
                        {
                            title,
                            text,
                            subject: PushNotificationSubject.POST,
                            data: {
                                isTranslationRequired: 'true',
                                type: PushNotificationTypes.ERROR_PUBLISHING_POST,
                                restaurantId: post.restaurantId.toString(),
                                postId: post._id.toString(),
                            },
                        },
                        { fromRealtime: false }
                    )
                    .catch((e) => {
                        logger.error('[PUSH_NOTIFICATION_ERROR] sendPublicationFailedPushNotification', e);
                    });
            });
        } catch (e) {
            logger.error('[PUSH_NOTIFICATION_ERROR] sendPublicationFailedPushNotification', e);
        }
    };

    synchronizeRestaurantStories = async (restaurantId: string): Promise<IPostWithAttachments[]> => {
        logger.info('[SYNCHRONIZE_RESTAURANT_STORIES]', { restaurantId });
        const platforms = await this._platformsRepository.getPlatformsByRestaurantIdAndPlatformKeys(
            restaurantId,
            getPlatformKeysWithStories()
        );

        let mappedStoriesPerPlatform: Partial<IPost>[][];
        try {
            mappedStoriesPerPlatform = await Promise.all(
                platforms.map((platform) => this.getPlatformPostUseCases(platform.key).synchronizeStories(platform))
            );
        } catch (error: any) {
            if (isFacebookError(error)) {
                if (error.code === FacebookErrorCode.OAUTH) {
                    throw new MalouError(MalouErrorCode.CREDENTIALS_FACEBOOK_ERROR);
                }
            }
            throw error;
        }

        // TODO: with this logic we do not handle stories that may have been deleted from the apps (still appear as active in front)
        const upsertPromises = mappedStoriesPerPlatform
            .filter((mappedStories) => mappedStories?.length)
            .map((platformPosts) =>
                platformPosts.map((post) =>
                    post.key
                        ? this.getPlatformPostUseCases(post.key).upsertStoryAndSaveAttachments(
                              restaurantId,
                              { socialId: post.socialId, platformId: post.platformId },
                              post
                          )
                        : undefined
                )
            )
            .flat();
        return Promise.all(compact(upsertPromises));
    };

    schedulePrepareStory = async (keys: string[], malouStoryId: string): Promise<void> => {
        logger.info('[SCHEDULE_INITIALIZE_STORY]', { malouStoryId, keys });
        const posts = await this._postsRepository.find({
            filter: { isStory: true, malouStoryId },
            options: { lean: true, sort: { plannedPublicationDate: -1 } },
        });
        for (const post of posts) {
            assert(post.attachments?.length, `the post ${post._id} has no attachments`);
        }

        const agenda = await this._agendaSingleton.getInstance();
        await agenda.cancel({ name: AgendaJobName.INITIALIZE_STORY, 'data.malouStoryId': malouStoryId });
        const firstPost = posts[0];
        const { restaurantId } = firstPost;

        await this._postsRepository.updateMany({
            filter: { malouStoryId },
            update: { published: PostPublicationStatus.PENDING },
            options: { new: true, lean: true },
        });

        if (firstPost.plannedPublicationDate) {
            const jobData = {
                keys,
                malouStoryId,
                restaurantId: restaurantId.toString(),
            };
            logger.info('[SCHEDULE_INITIALIZE_STORY] scheduling story:', { jobData, firstPost });
            await this._agendaSingleton.schedule(firstPost.plannedPublicationDate, AgendaJobName.INITIALIZE_STORY, jobData);
        }
    };

    cancelStory = async (malouStoryId: string): Promise<void> => {
        logger.info('[CANCEL_STORY]', { malouStoryId });
        const agenda = await this._agendaSingleton.getInstance();
        await agenda.cancel({ name: AgendaJobName.INITIALIZE_STORY, 'data.malouStoryId': malouStoryId });
        await this._postsRepository.updateMany({ filter: { malouStoryId }, update: { published: PostPublicationStatus.DRAFT } });
    };

    getPostTypeFromAttachments = (attachments: IMedia[]): PostType | undefined => {
        if (!attachments?.length) return;
        if (attachments.length > 1) {
            return PostType.CAROUSEL;
        }
        if (attachments.length === 1) {
            const [media] = attachments;

            if (media?.type === MediaType.VIDEO) {
                return PostType.REEL;
            }
            if (media?.type === MediaType.PHOTO) {
                return PostType.IMAGE;
            }
        }
    };

    getStoryTypeFromAttachments = (attachments: IMedia[]): PostType | undefined => {
        if (!attachments?.length) return;

        assert(attachments.length <= 1, 'Stories cannot have multiple attachments');

        const [media] = attachments;

        if (media?.type === MediaType.VIDEO) {
            return PostType.VIDEO;
        }
        if (media?.type === MediaType.PHOTO) {
            return PostType.IMAGE;
        }
    };

    private async _downloadMediaFromUrl(url: string, pathName: string): Promise<{ type: MediaType; format: string; pathName: string }> {
        const response = await axios({
            method: 'GET',
            url,
            responseType: 'stream',
        });

        const mimeType = response.headers['content-type'];

        const outputStream = fs.createWriteStream(pathName);
        response.data.pipe(outputStream);

        await new Promise((resolve, reject) => {
            outputStream.on('finish', resolve);
            outputStream.on('error', reject);
        });

        return {
            type: getTypeFromMimetype(mimeType),
            format: mimeType.split('/')[1],
            pathName,
        };
    }

    resizeAttachmentsAndCreateNewPostMedias = async (
        postMedias: Media[],
        postSource: PostSource,
        options?: {
            shouldForceResizeToRecommendedSize?: boolean;
        },
        postType?: PostType
    ): Promise<Media[]> => {
        logger.info('[PostsUseCases] resizeAttachmentsAndCreateNewPostMedias (v1)', { mediaIds: postMedias.map((m) => m.id) });
        const promises = postMedias.map(async (postMedia) => {
            const inputFilePath = `downloadedMedias/${postMedia.id}_${v4()}.${postMedia.urls.original.split('.com/')[1].split('.')[1]}`;
            await this._downloadMediaFromUrl(postMedia.urls.original, inputFilePath);

            const outputFilePath = inputFilePath.replace(
                `.${postMedia.getOriginalImageExtension()}`,
                `_resized.${postMedia.getOriginalImageExtension()}`
            );
            const mediaFormatter = new MediaFormatter(postMedia, postType === PostType.REEL ? FormatterType.REELS : FormatterType.FB);

            const platformKey = postSource === PostSource.SEO ? PlatformKey.GMB : PlatformKey.INSTAGRAM;

            const resizedMediaMetadata = await mediaFormatter.formatMedia(
                inputFilePath,
                outputFilePath,
                platformKey,
                postMedia.type as MediaType,
                postMedia.getCropOptions(),
                options
            );

            assert(postMedia.dimensions, 'Missing dimensions');
            postMedia.dimensions.original = { width: resizedMediaMetadata.width, height: resizedMediaMetadata.height };

            const mediaToCreate = {
                // be sure to omit storedObjects, this is a v1 media. It can create a bug in some edge cases.
                ...omit(postMedia, 'storedObjects'),
                id: newDbId().toString(),
                restaurantId: postMedia.restaurantId?.toString(),
                userId: postMedia.userId?.toString(),
                postIds: postMedia.postIds?.map((pid) => pid.toString()),
                originalMediaId: postMedia.id?.toString(),
                category: postMedia.category ?? MediaCategory.ADDITIONAL,
                createdAt: new Date(),
                updatedAt: new Date(),
                resizeMetadata: {
                    width: resizedMediaMetadata.width,
                    height: resizedMediaMetadata.height,
                    aspectRatio: resizedMediaMetadata.width / resizedMediaMetadata.height,
                    cropPosition: { left: 0, top: 0 },
                },
                folderId: null,
                socialId: '',
                isV2: false,
            };
            const uploadedMedia = await this._mediaUploaderService.uploadFromFile(resizedMediaMetadata, mediaToCreate, {
                keepOriginalAsIgFit: true,
            });
            const createdMedia = await this._mediasRepository.createMedia(
                new Media({
                    ...mediaToCreate,
                    ...uploadedMedia,
                    // be sure to recompute the aspectRatio
                    aspectRatio: uploadedMedia.dimensions
                        ? uploadedMedia.dimensions.original.width / uploadedMedia.dimensions.original.height
                        : undefined,
                })
            );
            logger.info('[PostsUseCases] resizeAttachmentsAndCreateNewPostMedias (v1): created media', { mediaId: createdMedia.id });

            return createdMedia;
        });

        return Promise.all(promises);
    };

    async getRestaurantPostsByMediaIds(restaurantId: ID, mediaIds: ID[]): Promise<IPopulatedPost[]> {
        const promises = mediaIds.map((mediaId) =>
            this._postsRepository.find({
                filter: { restaurantId, attachments: { $elemMatch: { $eq: mediaId } } },
                options: { populate: [{ path: 'attachments' }, { path: 'feedback' }, { path: 'thumbnail' }] },
            })
        );
        const postsByMediaId = await Promise.all(promises);
        return postsByMediaId.flat(1);
    }

    _getInvolvedPeopleIds = async (post: { author?: IPost['author']; feedback?: IFeedback }): Promise<string[]> => {
        let ids = [post.author?._id].filter(Boolean);
        if ((post.feedback?.participants?.length ?? 0) > 0) {
            ids = ids.concat(post.feedback?.participants.map((user) => user.participant._id));
        }
        return uniq(ids.map((id) => id?.toString())).filter(isNotNil);
    };

    private async _resizeReelBeforePublish(postId: string): Promise<void> {
        const post = await this._postsRepository.findOne({
            filter: { _id: toDbId(postId) },
            options: { lean: true, populate: [{ path: 'attachments' }] },
        });
        assert(post, 'Post not found');

        // TODO posts-v2 remove this when TikTok is ready
        if (post.postType !== PostType.REEL || post.key === PlatformKey.TIKTOK) {
            return;
        }

        assert(post.restaurantId, 'Missing restaurantId');
        assert(post.key, 'Missing key');

        const media = new Media({
            id: post.attachments[0]._id.toString(),
            restaurantId: post.restaurantId.toString(),
            duplicatedFromRestaurantId: post.duplicatedFromRestaurantId?.toString(),
            userId: undefined,
            originalMediaId: undefined,
            postIds: post.attachments[0].postIds?.map((a) => a._id.toString()),
            ...post.attachments[0],
        } as any);
        const outputFilePath = `./downloadedMedias/${media.id}_resized.${media.getOriginalImageExtension()}`;
        const mediaFormatter = new MediaFormatter(media, FormatterType.REELS);
        const resizedMediaMetadata = await mediaFormatter.formatMedia(
            media.urls.original,
            outputFilePath,
            post.key,
            media.type as MediaType
        );

        assert(media.dimensions, 'Missing dimensions');

        media.dimensions.original = { width: resizedMediaMetadata.width, height: resizedMediaMetadata.height };

        const postToCreate = {
            ...media,
            id: newDbId().toString(),
            restaurantId: media.restaurantId?.toString(),
            userId: media.userId?.toString(),
            postIds: media.postIds?.map((pid) => pid.toString()),
            originalMediaId: media.id?.toString(),
            category: media.category ?? MediaCategory.ADDITIONAL,
            createdAt: new Date(),
            updatedAt: new Date(),
            folderId: null,
            socialId: '',
            isV2: false,
        };
        const uploadedMedia = await this._mediaUploaderService.uploadFromFile(resizedMediaMetadata, postToCreate, {
            keepOriginalAsIgFit: true,
        });
        const createdMedia = await this._mediasRepository.createMedia(new Media({ ...postToCreate, ...uploadedMedia }));

        await this._postsRepository.updateOne({
            filter: { _id: toDbId(postId) },
            update: { attachments: [toDbId(createdMedia.id)] },
        });
    }
}
