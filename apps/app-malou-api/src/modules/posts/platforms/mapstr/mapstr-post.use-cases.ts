import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { IMapstrCredential, IPost, IPostWithAttachments, IPostWithAttachmentsAndThumbnail } from '@malou-io/package-models';
import { MalouErrorCode, MediaType, PlatformKey, PostPublicationStatus } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { Platform } from ':modules/platforms/platforms.entity';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { MapstrPostMapper } from ':modules/posts/platforms/mapstr/mapstr-post.mapper';
import { MalouPostData, PlatformPostUseCases } from ':modules/posts/posts.interface';
import PostsRepository from ':modules/posts/posts.repository';
import { MapstrCreatePostStatus } from ':modules/providers/platforms/mapstr/mapstr.interface';
import { MapstrProvider } from ':modules/providers/platforms/mapstr/mapstr.provider';

@singleton()
export class MapstrPostsUseCases implements PlatformPostUseCases {
    constructor(
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _mapstrPostMapper: MapstrPostMapper,
        private readonly _mapstrProvider: MapstrProvider,
        private readonly _postsRepository: PostsRepository
    ) {}

    async updatePost(post: IPost): Promise<IPost> {
        logger.info('[MAPSTR_POSTS_USE_CASE_UPDATE_POST] Cant update Mapstr post when already published');
        return post;
    }

    async deletePost(post: IPost): Promise<IPost> {
        logger.info('[MAPSTR_POSTS_USE_CASE_DELETE_POST] Cant delete Mapstr post when already published');
        return post;
    }

    async synchronize(): Promise<MalouPostData[]> {
        return [];
    }

    async publish({ post }: { post: IPostWithAttachmentsAndThumbnail | IPostWithAttachments }): Promise<void> {
        const platform = await this._platformsRepository.findOne({
            filter: { _id: post.platformId },
            options: { populate: [{ path: 'credentials' }], lean: true },
        });

        if (!platform) {
            throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, {
                message: 'Platform not found in Mapstr posts use case',
                metadata: { platformId: post.platformId },
            });
        }
        const credential = platform.credentials[0] as IMapstrCredential;
        if (!credential) {
            throw new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND, {
                message: 'Credential not found in Mapstr posts use case',
                metadata: { platformId: post.platformId },
            });
        }
        assert(platform.socialId, 'Missing socialId on platform');
        const mapstrCreatePost = this._mapstrPostMapper.fromMalouToMapstr(post, platform.socialId, credential.accessToken);
        const response = await this._mapstrProvider.createPost(mapstrCreatePost);
        if (response.status === MapstrCreatePostStatus.ERROR) {
            logger.error(`[MAPSTR_POSTS_USE_CASES] Mapstr error`, response);
            await this._postsRepository.updateOne({
                filter: { _id: post._id },
                update: { published: PostPublicationStatus.ERROR, isPublishing: false, errorData: response.data },
            });
        } else {
            await this._postsRepository.updateOne({
                filter: { _id: post._id },
                update: { published: PostPublicationStatus.PUBLISHED, isPublishing: false },
            });
        }
    }

    fetchPost(): void {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            message: 'fetchPost is not implemented for Mapstr',
            metadata: { platformKey: PlatformKey.MAPSTR },
        });
    }

    fetchPostsWithInsights(): void {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            message: 'fetchPostsWithInsights is not implemented for Mapstr',
            metadata: { platformKey: PlatformKey.MAPSTR },
        });
    }

    publishStory(platformId: string, creationId: string, type: MediaType, postId: string): Promise<IPost> {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            message: 'publishStory is not implemented for Mapstr',
            metadata: { platformId, creationId, type, postId },
        });
    }

    synchronizeStories(platform: Platform): Promise<Partial<IPost>[]> {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            message: 'synchronizeStories is not implemented for Mapstr',
            metadata: { platform },
        });
    }

    completePublish({ post }: { post: IPostWithAttachmentsAndThumbnail | IPostWithAttachments }): Promise<MalouPostData | void> {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            message: 'completePublish is not implemented for Mapstr',
            metadata: { post },
        });
    }

    getCompetitorsPosts(): void {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            message: 'getCompetitorsPosts is not implemented for Mapstr',
            metadata: { platformKey: PlatformKey.MAPSTR },
        });
    }

    createStoryList(): void {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            message: 'createStoryList is not implemented for Mapstr',
            metadata: { platformKey: PlatformKey.MAPSTR },
        });
    }

    upsertStoryAndSaveAttachments(): void {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            message: 'upsertStoryAndSaveAttachments is not implemented for Mapstr',
            metadata: { platformKey: PlatformKey.MAPSTR },
        });
    }

    searchAccounts(): void {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            message: 'searchAccounts is not implemented for Mapstr',
            metadata: { platformKey: PlatformKey.MAPSTR },
        });
    }

    oembed(): void {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            message: 'oembed is not implemented for Mapstr',
            metadata: { platformKey: PlatformKey.MAPSTR },
        });
    }
}
