import { Job } from 'agenda';
import { singleton } from 'tsyringe';
import { z } from 'zod';

import { errorReplacer, PostError, PostPublicationStatus } from '@malou-io/package-utils';

import { GenericJobDefinition } from ':agenda-jobs/job-template/generic-job-definition';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { logger } from ':helpers/logger';
import { publishStoryValidator } from ':helpers/validators/jobs/posts-jobs.validators';
import { StoryToPublish } from ':modules/posts/posts.interface';
import PostsRepository from ':modules/posts/posts.repository';
import { PublishStoryUseCase } from ':modules/posts/use-cases/publish-story/publish-story.use-case';

type DataAttributes = z.infer<typeof publishStoryValidator>;

@singleton()
export class PublishStoryJob extends GenericJobDefinition {
    constructor(
        private readonly _postsRepository: PostsRepository,
        private readonly _publishStoryUseCase: PublishStoryUseCase
    ) {
        super({
            agendaJobName: AgendaJobName.PUBLISH_STORY,
            shouldDeleteJobOnSuccess: true,
        });
    }

    async executeJob(job: Job<DataAttributes>): Promise<void> {
        const { storyList, platformId } = publishStoryValidator.parse(job.attrs.data);
        const [storyToPublish] = storyList;

        try {
            await this._publishStoryUseCase.execute(storyToPublish as StoryToPublish, platformId);
            const newStoryList = storyList.slice(1);

            if (!newStoryList.length) {
                logger.info('No more story to publish');
                return;
            }

            const [nextStoryToPublish] = newStoryList;

            const jobData = { storyList: newStoryList, platformId };
            logger.info('[PostsJobs] [PUBLISH_STORY] - Scheduling next story', { nextStoryToPublish, jobData });

            const agenda = await this._agendaSingleton.getInstance();
            await agenda.schedule(nextStoryToPublish.plannedPublicationDate, AgendaJobName.PUBLISH_STORY, jobData);
        } catch (error: any) {
            await this._postsRepository.updateOne({
                filter: { _id: storyToPublish.postId },
                update: {
                    published: PostPublicationStatus.ERROR,
                    errorData: error?.message ?? JSON.stringify(error, errorReplacer),
                    errorStage: AgendaJobName.PUBLISH_STORY,
                    isPublishing: false,
                },
            });
            const post = await this._postsRepository.findOne({ filter: { _id: storyToPublish.postId } });
            if (post?.malouStoryId) {
                await this._postsRepository.updateMany({
                    filter: { malouStoryId: post.malouStoryId, published: PostPublicationStatus.PENDING, platformId },
                    update: {
                        published: PostPublicationStatus.ERROR,
                        errorData: PostError.STORY_NOT_PUBLISHED_BECAUSE_PREVIOUS_ONE_WAS_NOT_PUBLISHED,
                        errorStage: AgendaJobName.PUBLISH_STORY,
                        isPublishing: false,
                    },
                });
            }

            throw error;
        }
    }
}
