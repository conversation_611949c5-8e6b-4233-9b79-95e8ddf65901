import { Job } from 'agenda';
import { singleton } from 'tsyringe';
import { z } from 'zod';

import { errorReplacer, PostPublicationStatus } from '@malou-io/package-utils';

import { GenericJobDefinition } from ':agenda-jobs/job-template/generic-job-definition';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { initializeStoryValidator } from ':helpers/validators/jobs/posts-jobs.validators';
import PostsRepository from ':modules/posts/posts.repository';
import { InitializeStoryUseCase } from ':modules/posts/use-cases/initialize-story/initialize-story.use-case';

type DataAttributes = z.infer<typeof initializeStoryValidator>;

@singleton()
export class InitializeStoryJob extends GenericJobDefinition {
    // This step will create documents for all medias & all keys at the time of publishing, and then start the "prepare" phase
    constructor(
        private readonly _postsRepository: PostsRepository,
        private readonly _initializeStoryUseCase: InitializeStoryUseCase
    ) {
        super({
            agendaJobName: AgendaJobName.INITIALIZE_STORY,
            shouldDeleteJobOnSuccess: true,
        });
    }

    async executeJob(job: Job<DataAttributes>): Promise<void> {
        const { malouStoryId, restaurantId, keys } = initializeStoryValidator.parse(job.attrs.data);

        try {
            await this._initializeStoryUseCase.execute(restaurantId, malouStoryId, keys);
        } catch (err: any) {
            await this._postsRepository.updateMany({
                filter: { isStory: true, malouStoryId },
                update: {
                    published: PostPublicationStatus.ERROR,
                    errorData: err?.message ?? JSON.stringify(err, errorReplacer),
                    errorStage: AgendaJobName.INITIALIZE_STORY,
                    isPublishing: false,
                },
            });
            throw err;
        }
    }
}
