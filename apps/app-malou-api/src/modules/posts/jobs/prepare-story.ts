import { Job } from 'agenda';
import { singleton } from 'tsyringe';
import { z } from 'zod';

import { errorReplacer, PostPublicationStatus } from '@malou-io/package-utils';

import { GenericJobDefinition } from ':agenda-jobs/job-template/generic-job-definition';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { prepareStoryValidator } from ':helpers/validators/jobs/posts-jobs.validators';
import PostsRepository from ':modules/posts/posts.repository';

import { PrepareStoryUseCase } from '../use-cases/prepare-story/prepare-story.use-case';

type DataAttributes = z.infer<typeof prepareStoryValidator>;

@singleton()
export class PrepareStoryJob extends GenericJobDefinition {
    constructor(
        private readonly _postsRepository: PostsRepository,
        private readonly _prepareStoryUseCase: PrepareStoryUseCase
    ) {
        super({
            agendaJobName: AgendaJobName.PREPARE_STORY,
            shouldDeleteJobOnSuccess: true,
        });
    }

    async executeJob(job: Job<DataAttributes>): Promise<void> {
        const { malouStoryId, restaurantId, platformKey } = prepareStoryValidator.parse(job.attrs.data);

        try {
            await this._prepareStoryUseCase.execute(restaurantId, malouStoryId, platformKey);
        } catch (err: any) {
            await this._postsRepository.updateMany({
                filter: { isStory: true, malouStoryId, key: platformKey },
                update: {
                    published: PostPublicationStatus.ERROR,
                    errorData: err?.message ?? err?.malouErrorCode ?? JSON.stringify(err, errorReplacer),
                    errorStage: AgendaJobName.PREPARE_STORY,
                    isPublishing: false,
                },
            });
            throw err;
        }
    }
}
