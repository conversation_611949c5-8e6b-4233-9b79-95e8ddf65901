import { singleton } from 'tsyringe';

import { toDbId } from '@malou-io/package-models';
import { PlatformKey } from '@malou-io/package-utils';

import { AgendaSingleton } from ':helpers/classes/agenda-singleton';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { logger } from ':helpers/logger';
import PostsRepository from ':modules/posts/posts.repository';

@singleton()
export class ScheduleStoryPublicationService {
    constructor(
        private readonly _agendaSingleton: AgendaSingleton,
        private readonly _postsRepository: PostsRepository
    ) {}

    async scheduleStoryPublication(storyId: string, date: Date): Promise<void> {
        await this.cancelStoryPublication(storyId);
        await this._agendaSingleton.schedule(date, AgendaJobName.PUBLISH_STORY_ON_PLATFORM, {
            storyId,
        });

        logger.info('[STORY PUBLICATION] Rescheduled story publication', { storyId, date });
    }

    async schedulePostPublicationV1(restaurantId: string, date: Date, malouStoryId: string, keys: PlatformKey[]): Promise<void> {
        await this._agendaSingleton.schedule(date, AgendaJobName.INITIALIZE_STORY, {
            restaurantId,
            keys,
            malouStoryId,
        });

        logger.info('[STORY PUBLICATION] Scheduled story publication V1', { restaurantId, keys, malouStoryId });
    }

    async cancelStoryPublication(storyId: string): Promise<void> {
        const deletedJobsCount = await this._agendaSingleton.deleteJobs({
            name: AgendaJobName.PUBLISH_STORY_ON_PLATFORM,
            'data.storyId': { $in: [storyId, toDbId(storyId)] },
        });

        // Delete v1 jobs
        // TODO stories-v2 : delete after stories-v1 are removed
        const story = await this._postsRepository.findById(storyId);
        let deletedV1JobsCount: number | undefined;
        const malouStoryId = story?.malouStoryId;
        const restaurantId = story?.restaurantId;
        if (malouStoryId && restaurantId) {
            deletedV1JobsCount = await this._agendaSingleton.deleteJobs({
                name: { $in: [AgendaJobName.INITIALIZE_STORY, AgendaJobName.PREPARE_STORY, AgendaJobName.PUBLISH_STORY] },
                'data.malouStoryId': malouStoryId,
                'data.restaurantId': { $in: [restaurantId, toDbId(restaurantId)] },
            });
        }

        logger.info('[STORY PUBLICATION] Cancelled story publication', { storyId, deletedJobsCount, deletedV1JobsCount });
    }
}
