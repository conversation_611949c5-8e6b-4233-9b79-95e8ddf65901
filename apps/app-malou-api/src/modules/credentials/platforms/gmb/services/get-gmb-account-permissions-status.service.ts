import { singleton } from 'tsyringe';

import { IGmbCredential } from '@malou-io/package-models';
import { InvalidPlatformReason, scopes } from '@malou-io/package-utils';

import { RecommendationReason } from ':modules/credentials/platforms/gmb/interfaces';
import { CredentialValidityStatus } from ':modules/credentials/platforms/interfaces';
import { Platform } from ':modules/platforms/platforms.entity';
import { GmbBusinessInformationProvider } from ':providers/google/gmb.business-information.provider';
import { GmbBusinessVerificationsProvider } from ':providers/google/gmb.business-verifications.provider';
import { GmbRefreshTokenService } from ':services/credentials/gmb/gmb-refresh-token.service';

@singleton()
export class GetGmbAccountPermissionsStatusService {
    constructor(
        private readonly _gmbBusinessInformationProvider: GmbBusinessInformationProvider,
        private readonly _gmbBusinessVerificationsProvider: GmbBusinessVerificationsProvider,
        private readonly _gmbRefreshTokenService: GmbRefreshTokenService
    ) {}

    async execute(platform: Platform): Promise<CredentialValidityStatus<InvalidPlatformReason>> {
        const { credentials, apiEndpointV2 } = platform;
        const credentialId = credentials?.[0];
        if (!credentialId || !apiEndpointV2) {
            return { isValid: false, missing: [] };
        }

        const locationId = apiEndpointV2.replace('locations/', '');

        let credential: IGmbCredential;
        try {
            credential = await this._gmbRefreshTokenService.getFreshTokenIfNecessary(credentialId);
        } catch (error) {
            return { isValid: false, missing: [], miscellaneous: InvalidPlatformReason.GMB_DISCONNECTED };
        }

        try {
            await this._gmbBusinessInformationProvider.getLocation({
                accessToken: credential.accessToken,
                locationId,
                readMask: 'name',
            });
        } catch (error) {
            return { isValid: false, missing: [], miscellaneous: InvalidPlatformReason.GMB_DISCONNECTED };
        }

        const { data } = await this._gmbBusinessVerificationsProvider.getVoiceOfMerchantState({
            accessToken: credential.accessToken,
            locationId,
        });
        if (data?.complyWithGuidelines?.recommendationReason === RecommendationReason.BUSINESS_LOCATION_DISABLED) {
            return { isValid: false, missing: [], miscellaneous: InvalidPlatformReason.GMB_DISABLED };
        }

        if (data?.complyWithGuidelines?.recommendationReason === RecommendationReason.BUSINESS_LOCATION_SUSPENDED) {
            return { isValid: false, missing: [], miscellaneous: InvalidPlatformReason.GMB_SUSPENDED };
        }

        if (!data?.hasVoiceOfMerchant) {
            if (data.verify?.hasPendingVerification) {
                return { isValid: false, missing: [], miscellaneous: InvalidPlatformReason.GMB_PENDING_VERIFICATION };
            }

            return { isValid: false, missing: [], miscellaneous: InvalidPlatformReason.GMB_NOT_VERIFIED };
        }

        const scope = credential.scope?.split(' ');
        const missing = scopes.GMB.reduce((acc, next) => (scope?.find((s) => s === next) ? acc : [...acc, next]), [] as string[]);
        return { isValid: !missing.length, missing };
    }
}
