import axios, { AxiosRequestConfig } from 'axios';
import { shuffle, uniqBy } from 'lodash';
import assert from 'node:assert/strict';
import { inject, singleton } from 'tsyringe';
import UserAgent from 'user-agents';

import { IPlatform, IReview, IReviewComment } from '@malou-io/package-models';
import { isFulfilled, MalouErrorCode, PlatformKey, TimeInMilliseconds, TimeInSeconds, waitFor } from '@malou-io/package-utils';

import { Config } from ':config';
import { MalouError } from ':helpers/classes/malou-error';
import { InjectionToken } from ':helpers/injection';
import { logger } from ':helpers/logger';
import { fetchUntilWorks } from ':microservices/node-crawler';
import {
    GqlLaFourchetteReviewMapped,
    GqlLaFourchetteReviewV2WithLanguage,
    TheForkReviewGqlResponse,
} from ':modules/reviews/platforms/lafourchette/reviews.interface';
import { ReviewMapper } from ':modules/reviews/reviews.mapper';
import { reviewsFetchCounter, theForkReviewApiBypassCounter } from ':modules/reviews/reviews.metrics';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import { PlatformReplyPayload, PlatformReviewsUseCases, ReviewInputWithRestaurantAndPlatformIds } from ':modules/reviews/reviews.types';
import { Cache } from ':plugins/cache';

type TheForkGqlHeaders = { 'User-Agent': string; Cookie: string; 'Accept-Language': string; Accept: string; 'Content-Type': string };

@singleton()
export default class TheForkReviewsUseCases implements PlatformReviewsUseCases<IReview> {
    constructor(
        private readonly _reviewsRepository: ReviewsRepository,
        @inject(InjectionToken.Cache) private readonly _cache: Cache
    ) {}

    private readonly _proxy = {
        protocol: 'http',
        host: Config.services.brightData.proxyHost,
        port: Config.services.brightData.proxyPort,
        auth: {
            username: Config.services.brightData.residentialProxyUsername,
            password: Config.services.brightData.residentialProxyPassword!,
        },
    };
    async getReviewsData(
        { socialId, restaurantId }: { socialId?: string; restaurantId?: string },
        recentOnly: boolean
    ): Promise<
        | GqlLaFourchetteReviewMapped[]
        | {
              error: boolean;
              message: MalouErrorCode;
          }
    > {
        try {
            assert(socialId, 'Missing socialId');
            const currentReviews =
                (await this._reviewsRepository.find({
                    filter: { restaurantId, key: PlatformKey.LAFOURCHETTE },
                    projection: { lang: 1 },
                    options: { lean: true },
                })) ?? [];
            const reviews = await this._fetchReviews(socialId, recentOnly, currentReviews);
            return this._mapRawReviews(uniqBy(reviews, 'id'));
        } catch (error) {
            logger.warn('[LAFOURCHETTE_REVIEWS_ERROR] - Error fetching reviews', { socialId, restaurantId, error });
            return { error: true, message: MalouErrorCode.PLATFORM_DATA_CRAWLING_ERROR };
        }
    }

    mapReviewsDataToMalou(platform: IPlatform, reviewsData): ReviewInputWithRestaurantAndPlatformIds[] {
        const filteredReviews = reviewsData.map((review) => ReviewMapper.mapToMalouReview(platform, review)).filter((r) => r.socialId);
        return filteredReviews;
    }

    /**
     * Update Reviews in malou database with new review reply
     * @param {Object} comment
     * @param {string} comment.comment
     */

    reply({ comment }: { comment: PlatformReplyPayload }): Promise<any> {
        return Promise.resolve(comment);
    }

    pushReviewComment = ({ socialId, key, comment }: { socialId: string; key: PlatformKey; comment: IReviewComment }) =>
        this._reviewsRepository.pushReviewComment({ socialId, key, comment });

    updateComment() {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, { message: 'TheForkReviewsUseCases does not implement updateComment !' });
    }

    async fetchTotalReviewCount(_restaurantId: string): Promise<number> {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            message: 'TheForkReviewsUseCases does not implement fetchTotalReviewCount',
        });
    }

    private _mapRawReviews(rawPlatformReviews: GqlLaFourchetteReviewV2WithLanguage[]): GqlLaFourchetteReviewMapped[] {
        return rawPlatformReviews.map((rev) => ({
            reviewRating: {
                ratingValue: rev.ratingValue,
            },
            reviewBody: rev.review?.reviewBody,
            mealDate: rev.mealDate,
            language: rev.language,
            restaurantReply: rev.restaurantReply?.body
                ? {
                      body: rev.restaurantReply?.body,
                      status: rev.restaurantReply?.status,
                  }
                : undefined,
            id: rev.id,
            author: {
                id: rev.reviewer.id,
                image: rev.reviewer.avatarUrl,
                givenName: rev.reviewer.firstName,
                familyName: rev.reviewer.lastName,
            },
        }));
    }

    private _fetchReviews = async (
        socialId: string,
        recentOnly: boolean,
        currentReviews: { lang?: string | null }[],
        maxCookieRetries: number = 5
    ): Promise<GqlLaFourchetteReviewV2WithLanguage[]> => {
        const reviewsPerPage = 100;
        const maxOffset = recentOnly ? 99 : 99;

        const { cookies } = await this._getDatadomeCookies(maxCookieRetries);
        if (cookies.length === 0) {
            logger.error('[ERROR_NO_COOKIES_AVAILABLE_THE_FORK]', { socialId });
            reviewsFetchCounter.add(1, {
                source: PlatformKey.LAFOURCHETTE,
                status: 'failure',
            });
            throw new MalouError(MalouErrorCode.PLATFORM_DATA_CRAWLING_ERROR, {
                message: 'No cookies available for TheFork requests',
            });
        }

        let firstCall: TheForkReviewGqlResponse | undefined = undefined;

        logger.info('[THE_FORK_COOKIE_RETRY_INFO]', {
            socialId,
            availableCookies: cookies.length,
        });

        try {
            firstCall = await this._multipleTriesCallingTheForkApi(cookies, { socialId, language: 'fr' });
            logger.info('[FIRST_CALL_THE_FORK_SUCCESS]', { socialId });
        } catch (error) {
            logger.error('[ERROR_FIRST_CALL_THE_FORK_ALL_COOKIES_FAILED]', {
                socialId,
                error,
            });
            reviewsFetchCounter.add(1, {
                source: PlatformKey.LAFOURCHETTE,
                status: 'failure',
            });
            throw error;
        }
        if (!firstCall?.[0]?.data?.restaurantRatingsList) {
            logger.warn('[COULD_NOT_FETCH_FIRST_PAGE_THE_FORK_REVIEWS]', { socialId, firstCall });
            reviewsFetchCounter.add(1, {
                source: PlatformKey.LAFOURCHETTE,
                status: 'failure',
            });
            throw new MalouError(MalouErrorCode.PLATFORM_THE_FORK_REVIEWS_MALFORMED_RESPONSE);
        }
        const {
            data: {
                ratingSummary,
                restaurantRatingsList: { ratings: firstRawReviews },
            },
        } = firstCall[0];

        if (!ratingSummary?.languageStats) {
            // Sometimes TheFork does not send back the ratingSummary. We send back the first 100 reviews we got
            logger.warn('[NO_RATING_SUMMARY_FROM_THE_FORK]', { socialId, firstCall });
            reviewsFetchCounter.add(1, {
                source: PlatformKey.LAFOURCHETTE,
                status: 'success',
            });
            return firstRawReviews.map((r) => ({ ...r, language: r.review ? 'fr' : null }));
        }
        const { languageStats } = ratingSummary;
        const requestConfigs: { body: { socialId: string; offset: number; language: string | null }; language: string }[] = [];
        for (const languageKey in languageStats) {
            if (!languageStats[languageKey]) {
                continue;
            }
            const currentLangExistingReviews = currentReviews.filter((r) => r.lang == (languageKey === 'null' ? null : languageKey));
            const minReviewCountBeforeFetchingEverytime = 10;
            if (
                languageStats[languageKey] <= minReviewCountBeforeFetchingEverytime &&
                languageStats[languageKey] === currentLangExistingReviews.length
            ) {
                continue;
            }
            const reviewCount = languageStats[languageKey];
            const maxIndex = Math.min(maxOffset, reviewCount);
            for (let index = 0; index <= maxIndex; index += reviewsPerPage) {
                requestConfigs.push({
                    body: { socialId, offset: index, language: languageKey === 'null' ? null : languageKey },
                    language: languageKey,
                });
            }
        }
        const responses = await Promise.allSettled(
            requestConfigs.map((conf) =>
                this._multipleTriesCallingTheForkApi(cookies, conf.body).then((res) => ({ data: res, language: conf.language }))
            )
        );
        const results = responses.filter(isFulfilled).map((r) => (r as PromiseFulfilledResult<any>).value);
        const rawReviewList: any[] = [];
        for (const { data, language } of results) {
            const rawReviews = data?.[0]?.data?.restaurantRatingsList?.ratings;
            if (rawReviews?.length) rawReviewList.push(rawReviews.map((r) => ({ ...r, language: language === 'null' ? null : language })));
        }
        // We concat the first results because it has better chance of being successful. Add them at the end of the array so they are less prioritized when using uniqBy
        const reviews = rawReviewList.flat().concat(firstRawReviews.map((r) => ({ ...r, language: r.review ? 'undetermined' : null })));
        if (!reviews.length) {
            logger.error('[EMPTY_THE_FORK_REVIEWS]', { socialId });
        } else {
            logger.info('[RETURNING_THE_FORK_REVIEWS]', { socialId, reviewCount: reviews.length });
        }
        reviewsFetchCounter.add(1, {
            source: PlatformKey.LAFOURCHETTE,
            status: 'success',
        });
        return reviews;
    };

    private _multipleTriesCallingTheForkApi = async (
        cookies: string[],
        {
            socialId,
            offset = 0,
            language = null,
            limit = 100,
        }: {
            socialId: string;
            offset?: number;
            language: string | null;
            limit?: number;
        }
    ) => {
        let success = false;
        let lastError: unknown;
        const maxAttempts = cookies.length;
        for (let i = 0; i < maxAttempts; i++) {
            try {
                const config: AxiosRequestConfig = {
                    method: 'POST',
                    url: 'https://www.thefork.fr/api/graphql',
                    headers: this._getGraphQlHeaders(cookies[i]),
                    data: this._buildReviewsBodyForGraphQl({ socialId, offset, language, limit }),
                    proxy: this._proxy,
                };
                const response = await axios.request(config);
                theForkReviewApiBypassCounter.add(1, {
                    status: 'success',
                });
                success = true;
                return response.data;
            } catch (error) {
                lastError = error;
            }
        }
        if (!success) {
            logger.error('[THE_FORK_API_CALL_FAILED_ALL_COOKIES]', { socialId, error: lastError });
            theForkReviewApiBypassCounter.add(1, {
                status: 'failure',
            });
            throw lastError;
        }
    };

    private _buildReviewsBodyForGraphQl = ({
        socialId,
        offset = 0,
        language = null,
        limit = 100,
    }: {
        socialId: string;
        offset?: number;
        language: string | null;
        limit?: number;
    }) => [
        {
            operationName: 'getRatingsList',
            variables: {
                language: language,
                occasion: null,
                orderBy: 'MEAL_DATE',
                pagination: {
                    limit: limit,
                    offset: offset,
                },
                restaurantId: socialId.toString(),
                sortDirection: 'DESC',
                withReview: language ? 'WITH_REVIEW' : 'ALL',
            },
            query: 'query getRatingsList($restaurantId: ID!, $pagination: PaginationParams, $sortDirection: SortDirection, $withReview: ReviewFilterEnum, $orderBy: RatingSortingEnum, $language: String, $occasion: RatingOccasionEnum, $keywordUuid: ID) { restaurant(restaurantId: $restaurantId) { id seoId slug name aggregateRatings { thefork { reviewCount } } } ratingSummary(restaurantId: $restaurantId) { reviewCount ratingCount languageStats } restaurantRatingsList( restaurantId: $restaurantId pagination: $pagination orderBy: $orderBy withReview: $withReview sortDirection: $sortDirection language: $language occasion: $occasion keywordUuid: $keywordUuid ) { ratings { id uuid ratingValue mealDate review { reviewBody } reviewer { id avatarUrl firstName lastName reviewCount } restaurantReply { body status } photos { id thumbnailUrl photoUrl likedByCustomer likes } likes keywordsPosition { start end } } pagination { hasNext } } }',
        },
    ];

    private _getGraphQlHeaders = (datadomeCookie?: string): TheForkGqlHeaders => {
        const userAgent = new UserAgent().toString();
        const head = {
            'User-Agent': userAgent,
            Cookie: 'datadome=kaaGYx2puzPnd6dWwXZYpSTZ8QAL0DVZCEEshVt6XCnPax8wku_LqHLjZ6IkT9622lHSBQTqwcfo~ibZ79JThzmii4LHorzHNGsStjWU9GgsK9kdDaZH6~BTyiLsw6wo; Max-Age=31536000; Domain=.thefork.fr; Path=/; Secure; SameSite=Lax',
            'Accept-Language': 'fr-FR',
            Accept: '*/*',
            'Content-Type': 'application/json',
        };
        if (datadomeCookie) {
            head.Cookie = datadomeCookie;
        }
        return head;
    };

    private _fetchDatadomeCookie = (): Promise<{ cookie?: string; status?: number } | undefined> => {
        return fetchUntilWorks({
            params: {
                url: 'https://api-js.datadome.co/js/',
                headers: {
                    'Content-type': 'application/x-www-form-urlencoded; charset=utf-8',
                },
                method: 'POST',
                body: {
                    ddk: '4980A61279181687DE605B235F81B9',
                    Referer: 'https%3A%2F%2Fwww.thefork.fr%2Frestaurant%2Fla-civette-jean-bart-r479923',
                },
                timeout: 5000,
            },
            isResponseValid: (res) => {
                return !!(res?.status === 200 && res?.cookie);
            },
            retries: 1,
        });
    };

    private _getDatadomeCookies = async (count: number = 1): Promise<{ cookies: string[]; clearKeys: string[] }> => {
        const getCookieKey = (index: number) => `the-fork-datadome-cookie-${index}`;
        const allCookieKeys = Array.from(Array(10).keys()).map(getCookieKey);

        const selectedKeys = shuffle(allCookieKeys).slice(0, Math.min(count, 10));

        logger.info('[THE_FORK_DATADOME_COOKIES] - Attempting to get cookies', {
            requestedCount: count,
            selectedKeys: selectedKeys.length,
        });

        const cookiePromises = selectedKeys.map((key) => this._getSingleCookie(key));
        const results = await Promise.all(cookiePromises);

        logger.info('[THE_FORK_DATADOME_COOKIES] - Cookie retrieval results', {
            requestedCount: count,
        });

        return {
            cookies: results.map((s) => s.cookie),
            clearKeys: results.map((s) => s.clearKey),
        };
    };

    private _getSingleCookie = async (cookieKey: string): Promise<{ cookie: string; clearKey: string }> => {
        const waitForValidCookie = async (cookieObj: { cookie: string; date: string }) => {
            const cookieSetTime = new Date(cookieObj.date).getTime();
            const currentTime = Date.now();
            const elapsed = currentTime - cookieSetTime;
            const waitTime = TimeInMilliseconds.MINUTE - elapsed;

            if (waitTime > 0) {
                await waitFor(waitTime);
            }
        };

        const fetchAndStoreCookie = async (key: string): Promise<string> => {
            const res = await this._fetchDatadomeCookie();
            const cookie = res!.cookie!;
            await this._cache.set(key, JSON.stringify({ cookie, date: new Date().toISOString() }), TimeInSeconds.MINUTE * 120);
            return cookie;
        };

        const cachedCookieStr = (await this._cache.get(cookieKey)) as string;

        if (!cachedCookieStr?.length) {
            logger.info('[THE_FORK_DATADOME_COOKIE] - No cookie found, fetching a new one', { cookieKey });
            const newCookie = await fetchAndStoreCookie(cookieKey);
            await waitFor(TimeInMilliseconds.MINUTE);
            fetchAndStoreCookie(cookieKey).catch((e) => {
                logger.info('[THE_FORK_DATADOME_COOKIE] - Error while fetching a new cookie', { cookieKey, error: e });
            });
            return { cookie: newCookie, clearKey: cookieKey };
        }

        const cachedCookie: { cookie: string; date: string } = JSON.parse(cachedCookieStr);
        await waitForValidCookie(cachedCookie);
        logger.info('[THE_FORK_DATADOME_COOKIE] - Using cached cookie', { cookieKey });
        fetchAndStoreCookie(cookieKey).catch((e) => {
            logger.info('[THE_FORK_DATADOME_COOKIE] - Error while refreshing cookie', { cookieKey, error: e });
        }); // we reset the cookie each time we use it once
        return { cookie: cachedCookie.cookie, clearKey: cookieKey };
    };
}
