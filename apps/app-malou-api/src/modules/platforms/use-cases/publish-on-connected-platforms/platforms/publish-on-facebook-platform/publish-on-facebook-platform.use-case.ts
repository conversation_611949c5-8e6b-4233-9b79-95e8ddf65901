import { omit, pick, uniq } from 'lodash';
import { singleton } from 'tsyringe';

import { DbId } from '@malou-io/package-models';
import {
    exponentialBackoffDelay,
    FbErrorCodeOrMessage,
    InvalidPlatformReason,
    isNotNil,
    MalouErrorCode,
    PlatformKey,
    waitFor,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { FacebookPageInfo } from ':modules/credentials/platforms/facebook/facebook.types';
import * as facebookCredentialsUseCases from ':modules/credentials/platforms/facebook/facebook.use-cases';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { FacebookMapper } from ':modules/platforms/platforms/facebook/facebook-mapper';
import {
    PublishOnPlatformResponse,
    RestaurantPopulatedToPublish,
} from ':modules/platforms/use-cases/publish-on-connected-platforms/publish-on-connected-platforms.use-case';
import { FacebookError } from ':modules/posts/use-cases/publish-post/interface';
import { SlackChannel, SlackService } from ':services/slack.service';

@singleton()
export class PublishOnFacebookPlatformUseCase {
    private readonly _MAX_RETRIES_COUNT = 3;

    constructor(
        private _platformsRepository: PlatformsRepository,
        private _slackService: SlackService
    ) {}

    async execute({
        restaurant,
        keysToUpdate,
        retryCount = 0,
    }: {
        restaurant: RestaurantPopulatedToPublish;
        keysToUpdate: (keyof RestaurantPopulatedToPublish)[];
        retryCount?: number;
    }): Promise<PublishOnPlatformResponse> {
        const restaurantId = restaurant._id.toString();

        try {
            const errors: { field: string; reason: string }[] = [];

            const platform = await this._platformsRepository.getPlatformByRestaurantIdAndPlatformKey(restaurantId, PlatformKey.FACEBOOK);

            const pageId = platform?.socialId;
            const credentialId = platform?.credentials?.[0]?.toString();
            if (!credentialId || !pageId) {
                throw new MalouError(MalouErrorCode.PLATFORM_FB_NOT_CONNECTED, { metadata: { restaurantId } });
            }

            const isBrandPage = await facebookCredentialsUseCases.isBrandPage(credentialId, pageId);
            const mapper = new FacebookMapper(isBrandPage);

            const facebookKeysToUpdate: (keyof RestaurantPopulatedToPublish)[] = uniq([
                ...keysToUpdate,
                // category can only be updated using field categoryList on Facebook
                // if categoryList is not present, we need to add it to trigger categories fetch in the mapper
                ...((keysToUpdate.includes('category') ? ['categoryList'] : []) as (keyof RestaurantPopulatedToPublish)[]),
                '_id',
            ]);
            const dataToUpdate = pick(restaurant, facebookKeysToUpdate);
            let dataToPublish: Partial<FacebookPageInfo> = await mapper.toPlatformMapper(dataToUpdate);
            dataToPublish = mapper.getDataForUpdate(dataToPublish);

            // Check for missing mapping between GMB and Facebook categories
            void this._checkMissingCategoryMapping({ restaurant: dataToUpdate, dataToPublish }).catch((err) =>
                logger.error('[FACEBOOK PUBLISH] Failed to send notification', err)
            );

            const { logo, cover, logoChanged, coverChanged } = restaurant;

            if (logoChanged) {
                try {
                    await this._uploadProfilePicture(mapper, logo, credentialId, pageId);
                } catch (err) {
                    logger.error('[FACEBOOK PUBLISH] Profile picture', { restaurantId, err });
                    errors.push({
                        field: 'logo',
                        reason: this._mapFbErrorToPlatformReason(err),
                    });
                }
            }

            if (coverChanged) {
                try {
                    dataToPublish = await this._uploadCoverPhoto(credentialId, pageId, cover, dataToPublish);
                } catch (err) {
                    logger.error('[FACEBOOK PUBLISH] Cover picture', { restaurantId, err });
                    errors.push({
                        field: 'cover',
                        reason: this._mapFbErrorToPlatformReason(err),
                    });
                }
            }

            const { errors: errorsForFields } = await this.publishFields({ credentialId, pageId, dataToPublish, restaurantId });

            errors.push(...errorsForFields);

            return {
                success: errorsForFields.length === 0,
                errors,
                shouldAlert: this._shouldAlert(errors),
            };
        } catch (err: any) {
            logger.error('[FACEBOOK PUBLISH] Error while publishing on Facebook', {
                restaurantId,
                err,
            });

            // Usually this happens when Facebook API is temporarily unavailable
            if (retryCount < this._MAX_RETRIES_COUNT && this._shouldRetryError(err)) {
                logger.warn('[FACEBOOK PUBLISH] Error while publishing on Facebook but will retry', {
                    restaurantId,
                    keysToUpdate,
                    err,
                    retryCount,
                });
                const delay = exponentialBackoffDelay(retryCount); // arbitrary and exponential delay to not overload the API but still retry quickly
                await waitFor(delay);
                return this.execute({ restaurant, keysToUpdate, retryCount: retryCount + 1 });
            }

            const errors = [
                {
                    field: 'platform',
                    reason: this._mapFbErrorToPlatformReason(err),
                },
            ];

            return {
                success: false,
                errors,
                shouldAlert: this._shouldAlert(errors),
            };
        }
    }

    private async _uploadCoverPhoto(
        credentialId: string,
        pageId: string,
        cover: {
            urls: { original: string };
        },
        dataToPublish: Partial<FacebookPageInfo>
    ) {
        const fbCoverPostId = await facebookCredentialsUseCases.uploadUnpublishedPhoto(credentialId, pageId, cover.urls.original);
        if (fbCoverPostId) {
            dataToPublish = { ...dataToPublish, cover: fbCoverPostId };
        }
        return dataToPublish;
    }

    private async _uploadProfilePicture(
        mapper: FacebookMapper,
        logo: {
            urls: { original: string };
        },
        credentialId: string,
        pageId: string
    ) {
        const mappedLogo = mapper.toPlatformLogo(logo);
        return facebookCredentialsUseCases.publishPageProfilePicture(credentialId, mappedLogo, pageId);
    }

    async publishFields({
        restaurantId,
        credentialId,
        pageId,
        dataToPublish,
    }: {
        restaurantId: string;
        credentialId: string;
        pageId: string;
        dataToPublish: Partial<FacebookPageInfo>;
    }): Promise<{ errors: { field: string; reason: string }[] }> {
        // temporary_status field should be updated last because it can't be set before restaurant's hours are set
        // delivery_and_pickup_option_info field should be updated last because it can't be set if restaurant is not a Restaurant type (so categories update should occur before this field update)
        const fieldsToDelay: string[] = ['temporary_status', 'delivery_and_pickup_option_info'];

        const primaryMappedData = omit(dataToPublish, fieldsToDelay);
        const secondaryMappedData = pick(dataToPublish, fieldsToDelay);

        const potentialErrors: {
            field: string;
            reason: string;
        }[] = [];

        potentialErrors.push(
            ...(
                await Promise.all(
                    Object.keys(primaryMappedData).map((field) =>
                        this._publishField({ restaurantId, credentialId, pageId, dataToPublish, field })
                    )
                )
            ).filter(isNotNil)
        );

        potentialErrors.push(
            ...(
                await Promise.all(
                    Object.keys(secondaryMappedData).map((field) =>
                        this._publishField({ restaurantId, credentialId, pageId, dataToPublish, field })
                    )
                )
            ).filter(isNotNil)
        );

        const errors = potentialErrors.filter(isNotNil);
        return {
            errors,
        };
    }

    private async _publishField(
        {
            restaurantId,
            credentialId,
            pageId,
            dataToPublish,
            field,
        }: {
            restaurantId: string;
            credentialId: string;
            pageId: string;
            dataToPublish: Partial<FacebookPageInfo>;
            field: string;
        },
        retryCount = 0
    ): Promise<{ field: string; reason: string } | undefined> {
        const malouKey = Object.values(FacebookMapper.mappingConfiguration).find((item) => item.facebookKey === field)?.malouKey;

        try {
            const ignoreCoordinateWarningsParam = malouKey === 'address' ? { ignore_coordinate_warnings: true } : {};
            logger.info('[FACEBOOK PUBLISH] About to update info', {
                restaurantId,
                pageId,
                facebookKey: field,
                malouKey,
                value: dataToPublish[field],
            });

            await facebookCredentialsUseCases.postFields(credentialId, pageId, {
                [field]: dataToPublish[field],
                ...ignoreCoordinateWarningsParam,
            });

            logger.info('[FACEBOOK PUBLISH] Updated info', {
                restaurantId,
                pageId,
                facebookKey: field,
                malouKey,
                value: dataToPublish[field],
            });
        } catch (error: any) {
            if (retryCount < this._MAX_RETRIES_COUNT && this._shouldRetryError(error)) {
                logger.warn('[FACEBOOK PUBLISH] Error trying to update info but will retry', {
                    restaurantId,
                    pageId,
                    facebookKey: field,
                    malouKey,
                    value: dataToPublish[field],
                    error,
                    retryCount,
                });
                const delay = exponentialBackoffDelay(retryCount); // arbitrary and exponential delay to not overload the API but still retry quickly
                await waitFor(delay);
                return this._publishField({ restaurantId, credentialId, pageId, dataToPublish, field }, retryCount + 1);
            }

            logger.error('[FACEBOOK PUBLISH] Error trying to update info', {
                restaurantId,
                pageId,
                facebookKey: field,
                malouKey,
                value: dataToPublish[field],
                error,
            });

            return malouKey ? { field: malouKey, reason: this._mapFbErrorToPlatformReason(error?.message ?? '') } : undefined;
        }
    }

    private _shouldRetryError(error?: FacebookError): boolean {
        // Example of error received from Facebook API (2024-11-19T10:00:11.685Z):
        // {
        //     "message": "An unexpected error has occurred. Please retry your request later.",
        //     "type": "OAuthException",
        //     "is_transient": true,
        //     "code": 2,
        //     "fbtrace_id": "Aa8UvPlNTxy5hbvUUjXEU2A"
        // }
        return (
            !!error?.message?.match(
                /(ETIMEDOUT)|(ESOCKETTIMEDOUT)|(Service temporarily unavailable)|(Please retry your request later)|(An unknown error occurred)/
            ) || ['ETIMEDOUT', 'ESOCKETTIMEDOUT', 'ENETUNREACH'].includes(error?.code?.toString() ?? '')
        );
    }

    private _mapFbErrorToPlatformReason(error: any): InvalidPlatformReason | FbErrorCodeOrMessage {
        if (MalouError.isMalouError(error)) {
            if (
                [MalouErrorCode.PLATFORM_FB_NOT_CONNECTED, MalouErrorCode.CREDENTIALS_FACEBOOK_CANNOT_FETCH_ACCOUNTS_AND_STORES].includes(
                    error.malouErrorCode
                )
            ) {
                return InvalidPlatformReason.FB_DISCONNECTED;
            }
        }

        const errorStringified = JSON.stringify(error, Object.getOwnPropertyNames(error));
        if (errorStringified.match(/The session has been invalidated because the user changed their password/i)) {
            return InvalidPlatformReason.FB_PASSWORD_CHANGED;
        }

        if (errorStringified.match(/The user must be an administrator, editor, or moderator of the page in order to impersonate it/i)) {
            return InvalidPlatformReason.FB_USER_NEED_TO_BE_ADMIN_OR_MODERATOR_OR_EDITOR;
        }

        if (errorStringified.match(/The user has not authorized application/i)) {
            return InvalidPlatformReason.FB_USER_HAS_NOT_AUTHORIZED_APP;
        }

        if (
            errorStringified.match(
                /une taille inférieure à 4 Mo et enregistrées au format JPG, PNG, GIF, TIFF, HEIF ou WebP sont autorisées/i
            )
        ) {
            return InvalidPlatformReason.FB_UNABLE_DOWNLOAD_PHOTOS;
        }

        if (errorStringified.match(/Sessions for the user are not allowed because the user is not a confirmed/i)) {
            return InvalidPlatformReason.FB_NOT_CONFIRMED_USER;
        }

        if (errorStringified.match(/You cannot access the app till you log in to www.facebook.com and follow the instructions given/i)) {
            return InvalidPlatformReason.FB_LOGIN_AND_FOLLOW_INSTRUCTIONS;
        }

        if (errorStringified.match(/does not exist, cannot be loaded due to missing permissions, or does not support this operation/i)) {
            return InvalidPlatformReason.FB_INVALID_SOCIAL_ID_OR_MISSING_PERMISSIONS;
        }

        if (errorStringified.match(/Permissions error/i)) {
            return FbErrorCodeOrMessage.FACEBOOK_FIELD_MISSING_PERMISSIONS;
        }

        if (errorStringified.match(/Inconsistent Coordinates/i)) {
            return FbErrorCodeOrMessage.FACEBOOK_INCONSISTENT_COORDINATES;
        }

        logger.warn('[FACEBOOK PUBLISH] Unknown Platform Reason', { error });
        return InvalidPlatformReason.UNKNOWN;
    }

    private _shouldAlert(errors: NonNullable<PublishOnPlatformResponse['errors']>): boolean {
        const acceptableErrors: string[] = [
            InvalidPlatformReason.FB_PASSWORD_CHANGED,
            InvalidPlatformReason.FB_DISCONNECTED,
            InvalidPlatformReason.FB_USER_NEED_TO_BE_ADMIN_OR_MODERATOR_OR_EDITOR,
            InvalidPlatformReason.FB_USER_HAS_NOT_AUTHORIZED_APP,
            InvalidPlatformReason.FB_NOT_CONFIRMED_USER,
            InvalidPlatformReason.FB_LOGIN_AND_FOLLOW_INSTRUCTIONS,
        ];

        return errors.some(({ reason }) => !acceptableErrors.includes(reason));
    }

    private async _checkMissingCategoryMapping({
        restaurant,
        dataToPublish,
    }: {
        restaurant: Partial<RestaurantPopulatedToPublish> & { _id: DbId };
        dataToPublish: Partial<FacebookPageInfo>;
    }): Promise<void> {
        const restaurantCategories = uniq(
            [restaurant.category?.categoryId, ...(restaurant.categoryList ?? []).map((c) => c.categoryId)].filter(isNotNil)
        );

        const isMappingMissing = restaurantCategories.length > 0 && dataToPublish?.category_list?.length === 0;
        if (!isMappingMissing) {
            return;
        }

        const context = await this._slackService.createContextForSlack({ restaurantId: restaurant._id.toString() });
        const slackMessage = `:face_with_monocle: *Missing GMB/FB mapping*\nCategories: ${restaurantCategories.join(', ')}${context}`;

        this._slackService.sendMessage({
            text: slackMessage,
            channel: SlackChannel.PLATFORM_UPDATES_ALERTS,
        });
    }
}
