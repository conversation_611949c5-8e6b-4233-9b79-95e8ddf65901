import { inject, registry, singleton } from 'tsyringe';

import { NotificationChannel, NOTIFICATIONS_ROLE, NotificationType } from '@malou-io/package-utils';

import { InjectionToken } from ':helpers/injection';
import { RoiActivatedNotification } from ':modules/notifications/entities/roi-activated-notification.entity';
import { NotificationsUserRestaurantsRepository } from ':modules/notifications/repositories/notifications-user-restaurants/notifications-user-restaurants.repository';
import NotificationsRepository from ':modules/notifications/repositories/notifications.repository';
import { NotificationSenderService } from ':modules/notifications/services/notifications-sender-service/notification-sender.service.interface';
import { SendNotificationsToChannelsService } from ':modules/notifications/services/notifications-sender-service/send-notifications-to-channels.service';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { RoiSettingsRepository } from ':modules/roi-settings/roi-settings.repository';

@singleton()
@registry([
    {
        token: InjectionToken.NotificationSenderService,
        useClass: SendNotificationsToChannelsService,
    },
])
export class CreateRoiActivatedNotificationUseCase {
    constructor(
        private readonly _userRestaurantsRepository: NotificationsUserRestaurantsRepository,
        private readonly _notificationsRepository: NotificationsRepository,
        @inject(InjectionToken.NotificationSenderService)
        private readonly _sendNotificationsToChannelsService: NotificationSenderService,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _roiSettingsRepository: RoiSettingsRepository
    ) {}

    async execute(restaurantsIds: string[], userTestId?: string): Promise<void> {
        let userRestaurants = await this._userRestaurantsRepository.getRestaurantsGroupedByUserIds(
            restaurantsIds,
            NOTIFICATIONS_ROLE[NotificationType.ROI_ACTIVATED],
            userTestId
        );

        if (userTestId) {
            userRestaurants = userRestaurants.filter((userRestaurant) => userRestaurant.userId === userTestId);
        }

        const notifications: Array<RoiActivatedNotification[]> = [];

        for (const userRestaurant of userRestaurants) {
            const { user, restaurantIds, userId } = userRestaurant;

            const isAtLeastOneRestaurantWithoutRoiSettings =
                await this._roiSettingsRepository.isAtLeastOneRestaurantWithoutRoiSettings(restaurantIds);

            notifications.push(
                user.getReceivableNotificationChannels(NotificationType.ROI_ACTIVATED).map(
                    (channel: NotificationChannel) =>
                        new RoiActivatedNotification({
                            channel,
                            type: NotificationType.ROI_ACTIVATED,
                            data: {
                                restaurantIds,
                                isAtLeastOneRestaurantWithoutRoiSettings,
                            },
                            userId,
                            user,
                        })
                )
            );
        }
        await this._notificationsRepository.createMany({
            data: notifications.flat().map((n) => this._notificationsRepository.toDocument(n)),
        });

        const notificationsToSend: RoiActivatedNotification[] = [];
        for (const notification of notifications.flat()) {
            const restaurantNames = await this._restaurantsRepository.getRestaurantNamesByIds(notification.data.restaurantIds);
            notificationsToSend.push(new RoiActivatedNotification({ ...notification, data: { ...notification.data, restaurantNames } }));
        }

        await this._sendNotificationsToChannelsService.sendNotificationsToChannels(notificationsToSend);
    }
}
