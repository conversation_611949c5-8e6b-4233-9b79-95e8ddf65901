import { groupBy } from 'lodash';
import { DateTime } from 'luxon';
import { inject, registry, singleton } from 'tsyringe';

import { allCountries, NotificationChannel, NOTIFICATIONS_ROLE, NotificationType } from '@malou-io/package-utils';

import { InjectionToken } from ':helpers/injection';
import { CalendarEvent } from ':modules/calendar-events/entities/calendar-event.entity';
import { PostSuggestionNotification } from ':modules/notifications/entities/post-suggestion-notification.entity';
import { NotificationsUserRestaurantsRepository } from ':modules/notifications/repositories/notifications-user-restaurants/notifications-user-restaurants.repository';
import NotificationsRepository from ':modules/notifications/repositories/notifications.repository';
import { NotificationSenderService } from ':modules/notifications/services/notifications-sender-service/notification-sender.service.interface';
import { SendNotificationsToChannelsService } from ':modules/notifications/services/notifications-sender-service/send-notifications-to-channels.service';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { isFeatureAvailableForUsers } from ':services/experimentations-service/experimentation.service';

@singleton()
@registry([
    {
        token: InjectionToken.NotificationSenderService,
        useClass: SendNotificationsToChannelsService,
    },
])
export class CreatePostsSuggestionNotificationUseCase {
    private readonly _PERIOD_FOR_POSTS_SUGGESTION = 14;
    constructor(
        private readonly _userRestaurantsRepository: NotificationsUserRestaurantsRepository,
        private readonly _notificationsRepository: NotificationsRepository,
        @inject(InjectionToken.NotificationSenderService)
        private readonly _sendNotificationsToChannelsService: NotificationSenderService,
        private readonly _restaurantsRepository: RestaurantsRepository
    ) {}

    async execute({
        calendarEvents,
        testUserId,
        channel,
    }: {
        calendarEvents: CalendarEvent[];
        testUserId?: string;
        channel: NotificationChannel;
    }): Promise<void> {
        if (!calendarEvents.length) return;
        let activeUserRestaurants = await this._userRestaurantsRepository.getActiveUserRestaurantsWithRolesAndNoPostsSince({
            roles: NOTIFICATIONS_ROLE[NotificationType.POST_SUGGESTION],
            startDate: DateTime.now().startOf('day').minus({ days: this._PERIOD_FOR_POSTS_SUGGESTION }).toJSDate(),
            testUserId,
        });

        if (!activeUserRestaurants.length) return;

        const groupedUserRestaurants = groupBy(activeUserRestaurants, 'userId');

        const featureEnabledForUsers = await isFeatureAvailableForUsers({
            userIds: Object.keys(groupedUserRestaurants),
            featureName: 'release-post-suggestion-notifications',
        });

        activeUserRestaurants = activeUserRestaurants.filter(
            (ur) => featureEnabledForUsers.includes(ur.userId) || (testUserId && testUserId !== ur.userId)
        );

        const notifications: PostSuggestionNotification[] = [];
        for (const event of calendarEvents) {
            const allUserRestaurants = activeUserRestaurants.filter(
                (userRestaurant) =>
                    // This is for default events, if the restaurant has the event, we should send the notification
                    ((userRestaurant.restaurant?.calendarEventsCountry === event.country || event.country === allCountries) &&
                        event.byDefault) ||
                    // This is for custom events created by users, if it is not by default, we should check if the restaurant has this event so it is not sent to other users
                    (event.byDefault === false && userRestaurant.restaurant?.calendarEventIds.includes(event.id))
            );
            const userRestaurantsByUserId = groupBy(allUserRestaurants, 'userId');

            // check if feature is available for user

            notifications.push(
                ...Object.keys(userRestaurantsByUserId)
                    .map((userId) => {
                        const userRestaurants = userRestaurantsByUserId[userId];
                        const user = userRestaurants[0].user;
                        const restaurants = userRestaurants.map((ur) => ur.restaurant);

                        if (!user.hasActivatedNotificationForChannel(NotificationType.POST_SUGGESTION, channel)) {
                            return null;
                        }

                        return new PostSuggestionNotification({
                            channel,
                            type: NotificationType.POST_SUGGESTION,
                            data: {
                                restaurantIds: restaurants.map((r) => r!.id),
                                event,
                            },
                            userId: user.id,
                            user,
                        });
                    })
                    .filter((notification) => notification !== null)
            );
        }

        await this._notificationsRepository.createMany({
            data: notifications.flat().map((n) => this._notificationsRepository.toDocument(n)),
        });

        const notificationsToSend: PostSuggestionNotification[] = [];
        for (const notification of notifications.flat()) {
            const restaurantNames = await this._restaurantsRepository.getRestaurantNamesByIds(notification.data.restaurantIds);
            notificationsToSend.push(new PostSuggestionNotification({ ...notification, data: { ...notification.data, restaurantNames } }));
        }

        await this._sendNotificationsToChannelsService.sendNotificationsToChannels(notificationsToSend);
    }
}
