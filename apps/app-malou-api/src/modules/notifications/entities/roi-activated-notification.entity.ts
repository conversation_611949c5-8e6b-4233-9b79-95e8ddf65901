import { EntityConstructor } from '@malou-io/package-utils';

import { NotificationUserProps } from ':modules/notifications/entities/child-entities/notification-user.entity';

import { Notification } from './notification.entity';

export interface RoiActivatedNotificationData {
    restaurantIds: string[];
    restaurantNames?: string[];
    isAtLeastOneRestaurantWithoutRoiSettings: boolean;
}

type RoiActivatedNotificationProps = EntityConstructor<RoiActivatedNotification, { user: NotificationUserProps }>;

export class RoiActivatedNotification extends Notification<RoiActivatedNotificationData> {
    constructor(props: RoiActivatedNotificationProps) {
        super(props);
    }
}
