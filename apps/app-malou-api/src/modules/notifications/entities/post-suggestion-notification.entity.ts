import { EntityConstructor } from '@malou-io/package-utils';

import { CalendarEvent } from ':modules/calendar-events/entities/calendar-event.entity';
import { NotificationUserProps } from ':modules/notifications/entities/child-entities/notification-user.entity';

import { Notification } from './notification.entity';

export interface PostSuggestionNotificationData {
    restaurantIds: string[];
    event: CalendarEvent;
    restaurantNames?: string[];
}

type PostSuggestionNotificationProps = EntityConstructor<PostSuggestionNotification, { user: NotificationUserProps }>;

export class PostSuggestionNotification extends Notification<PostSuggestionNotificationData> {
    constructor(props: PostSuggestionNotificationProps) {
        super(props);
    }
}
