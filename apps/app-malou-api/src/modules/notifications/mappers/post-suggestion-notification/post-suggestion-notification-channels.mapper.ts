import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { NotificationDTO } from '@malou-io/package-dto';
import { PostEventReminderNotificationTemplate } from '@malou-io/package-emails';
import { HeapEventName, Locale, NotificationType } from '@malou-io/package-utils';

import { Config } from ':config';
import { PostSuggestionNotification } from ':modules/notifications/entities/post-suggestion-notification.entity';
import { Translation } from ':services/translation.service';

import { EmailNotificationPayload, NotificationMapper } from '../notification.mapper.interface';

interface PostSuggestionEmailNotificationPayload extends EmailNotificationPayload {
    notification: PostSuggestionNotification;
}

@singleton()
export class PostSuggestionNotificationChannelsMapper
    implements NotificationMapper<PostSuggestionNotification, PostSuggestionEmailNotificationPayload, NotificationDTO, any>
{
    constructor(private readonly _translator: Translation) {}

    async mapToEmail(notification: PostSuggestionNotification, apiKey: string): Promise<PostSuggestionEmailNotificationPayload> {
        const link = this._getEmailNotificationLink(notification.data.restaurantIds[0], notification.id);
        const unsubscribeLink = this._getUnsubscribedLink(notification.user.id);
        const trackingUrl = this._buildTrackingUrl({ apiKey, notificationId: notification.id, userEmail: notification.user.email });
        const emailSubject = this._getEmailSubject(notification);
        const event = notification.data.event;
        assert(event.name.fr, '[PostSuggestionNotificationChannelsMapper] Event name is required');
        const postEventReminderNotificationReactTemplate = PostEventReminderNotificationTemplate({
            link,
            unsubscribeLink,
            trackingUrl,
            locale: (notification.user.defaultLanguage as Locale) ?? Locale.FR,
            receiver: notification.user.name,
            restaurantNames: notification.data.restaurantNames ?? [],
            eventEmoji: event.emoji,
            eventName: event.name[(notification.user.defaultLanguage as Locale) ?? Locale.FR] ?? event.name.fr,
        });

        return {
            notification,
            emailSubject,
            templateFunc: () => postEventReminderNotificationReactTemplate,
            heapEventName: HeapEventName.NOTIFICATION_POST_SUGGESTION_TRACKING_EMAIL_SENT,
        };
    }

    async mapToWeb(notification: PostSuggestionNotification): Promise<NotificationDTO> {
        return notification.toDTO();
    }

    async mapToPushNotification(_notification: PostSuggestionNotification): Promise<any> {
        throw new Error('Method not implemented.');
    }

    private _getEmailSubject(notification: PostSuggestionNotification): string {
        const emailLanguage = (notification.user.defaultLanguage as Locale) ?? Locale.FR;
        const eventName = emailLanguage === Locale.FR ? '' : notification.data.event.name[emailLanguage];
        return this._translator.fromLang({ lang: emailLanguage }).notifications.post_suggestion.subject({
            eventName: eventName ?? '',
        });
    }

    private _buildTrackingUrl({
        apiKey,
        notificationId,
        userEmail,
    }: {
        apiKey: string;
        notificationId: string;
        userEmail: string;
    }): string {
        const urlWithoutQuery = `${Config.baseApiUrl}/notifications/emails/opened`;
        const today = new Date().getTime();
        // eslint-disable-next-line max-len
        return `${urlWithoutQuery}?nid=${notificationId}&receiverEmail=${userEmail}&t=${today}&api_key=${apiKey}&type=${NotificationType.POST_SUGGESTION}`;
    }

    private _getEmailNotificationLink(restaurantId: string, notificationId: string): string {
        // eslint-disable-next-line max-len
        return `${Config.baseAppUrl}/restaurants/${restaurantId}/social/socialposts?type=${NotificationType.POST_SUGGESTION}&nid=${notificationId}&nchannel=email`;
    }

    private _getUnsubscribedLink(userId: string): string {
        return `${Config.baseAppUrl}/users/${userId}/notifications-settings`;
    }
}
