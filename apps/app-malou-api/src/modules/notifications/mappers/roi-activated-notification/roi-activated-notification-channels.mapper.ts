import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { NotificationDTO } from '@malou-io/package-dto';
import { RoiActivatedNotificationTemplate } from '@malou-io/package-emails';
import { HeapEventName, Locale, NotificationType } from '@malou-io/package-utils';

import { Config } from ':config';
import { RoiActivatedNotification } from ':modules/notifications/entities/roi-activated-notification.entity';
import { Translation } from ':services/translation.service';

import { EmailNotificationPayload, NotificationMapper } from '../notification.mapper.interface';

interface RoiActivatedEmailNotificationPayload extends EmailNotificationPayload {
    notification: RoiActivatedNotification;
}

@singleton()
export class RoiActivatedNotificationChannelsMapper
    implements NotificationMapper<RoiActivatedNotification, RoiActivatedEmailNotificationPayload, NotificationDTO, any>
{
    constructor(private readonly _translator: Translation) {}

    async mapToEmail(notification: RoiActivatedNotification, apiKey: string): Promise<RoiActivatedEmailNotificationPayload> {
        const link = this._getEmailNotificationLink(notification.data.restaurantIds, notification.id);
        const trackingUrl = this._buildTrackingUrl({ apiKey, notificationId: notification.id, userEmail: notification.user.email });
        const emailSubject = this._getEmailSubject(notification);
        const restaurantNames = notification.data.restaurantNames;
        assert(restaurantNames, '[RoiActivatedNotificationChannelsMapper] Restaurant names are required');
        const roiActivatedReminderReactTemplate = RoiActivatedNotificationTemplate({
            link,
            locale: (notification.user.defaultLanguage as Locale) ?? Locale.FR,
            trackingUrl,
            receiver: `${notification.user.name} ${notification.user.lastname}`,
            restaurantNames,
            isAtLeastOneRestaurantWithoutRoiSettings: notification.data.isAtLeastOneRestaurantWithoutRoiSettings,
        });

        return {
            notification,
            emailSubject,
            templateFunc: () => roiActivatedReminderReactTemplate,
            heapEventName: HeapEventName.NOTIFICATION_ROI_ACTIVATED_TRACKING_EMAIL_SENT,
        };
    }

    async mapToWeb(notification: RoiActivatedNotification): Promise<NotificationDTO> {
        return notification.toDTO();
    }

    async mapToPushNotification(_notification: RoiActivatedNotification): Promise<any> {
        throw new Error('Method not implemented.');
    }

    private _getEmailSubject(notification: RoiActivatedNotification): string {
        const emailLanguage = (notification.user.defaultLanguage as Locale) ?? Locale.FR;
        return this._translator.fromLang({ lang: emailLanguage }).notifications.roi.activated_subject();
    }

    private _buildTrackingUrl({
        apiKey,
        notificationId,
        userEmail,
    }: {
        apiKey: string;
        notificationId: string;
        userEmail: string;
    }): string {
        const urlWithoutQuery = `${Config.baseApiUrl}/notifications/emails/opened`;
        const today = new Date().getTime();
        // eslint-disable-next-line max-len
        return `${urlWithoutQuery}?nid=${notificationId}&receiverEmail=${userEmail}&t=${today}&api_key=${apiKey}&type=${NotificationType.ROI_ACTIVATED}`;
    }

    private _getEmailNotificationLink(restaurantIds: string[], notificationId: string): string {
        return restaurantIds.length > 1
            ? // eslint-disable-next-line max-len
              `${Config.baseAppUrl}/groups/statistics/roi?nchannel=email&type=${NotificationType.ROI_ACTIVATED}&nid=${notificationId}`
            : // eslint-disable-next-line max-len
              `${Config.baseAppUrl}/restaurants/${restaurantIds[0]}/statistics/roi?nchannel=email&type=${NotificationType.ROI_ACTIVATED}&nid=${notificationId}`;
    }
}
