import { groupBy } from 'lodash';
import { DateTime } from 'luxon';
import { singleton } from 'tsyringe';

import { GetScanInsightsBodyDto, ScanForAggregatedInsightsDto } from '@malou-io/package-dto';
import { IScanForAggregatedInsights, toDbIds } from '@malou-io/package-models';
import { getDateRangeFromMalouComparisonPeriod, isNotNil, PlatformKey } from '@malou-io/package-utils';

import { PrivateReviewsRepository } from ':modules/private-reviews/private-reviews.repository';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import ScansRepository from ':modules/scans/repository/scans.repository';
import { ScansDtoMapper } from ':modules/scans/scans.dto-mapper';
import { metricsService } from ':services/metrics.service';

const durationHistogram = metricsService.getMeter().createHistogram('scans.getScanInsightsForRestaurantsUseCase.fastvariant.duration', {
    description: 'The duration of GetScanInsightsForRestaurantsUseCase',
    advice: { explicitBucketBoundaries: [0.1, 0.2, 0.5, 1, 2, 4, 8, 16] },
    unit: 'seconds',
});

const restaurantCountHistogram = metricsService.getMeter().createHistogram('scans.getScanInsightsForRestaurantsUseCase.restaurantCount', {
    description: 'How many restaurants GetScanInsightsForRestaurantsUseCase is called with',
    advice: { explicitBucketBoundaries: [2, 4, 8, 16, 32, 64, 128, 256, 512, 1024, 2048, 4096] },
    unit: 'number of restaurants',
});

const reviewCountHistogram = metricsService.getMeter().createHistogram('scans.getScanInsightsForRestaurantsUseCase.reviewCount', {
    description: 'How many reviews GetScanInsightsForRestaurantsUseCase retrieves',
    advice: { explicitBucketBoundaries: [2, 4, 8, 16, 32, 64, 128, 256, 512, 1024, 2048, 4096, 8192, 16384, 32768] },
    unit: 'number of reviews',
});

@singleton()
export class GetScanInsightsForRestaurantsUseCase {
    constructor(
        private readonly _scansRepository: ScansRepository,
        private readonly _scansDtoMapper: ScansDtoMapper,
        private readonly _reviewsRepository: ReviewsRepository,
        private readonly _privateReviewsRepository: PrivateReviewsRepository
    ) {}

    async execute(params: GetScanInsightsBodyDto): Promise<ScanForAggregatedInsightsDto> {
        const perfMetricsBegin = new Date();
        restaurantCountHistogram.record(params.restaurantIds.length);

        const { previousPeriod, currentPeriod } = this._getInsightsPeriod({
            startScannedAt: params.startScannedAt,
            endScannedAt: params.endScannedAt,
            comparisonPeriod: params.comparisonPeriod,
        });

        const promises = [
            this._scansRepository.getScansForAggregatedInsights({
                ...params,
                startScannedAt: currentPeriod.startScannedAt,
                endScannedAt: currentPeriod.endScannedAt,
            }),
            this._scansRepository.getScansForAggregatedInsights({
                ...params,
                startScannedAt: previousPeriod.startScannedAt,
                endScannedAt: previousPeriod.endScannedAt,
            }),
        ];

        const result = await Promise.all(promises);

        const currentScans = result[0]?.map((s) => this._scansDtoMapper.toScanForAggregatedInsightsDto(s)) ?? [];
        const previousScans = result[1]?.map((s) => this._scansDtoMapper.toScanForAggregatedInsightsDto(s)) ?? [];

        const wofScans = currentScans.filter((s) => s.nfcSnapshot.isRedirectingToWof);
        const previousWofScans = previousScans.filter((s) => s.nfcSnapshot.isRedirectingToWof);

        const estimatedReviewCountPerRestaurant = this._getReviewCountPerRestaurant({
            currentScans: wofScans,
            previousScans: previousWofScans,
            restaurantIds: params.restaurantIds,
        });
        const wofScansCount = wofScans.length;
        const previousWofScansCount = previousWofScans.length;

        const totemReviewsPerRestaurant = await this._getTotemReviewPerRestaurant({
            currentScans,
            previousScans,
            restaurantIds: params.restaurantIds,
            currentPeriod: {
                startDate: new Date(currentPeriod.startScannedAt),
                endDate: new Date(currentPeriod.endScannedAt),
            },
            previousPeriod: {
                startDate: new Date(previousPeriod.startScannedAt),
                endDate: new Date(previousPeriod.endScannedAt),
            },
        });

        const scansEvolution = this._getEvolution(currentScans.length, previousScans.length);
        const wofScansEvolution = this._getEvolution(wofScansCount, previousWofScansCount);
        const scans = currentScans.map(({ matchedReviewSocialId: _matchedReviewSocialId, ...rest }) => rest);

        durationHistogram.record((+new Date() - +perfMetricsBegin) / 1000);

        return {
            scansEvolution,
            wofScansEvolution,
            scans,
            estimatedReviewCountPerRestaurant,
            totemReviewsPerRestaurant,
        };
    }

    private _getInsightsPeriod({
        startScannedAt,
        endScannedAt,
        comparisonPeriod,
    }: Pick<GetScanInsightsBodyDto, 'startScannedAt' | 'endScannedAt' | 'comparisonPeriod'>): {
        previousPeriod: Pick<GetScanInsightsBodyDto, 'startScannedAt' | 'endScannedAt'>;
        currentPeriod: Pick<GetScanInsightsBodyDto, 'startScannedAt' | 'endScannedAt'>;
    } {
        const startDate = new Date(startScannedAt);
        const endDate = new Date(endScannedAt);
        const difference = DateTime.fromJSDate(endDate).diff(DateTime.fromJSDate(startDate)).toObject();
        const previousStartDate = DateTime.fromJSDate(startDate).minus(difference).toJSDate();

        const previousDates = getDateRangeFromMalouComparisonPeriod({
            dateFilters: { startDate, endDate },
            comparisonPeriod,
        });
        let previousPeriod = {
            startScannedAt: previousStartDate.toISOString(),
            endScannedAt: startDate.toISOString(),
        };
        if (previousDates.startDate && previousDates.endDate) {
            previousPeriod = {
                startScannedAt: previousDates.startDate.toISOString(),
                endScannedAt: previousDates.endDate.toISOString(),
            };
        }

        return {
            previousPeriod,
            currentPeriod: {
                startScannedAt,
                endScannedAt,
            },
        };
    }

    private _getEvolution(currentPeriodCount: number, previousPeriodCount: number): number {
        return currentPeriodCount - previousPeriodCount;
    }

    private _getReviewCountPerRestaurant({
        currentScans,
        previousScans,
        restaurantIds,
    }: {
        currentScans: ScanForAggregatedInsightsDto['scans'][0][];
        previousScans: ScanForAggregatedInsightsDto['scans'][0][];
        restaurantIds: string[];
    }): ScanForAggregatedInsightsDto['estimatedReviewCountPerRestaurant'] {
        const scansAndPreviousScansByRestaurantId = this._getScansAndPreviousScansByRestaurant({
            currentScans,
            previousScans,
            restaurantIds,
        });
        const result: ScanForAggregatedInsightsDto['estimatedReviewCountPerRestaurant'] = {};
        Object.entries(scansAndPreviousScansByRestaurantId).forEach(
            ([restaurantId, { scans: scansByRestaurant, previousScans: previousScansByRestaurant }]) => {
                const estimatedReviewCount = this._getEstimatedReviewCount(scansByRestaurant);
                const previousEstimatedReviewCount = this._getEstimatedReviewCount(previousScansByRestaurant);
                const estimatedReviewCountDifferenceWithPreviousPeriod = estimatedReviewCount - previousEstimatedReviewCount;
                result[restaurantId] = {
                    estimatedReviewCount,
                    estimatedReviewCountDifferenceWithPreviousPeriod,
                };
            }
        );
        return result;
    }

    private async _getTotemReviewPerRestaurant({
        currentScans,
        previousScans,
        restaurantIds,
        currentPeriod,
        previousPeriod,
    }: {
        currentScans: (ScanForAggregatedInsightsDto['scans'][0] & { matchedReviewSocialId?: string | null })[];
        previousScans: (ScanForAggregatedInsightsDto['scans'][0] & { matchedReviewSocialId?: string | null })[];
        restaurantIds: string[];
        currentPeriod: { startDate: Date; endDate: Date };
        previousPeriod: { startDate: Date; endDate: Date };
    }): Promise<ScanForAggregatedInsightsDto['totemReviewsPerRestaurant']> {
        const currentScansFromTotem = currentScans.filter((scan) => !scan.nfcSnapshot.isRedirectingToWof);
        const previousScansFromTotem = previousScans.filter((scan) => !scan.nfcSnapshot.isRedirectingToWof);

        const allCurrentReviews = currentScansFromTotem.map((scan) => scan.matchedReview).filter(isNotNil);
        const allPreviousReviews = previousScansFromTotem.map((scan) => scan.matchedReview).filter(isNotNil);

        reviewCountHistogram.record(allCurrentReviews.length + allPreviousReviews.length);

        const currentReviews = allCurrentReviews.filter((review) => review.key !== PlatformKey.PRIVATE);
        const previousReviews = allPreviousReviews.filter((review) => review.key !== PlatformKey.PRIVATE);

        // We only keep private reviews with rating 1, 2, 3 or 4. Others are considered bad data.
        const [currentPrivateReviews, previousPrivateReviews] = await Promise.all([
            this._privateReviewsRepository.find({
                filter: {
                    restaurantId: {
                        $in: toDbIds(restaurantIds),
                    },
                    socialCreatedAt: { $gte: currentPeriod.startDate, $lte: currentPeriod.endDate },
                    rating: { $in: [1, 2, 3, 4] },
                },
                projection: { rating: 1, restaurantId: 1 },
                options: { lean: true },
            }),
            this._privateReviewsRepository.find({
                filter: {
                    restaurantId: {
                        $in: toDbIds(restaurantIds),
                    },
                    socialCreatedAt: { $gte: previousPeriod.startDate, $lte: previousPeriod.endDate },
                    rating: { $in: [1, 2, 3, 4] },
                },
                projection: { rating: 1, restaurantId: 1 },
                options: { lean: true },
            }),
        ]);

        const result: ScanForAggregatedInsightsDto['totemReviewsPerRestaurant'] = {
            reviewCountDifferenceWithPreviousPeriod: currentReviews.length - previousReviews.length,
            privateReviewCountDifferenceWithPreviousPeriod: currentPrivateReviews.length - previousPrivateReviews.length,
            reviewsPerPlatform: [],
            privateReviewsPerRating: [],
        };

        const reviewsPerPlatform: ScanForAggregatedInsightsDto['totemReviewsPerRestaurant']['reviewsPerPlatform'] = [];
        const privateReviewsPerRating: ScanForAggregatedInsightsDto['totemReviewsPerRestaurant']['privateReviewsPerRating'] = [];

        // Group reviews by platform key
        const reviewsByPlatform = groupBy(currentReviews, 'key');

        // Get all unique platform keys from reviews
        const platformKeys = Object.keys(reviewsByPlatform) as PlatformKey[];

        // Create reviewsPerPlatform array
        // For each platform, we need an entry with counts per restaurant
        platformKeys.forEach((platformKey) => {
            const platformReviews = reviewsByPlatform[platformKey] ?? [];
            const reviewsCountByRestaurant = restaurantIds.map((restaurantId) => {
                const count = platformReviews.filter((review) => review.restaurantId.toString() === restaurantId.toString()).length;
                return {
                    count,
                    restaurantId,
                };
            });

            reviewsPerPlatform.push({
                key: platformKey,
                reviewsCount: reviewsCountByRestaurant,
            });
        });

        // Group private reviews by rating
        const privateReviewsByRating = groupBy(currentPrivateReviews, 'rating');

        // Get all unique ratings from private reviews
        const ratings = Object.keys(privateReviewsByRating).map(Number);

        // Create privateReviewsPerRating array
        // For each rating, we need an entry with counts per restaurant
        ratings.forEach((rating) => {
            const ratingReviews = privateReviewsByRating[rating] ?? [];
            const reviewsCountByRestaurant = restaurantIds.map((restaurantId) => {
                const count = ratingReviews.filter((review) => review.restaurantId.toString() === restaurantId.toString()).length;
                return {
                    count,
                    restaurantId,
                };
            });

            privateReviewsPerRating.push({
                rating,
                reviewsCount: reviewsCountByRestaurant,
            });
        });

        result.reviewsPerPlatform = reviewsPerPlatform;
        result.privateReviewsPerRating = privateReviewsPerRating;

        return result;
    }

    private _getScansAndPreviousScansByRestaurant({
        currentScans,
        previousScans,
        restaurantIds,
    }: {
        currentScans: ScanForAggregatedInsightsDto['scans'][0][];
        previousScans: ScanForAggregatedInsightsDto['scans'][0][];
        restaurantIds: string[];
    }): Record<string, { scans: IScanForAggregatedInsights[]; previousScans: IScanForAggregatedInsights[] }> {
        const scansByRestaurantId: Record<string, ScanForAggregatedInsightsDto['scans'][0][]> = groupBy(currentScans, (el) =>
            el.nfcSnapshot.restaurantId.toString()
        );
        const previousScansByRestaurantId: Record<string, ScanForAggregatedInsightsDto['scans'][0][]> = groupBy(previousScans, (el) =>
            el.nfcSnapshot.restaurantId.toString()
        );
        return restaurantIds.reduce((acc, restaurantId) => {
            acc[restaurantId] = {
                scans: scansByRestaurantId[restaurantId] || [],
                previousScans: previousScansByRestaurantId[restaurantId] || [],
            };
            return acc;
        }, {});
    }
    private _getEstimatedReviewCount(scans: IScanForAggregatedInsights[]): number {
        return scans.filter((scan) => {
            return !!scan.matchedReview;
        }).length;
    }
}
