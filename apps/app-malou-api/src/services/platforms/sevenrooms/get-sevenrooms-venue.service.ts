import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { toDbId } from '@malou-io/package-models';
import { MalouErrorCode, PlatformKey } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { Platform } from ':modules/platforms/platforms.entity';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { SevenroomsProvider } from ':providers/sevenrooms/sevenrooms.provider';

@singleton()
export class GetSevenroomsVenueService {
    constructor(
        private readonly _sevenroomsProvider: SevenroomsProvider,
        private readonly _platformsRepository: PlatformsRepository
    ) {}

    async execute({
        socialId,
        restaurantId,
        csrfToken,
        sessionId,
    }: {
        socialId: string;
        restaurantId?: string;
        csrfToken: string;
        sessionId: string;
    }): Promise<string | null> {
        const platform = await this._getPlatform(socialId, restaurantId);
        assert(platform, 'Platform not found for the given socialId or restaurantId');
        if (platform.venueId) {
            return platform.venueId;
        }
        try {
            const venueId = await this._sevenroomsProvider.getVenueId({
                socialId,
                csrfToken,
                sessionId,
            });
            const platformId = platform.id;
            assert(platformId, 'Platform ID is required to update the venue ID');
            await this._platformsRepository.updateOne({
                filter: { _id: toDbId(platformId) },
                update: { $set: { venueId } },
            });
            return venueId;
        } catch (error) {
            throw new MalouError(MalouErrorCode.SEVENROOMS_VENUE_ID_NOT_FOUND, {
                message: 'Failed to fetch sevenrooms venue',
                metadata: {
                    socialId,
                    restaurantId,
                    csrfToken,
                    sessionId,
                    error,
                },
            });
        }
    }

    private async _getPlatform(socialId: string, restaurantId?: string): Promise<Platform | null> {
        if (restaurantId) {
            return this._platformsRepository.getPlatformByRestaurantIdAndPlatformKey(restaurantId, PlatformKey.SEVENROOMS);
        }
        const platforms = await this._platformsRepository.getPlatformsBySocialIdAndPlatformKey(socialId, PlatformKey.SEVENROOMS);
        return platforms.length > 0 ? platforms[0] : null;
    }
}
