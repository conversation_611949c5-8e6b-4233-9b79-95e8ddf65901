import axios, { AxiosInstance } from 'axios';
import assert from 'node:assert/strict';
import { randomUUID } from 'node:crypto';
import { singleton } from 'tsyringe';

import { CountryCode, exponentialBackoffDelay, MalouErrorCode, waitFor, YextPublisherId } from '@malou-io/package-utils';

import { Config } from ':config';
import { MalouError } from ':helpers/classes/malou-error';
import { getMalouCountryCode } from ':helpers/utils';
import { YextLocation } from ':modules/publishers/yext/entities/yext-location.entity';
import { ProviderMetricsService } from ':providers/provider.metrics.service';
import { IYextProvider } from ':providers/yext/yext.provider.interface';
import {
    YextCancelServiceRequestBody,
    YextCancelServiceResponseBody,
    YextCreateAddRequestRequestBody,
    YextCreateAddRequestResponseBody,
    YextCreationEntityType,
    YextEntity,
    YextEntityType,
    YextError,
    YextErrorCategory,
    YextErrorDetails,
    YextGetAddRequestResponseBody,
    YextGetListingsResponseBody,
    YextGetServicesResponseBody,
    YextUpdateEntityResponseBody,
} from ':providers/yext/yext.provider.interfaces';

/**
 * Yext Provider.
 *
 * Note that in Yext paradigm :
 *      - account (or customer): an instance that can manage multiple locations
 *      - location (or entity): an instance that represent a restaurant
 *      - service: a group of features that can be tied to a location or account
 *      - location service: service for location
 *      - account service: service for account (we do not use that for now)
 */
@singleton()
export class YextProvider implements IYextProvider {
    private _axiosInstance: AxiosInstance;

    constructor(private readonly _providerMetricsService: ProviderMetricsService) {
        this._axiosInstance = axios.create({
            baseURL: Config.vendors.yext.baseUrl,
            params: {
                api_key: Config.vendors.yext.apiKey,
                v: Config.vendors.yext.version,
            },
        });
    }

    /**
     * An asynchronous request that create a location.
     * We can follow the status of the request by polling an endpoint or via a webhook.
     *
     * The call will:
     *      - create a location
     *      - add defaults service(s) to the location
     *      - link it to the provided account
     *
     * https://hitchhikers.yext.com/docs/managementapis/agreements/addrequests#operation/createNewLocationAddRequest
     */
    async createAddRequestForNewLocation(
        accountId: string,
        accountName: string | undefined,
        entity: YextEntity
    ): Promise<{ partnerLocationId: string; responseBody: YextCreateAddRequestResponseBody }> {
        const partnerLocationId = randomUUID();
        const skus = this._getSkusByCountryCode(getMalouCountryCode(entity.meta.countryCode));
        const body: YextCreateAddRequestRequestBody = {
            newLocationId: partnerLocationId,
            newLocationAccountId: accountId,
            newLocationAccountName: accountName,
            newEntityType: this._getCreationEntityType(entity.locationType),
            newEntityData: entity,
            skus,
        };
        const url = 'accounts/me/newlocationaddrequests';
        try {
            const res = await this._providerMetricsService.callAndTrackExternalAPI({
                hostId: 'yext',
                requestId: 'addrequests.create',
                request: () => this._axiosInstance.post(url, body),
            });
            return { partnerLocationId, responseBody: res.data };
        } catch (error: Error | any) {
            const yextError = error.response?.data?.meta?.errors?.[0];
            throw new MalouError(MalouErrorCode.PLATFORM_SERVICE_ERROR, { metadata: { error: yextError, accountId, body } });
        }
    }

    /**
     * Check add request status.
     * When creating a location, the status can take seconds (or more) to be completed.
     *
     * https://hitchhikers.yext.com/docs/managementapis/agreements/addrequests/#operation/getAddRequest
     */
    async getAddRequestForLocation(addRequestId: string): Promise<YextGetAddRequestResponseBody> {
        const url = `accounts/me/addrequests/${addRequestId}`;
        try {
            const res = await this._providerMetricsService.callAndTrackExternalAPI({
                hostId: 'yext',
                requestId: 'addrequests.get',
                request: () => this._axiosInstance.get<YextGetAddRequestResponseBody>(url),
            });
            return res.data;
        } catch (error: Error | any) {
            const yextError = error.response?.data?.meta?.errors?.[0];
            throw new MalouError(MalouErrorCode.PLATFORM_SERVICE_ERROR, { metadata: { error: yextError, addRequestId } });
        }
    }

    /**
     * Update an entity.
     *
     * https://hitchhikers.yext.com/docs/managementapis/content/entities#operation/updateEntity
     */
    async updateEntity({
        accountId,
        locationId,
        entity,
        retryCount = 0,
    }: {
        accountId: string;
        locationId: string;
        entity: Partial<YextEntity>;
        retryCount?: number;
    }): Promise<YextUpdateEntityResponseBody> {
        const MAX_RETRIES = 3;

        const url = `accounts/${accountId}/entities/${locationId}`;

        try {
            const res = await this._providerMetricsService.callAndTrackExternalAPI({
                hostId: 'yext',
                requestId: 'entities.update',
                request: () => this._axiosInstance.put<YextUpdateEntityResponseBody>(url, entity),
            });
            return res.data;
        } catch (error: Error | any) {
            const yextError: YextError | undefined = error.response?.data?.meta?.errors?.[0];

            if (this._shouldRetry(yextError) && retryCount < MAX_RETRIES) {
                const delay = exponentialBackoffDelay(retryCount);
                await waitFor(delay);
                return await this.updateEntity({ accountId, locationId, entity, retryCount: retryCount + 1 });
            }

            throw new MalouError(MalouErrorCode.PLATFORM_SERVICE_ERROR, {
                metadata: { error: yextError, accountId, locationId, entity },
            });
        }
    }

    /**
     * Get location Services.
     *
     * https://hitchhikers.yext.com/docs/managementapis/agreements/services
     */
    async getLocationServices(location: YextLocation, accountId: string = 'me'): Promise<YextGetServicesResponseBody> {
        const url = `accounts/${accountId}/services?locationId=${location.partnerLocationId}`;
        try {
            const res = await this._providerMetricsService.callAndTrackExternalAPI({
                hostId: 'yext',
                requestId: 'services.get',
                request: () => this._axiosInstance.get<YextGetServicesResponseBody>(url),
            });
            return res.data;
        } catch (error: Error | any) {
            const yextError = error.response?.data?.meta?.errors?.[0];
            throw new MalouError(MalouErrorCode.PLATFORM_SERVICE_ERROR, { metadata: { error: yextError, accountId, location } });
        }
    }

    /**
     * Delete an entity.
     * The entity services need to be canceled before deleting the entity.
     *
     * https://hitchhikers.yext.com/docs/managementapis/content/entities#operation/deleteEntity
     */
    async deleteEntity(accountId: string, locationId: string): Promise<void> {
        const url = `accounts/${accountId}/entities/${locationId}`;
        try {
            await this._providerMetricsService.callAndTrackExternalAPI({
                hostId: 'yext',
                requestId: 'entities.delete',
                request: () => this._axiosInstance.delete<YextCreateAddRequestResponseBody>(url),
            });
        } catch (error: Error | any) {
            const yextError = error.response?.data?.meta?.errors?.[0];
            throw new MalouError(MalouErrorCode.PLATFORM_SERVICE_ERROR, { metadata: { error: yextError, accountId, locationId } });
        }
    }

    /**
     * Remove services for a location.
     * If skus are not provided, all services will be removed.
     *
     * https://hitchhikers.yext.com/docs/managementapis/agreements/services#operation/cancelServices
     */
    async cancelServicesForLocation(
        accountId: string,
        locationId: string,
        skus: string[] | null = null
    ): Promise<YextCancelServiceResponseBody> {
        const url = 'accounts/me/cancelservices';
        const body: YextCancelServiceRequestBody = {
            locationAccountId: accountId,
            locationId,
            ...(skus && { skus }),
        };
        try {
            const res = await this._providerMetricsService.callAndTrackExternalAPI({
                hostId: 'yext',
                requestId: 'services.cancel',
                request: () => this._axiosInstance.post<YextCancelServiceResponseBody>(url, body),
            });
            return res.data;
        } catch (error: Error | any) {
            const yextError = error.response?.data?.meta?.errors?.[0];
            throw new MalouError(MalouErrorCode.PLATFORM_SERVICE_ERROR, { metadata: { error: yextError, accountId, locationId } });
        }
    }

    /**
     * Get all listings for a location.
     *
     * https://hitchhikers.yext.com/docs/managementapis/listings/listingsmanagement#operation/listListings
     */
    async getListingsForLocation(accountId: string, locationIds: string[]): Promise<YextGetListingsResponseBody> {
        const url = `accounts/${accountId}/listings/listings?locationIds=${locationIds}`;
        try {
            const res = await this._providerMetricsService.callAndTrackExternalAPI({
                hostId: 'yext',
                requestId: 'listings.get',
                request: () => this._axiosInstance.get<YextGetListingsResponseBody>(url),
            });
            return res.data;
        } catch (error: Error | any) {
            const yextError = error.response?.data?.meta?.errors?.[0];
            throw new MalouError(MalouErrorCode.PLATFORM_SERVICE_ERROR, { metadata: { error: yextError, accountId, locationIds } });
        }
    }

    async optOutListing(accountId: string, locationIds: string[], publisherIds: YextPublisherId[]): Promise<void> {
        const url = `accounts/${accountId}/listings/listings/optout?locationIds=${locationIds.join(',')}&publisherIds=${publisherIds.join(',')}`;
        try {
            await this._providerMetricsService.callAndTrackExternalAPI({
                hostId: 'yext',
                requestId: 'listings.optOut',
                request: () => this._axiosInstance.get<YextGetListingsResponseBody>(url),
            });
        } catch (error: Error | any) {
            const yextError = error.response?.data?.meta?.errors?.[0];
            throw new MalouError(MalouErrorCode.PLATFORM_SERVICE_ERROR, { metadata: { error: yextError, accountId, locationIds } });
        }
    }

    async optInListing(accountId: string, locationIds: string[], publisherIds: YextPublisherId[]): Promise<void> {
        const url = `accounts/${accountId}/listings/listings/optin?locationIds=${locationIds.join(',')}&publisherIds=${publisherIds.join(',')}`;
        try {
            await this._providerMetricsService.callAndTrackExternalAPI({
                hostId: 'yext',
                requestId: 'listings.optIn',
                request: () => this._axiosInstance.get<YextGetListingsResponseBody>(url),
            });
        } catch (error: Error | any) {
            const yextError = error.response?.data?.meta?.errors?.[0];
            throw new MalouError(MalouErrorCode.PLATFORM_SERVICE_ERROR, { metadata: { error: yextError, accountId, locationIds } });
        }
    }

    private _getCreationEntityType(locationType: YextEntityType | undefined): YextCreationEntityType {
        switch (locationType) {
            case YextEntityType.HOTEL:
                return YextCreationEntityType.HOTEL;
            case YextEntityType.RESTAURANT:
                return YextCreationEntityType.RESTAURANT;
            case YextEntityType.LOCATION:
                return YextCreationEntityType.LOCATION;
            case YextEntityType.ATM:
                return YextCreationEntityType.ATM;
            case YextEntityType.HEALTHCARE_FACILITY:
                return YextCreationEntityType.HEALTHCARE_FACILITY;
            case YextEntityType.HEALTHCARE_PROFESSIONAL:
                return YextCreationEntityType.HEALTHCARE_PROFESSIONAL;
            default:
                return YextCreationEntityType.LOCATION;
        }
    }

    private _getSkusByCountryCode(countryCode: CountryCode | undefined): string[] {
        assert(Config.vendors.yext.skus.usKnowledgeEngineStarter, 'US Knowledge Engine Starter SKU is not defined');
        assert(Config.vendors.yext.skus.knowledgeEngineStarter, 'Knowledge Engine Starter SKU is not defined');

        switch (countryCode) {
            case CountryCode.UNITED_STATES:
                return [Config.vendors.yext.skus.usKnowledgeEngineStarter];
            default:
                return [Config.vendors.yext.skus.knowledgeEngineStarter];
        }
    }

    private _shouldRetry(yextError: YextError | undefined): boolean {
        return (
            this._isYextErrorFromCategory(yextError, YextErrorCategory.SERVICE_UNAVAILABLE) ||
            this._isYextErrorFromCategory(yextError, YextErrorCategory.SERVER_ERROR)
        );
    }

    private _isYextError(error: any): error is YextError {
        return (
            typeof error === 'object' &&
            error !== null &&
            'code' in error &&
            'message' in error &&
            'name' in error &&
            typeof (error as any).code === 'string' &&
            typeof (error as any).message === 'string' &&
            typeof (error as any).name === 'string'
        );
    }

    private _isYextErrorFromCategory(error: any, name: YextErrorCategory): boolean {
        if (!this._isYextError(error)) {
            return false;
        }

        const yextErrorDetails = YextErrorDetails[name];
        if (!yextErrorDetails) {
            return false;
        }

        return (
            error.code === yextErrorDetails.code ||
            error.message?.toLowerCase() === yextErrorDetails.message.toLowerCase() ||
            error.name?.toLowerCase() === yextErrorDetails.name.toLowerCase()
        );
    }
}
