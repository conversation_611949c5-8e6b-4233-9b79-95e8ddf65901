import { container } from 'tsyringe';

import { Config } from ':config';
import { AgendaSingleton } from ':helpers/classes/agenda-singleton';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { exitProcess } from ':helpers/exit-process';
import { logger } from ':helpers/logger';
import { WeeklySquadReachStatsForProductManagersJob } from ':modules/boosters/jobs/weekly-squad-reach-stats-for-product-managers.job';
import { CleanCategoriesJob } from ':modules/categories/jobs/clean-categories';
import { InitializeRestaurantsTheForkClientsJob } from ':modules/clients/provider-clients/providers/thefork/jobs/initialize-restaurants-the-fork-clients.job';
import { UpdatePlatformCommentsJob } from ':modules/comments/jobs/update-platform-comments';
import { ExtendDoorDashCredentialExpirationJob } from ':modules/credentials/jobs/extend-doordash-credential.job';
import { ExtendSevenroomsCredentialExpirationJob } from ':modules/credentials/jobs/extend-sevenrooms-credential-expiration.job';
import { IncrementGiftStocksForExpiredDrawsJob } from ':modules/gifts/jobs/increment-gift-stocks-for-expired-draws';
import { SendGiftExpiresSoonEmailsJob } from ':modules/gifts/jobs/send-gift-expires-soon-email';
import { SendInformationUpdatesJob } from ':modules/information-updates/jobs/send-information-updates';
import { UpdateWeeklyGeoSamplesJob } from ':modules/keywords/jobs/update-weekly-geo-samples';
import { GenerateNormalizedVideoJob } from ':modules/media/jobs/generate-normalized-video.job';
import { FetchMessagesJob } from ':modules/messages/jobs/fetch-messages';
import { DailySendUpcomingHolidaysEmailNotificationsJob } from ':modules/notifications/jobs/daily-send-upcoming-holidays-notifications/daily-send-upcoming-holidays-email-notifications';
import { DailySendUpcomingHolidaysWebNotificationsJob } from ':modules/notifications/jobs/daily-send-upcoming-holidays-notifications/daily-send-upcoming-holidays-web-notifications';
import { DailySendUpcomingPostsSuggestionEmailNotificationsJob } from ':modules/notifications/jobs/daily-send-upcoming-posts-suggestion-notifications/daily-send-upcoming-posts-suggestion-email-notifications';
import { DailySendUpcomingPostsSuggestionWebNotificationsJob } from ':modules/notifications/jobs/daily-send-upcoming-posts-suggestion-notifications/daily-send-upcoming-posts-suggestion-web-notifications';
import { SendUnreadMessagesNotificationsJob } from ':modules/notifications/jobs/send-unread-messages-notifications';
import { WeeklySendNotificationsSummaryJob } from ':modules/notifications/jobs/weekly-notifications-summary/weekly-send-notifications-summary';
import { InsertDailyFollowersJob } from ':modules/platform-insights/jobs/insert-daily-followers';
import { InsertWeeklyPlatformRatingsJob } from ':modules/platform-insights/jobs/insert-weekly-platform-ratings';
import { DailyCheckPlatformConnectionStateJob } from ':modules/platforms/jobs/daily-check-platform-connection-state/daily-check-platform-connection-state.job';
import { SendMapstrReminderEmailJob } from ':modules/platforms/jobs/send-mapstr-reminder-email';
import { FetchAndMatchFbPostTimedOutJob } from ':modules/posts/jobs/fetch-and-match-fb-post';
import { FetchPostAndCheckErrorsJob } from ':modules/posts/jobs/fetch-post-and-check-errors';
import { InitializeStoryJob } from ':modules/posts/jobs/initialize-story';
import { PostCheckWrongErrorAndUpdateJob } from ':modules/posts/jobs/post-check-wrong-error-and-update';
import { PreparePostJob } from ':modules/posts/jobs/prepare-post';
import { PrepareStoryJob } from ':modules/posts/jobs/prepare-story';
import { PublishPostOnPlatformJob } from ':modules/posts/jobs/publish-post-on-platform';
import { PublishStoryJob } from ':modules/posts/jobs/publish-story';
import { PublishStoryOnPlatformJob } from ':modules/posts/jobs/publish-story-on-platform';
import { SendFeedbackNotificationEmailJob } from ':modules/posts/jobs/send-feedback-notification-email';
import { LocationDeletionJob } from ':modules/publishers/yext/jobs/location-deletion';
import { RetryCanceledAddRequestJob } from ':modules/publishers/yext/jobs/retry-canceled-add-requests';
import { UpdateYextAddRequestStatusJob } from ':modules/publishers/yext/jobs/update-yext-add-request-status';
import { ResetRestaurantAiCreditsJob } from ':modules/restaurants/jobs/reset-restaurant-ai-credits';
import { SaveBookmarkedPostJob } from ':modules/restaurants/jobs/save-bookmarked-post';
import { AutoReplyToReviewJob } from ':modules/reviews/jobs/auto-reply-to-review';
import { MatchReviewToScanJob } from ':modules/reviews/jobs/match-review-to-scan.job';
import { RetryReplyToReviewJob } from ':modules/reviews/jobs/retry-reply-to-reviews';
import { StartReviewsAnalysesJob } from ':modules/reviews/jobs/start-reviews-analyses';
import { UpdateAllReviewsJob } from ':modules/reviews/jobs/update-all-reviews';
import { UpdateLatestReviewsJob } from ':modules/reviews/jobs/update-latest-reviews';
import { TopicPruningJob } from ':modules/segment-analysis-parent-topics/jobs/topic-pruning.job';
import { DeployAllStoreLocatorsJob } from ':modules/store-locator/jobs/deploy-all-store-locators';
import { DeployStoreLocatorJob } from ':modules/store-locator/jobs/deploy-store-locator';
import { GenerateStoreLocatorContentJob } from ':modules/store-locator/jobs/generate-store-locator-content';
import { PublishStoreLocatorPagesJob } from ':modules/store-locator/jobs/publish-store-locator';
import { SendWofLiveTomorrowEmailsJob } from ':modules/wheels-of-fortune/jobs/send-wof-live-tomorrow-email';
import { UpdateWofTotemsRedirectionJob } from ':modules/wheels-of-fortune/jobs/update-wof-totems-redirection';
import { NotifyExperimentationFeatureListJob } from ':services/experimentations-service/notify-experimentation-feature-list.job';

async function listenToAgendaJobs() {
    await container.resolve(AutoReplyToReviewJob).init();
    await container.resolve(CleanCategoriesJob).init();
    await container.resolve(FetchAndMatchFbPostTimedOutJob).init();
    await container.resolve(FetchMessagesJob).init();
    await container.resolve(FetchPostAndCheckErrorsJob).init();
    await container.resolve(InsertDailyFollowersJob).init();
    await container.resolve(InsertWeeklyPlatformRatingsJob).init();
    await container.resolve(PostCheckWrongErrorAndUpdateJob).init();
    await container.resolve(PreparePostJob).init();
    await container.resolve(PublishPostOnPlatformJob).init();
    await container.resolve(InitializeStoryJob).init();
    await container.resolve(PrepareStoryJob).init();
    await container.resolve(PublishStoryJob).init();
    await container.resolve(ResetRestaurantAiCreditsJob).init();
    await container.resolve(RetryReplyToReviewJob).init();
    await container.resolve(SaveBookmarkedPostJob).init();
    await container.resolve(SendFeedbackNotificationEmailJob).init();
    await container.resolve(SendGiftExpiresSoonEmailsJob).init();
    await container.resolve(SendInformationUpdatesJob).init();
    await container.resolve(SendUnreadMessagesNotificationsJob).init();
    // TODO: delete this line when feature toggle 'release-new-semantic-analysis' is removed
    await container.resolve(StartReviewsAnalysesJob).init();
    await container.resolve(UpdateAllReviewsJob).init();
    await container.resolve(UpdateLatestReviewsJob).init();
    await container.resolve(UpdatePlatformCommentsJob).init();
    await container.resolve(UpdateWeeklyGeoSamplesJob).init();
    await container.resolve(MatchReviewToScanJob).init();
    await container.resolve(IncrementGiftStocksForExpiredDrawsJob).init();
    await container.resolve(SendWofLiveTomorrowEmailsJob).init();
    await container.resolve(UpdateWofTotemsRedirectionJob).init();
    await container.resolve(UpdateYextAddRequestStatusJob).init();
    await container.resolve(LocationDeletionJob).init();
    await container.resolve(SendMapstrReminderEmailJob).init();
    await container.resolve(DailySendUpcomingPostsSuggestionEmailNotificationsJob).init();
    await container.resolve(DailySendUpcomingHolidaysEmailNotificationsJob).init();
    await container.resolve(DailySendUpcomingPostsSuggestionWebNotificationsJob).init();
    await container.resolve(DailySendUpcomingHolidaysWebNotificationsJob).init();
    await container.resolve(WeeklySendNotificationsSummaryJob).init();
    await container.resolve(DailyCheckPlatformConnectionStateJob).init();
    await container.resolve(WeeklySquadReachStatsForProductManagersJob).init();
    await container.resolve(GenerateNormalizedVideoJob).init();
    await container.resolve(RetryCanceledAddRequestJob).init();
    await container.resolve(DeployAllStoreLocatorsJob).init();
    await container.resolve(DeployStoreLocatorJob).init();
    await container.resolve(GenerateStoreLocatorContentJob).init();
    await container.resolve(PublishStoreLocatorPagesJob).init();
    await container.resolve(ExtendSevenroomsCredentialExpirationJob).init();
    await container.resolve(ExtendDoorDashCredentialExpirationJob).init();
    await container.resolve(TopicPruningJob).init();
    await container.resolve(NotifyExperimentationFeatureListJob).init();
    await container.resolve(PublishStoryOnPlatformJob).init();
    await container.resolve(InitializeRestaurantsTheForkClientsJob).init();

    const agenda = await container.resolve(AgendaSingleton).getInstance();

    await agenda.cancel({ name: AgendaJobName.INCREMENT_GIFT_STOCKS_FOR_EXPIRED_DRAWS });
    await agenda.cancel({ name: AgendaJobName.INSERT_DAILY_FOLLOWERS });
    await agenda.cancel({ name: AgendaJobName.INSERT_WEEKLY_PLATFORM_RATINGS });
    await agenda.cancel({ name: AgendaJobName.MATCH_REVIEW_TO_SCAN });
    await agenda.cancel({ name: AgendaJobName.RESET_RESTAURANTS_AI_CREDITS });

    await agenda.cancel({ name: AgendaJobName.SEND_DAILY_UNREAD_MESSAGES_NOTIFICATIONS });
    await agenda.cancel({ name: AgendaJobName.SEND_GIFT_EXPIRES_SOON_EMAILS });
    await agenda.cancel({ name: AgendaJobName.SEND_INFORMATION_UPDATES });
    await agenda.cancel({ name: AgendaJobName.DEPLOY_ALL_STORE_LOCATORS });
    await agenda.cancel({ name: AgendaJobName.SEND_WHEEL_OF_FORTUNE_LIVE_TOMORROW_EMAIL });
    // TODO: delete this line when feature toggle 'release-new-semantic-analysis' is removed
    await agenda.cancel({ name: AgendaJobName.START_REVIEWS_ANALYSES });
    await agenda.cancel({ name: AgendaJobName.UPDATE_ALL_REVIEWS });
    await agenda.cancel({ name: AgendaJobName.UPDATE_LATEST_REVIEWS });
    await agenda.cancel({ name: AgendaJobName.UPDATE_WEEKLY_GEOSAMPLES });
    await agenda.cancel({ name: AgendaJobName.UPDATE_WHEEL_OF_FORTUNE_TOTEMS_REDIRECTION });
    await agenda.cancel({ name: AgendaJobName.CLEAN_CATEGORIES });
    await agenda.cancel({ name: AgendaJobName.DAILY_SEND_UPCOMING_POSTS_SUGGESTION_EMAIL_NOTIFICATIONS });
    await agenda.cancel({ name: AgendaJobName.DAILY_SEND_UPCOMING_HOLIDAYS_EMAIL_NOTIFICATIONS });
    await agenda.cancel({ name: AgendaJobName.DAILY_SEND_UPCOMING_POSTS_SUGGESTION_WEB_NOTIFICATIONS });
    await agenda.cancel({ name: AgendaJobName.DAILY_SEND_UPCOMING_HOLIDAYS_WEB_NOTIFICATIONS });
    await agenda.cancel({ name: AgendaJobName.WEEKLY_SEND_NOTIFICATIONS_SUMMARY });
    await agenda.cancel({ name: AgendaJobName.DAILY_CHECK_PLATFORM_CONNECTION_STATE });
    await agenda.cancel({ name: AgendaJobName.MONTHLY_RETRY_YEXT_CANCELED_ADD_REQUESTS });
    await agenda.cancel({ name: AgendaJobName.WEEKLY_SQUAD_REACH_STATS_FOR_PRODUCT_MANAGERS });
    await agenda.cancel({ name: AgendaJobName.NOTIFY_EXPERIMENTATION_FEATURE_LIST });
    await agenda.cancel({ name: AgendaJobName.EXTEND_SEVENROOMS_CREDENTIAL_EXPIRATION });
    await agenda.cancel({ name: AgendaJobName.EXTEND_DOORDASH_CREDENTIAL });
    await agenda.cancel({ name: AgendaJobName.TOPIC_PRUNING });
    await agenda.cancel({ name: AgendaJobName.INITIALIZE_RESTAURANTS_THE_FORK_CLIENTS });

    if (_shouldCreateCronJobs()) {
        logger.info('Creating CRON jobs', { env: process.env.NODE_ENV });

        await agenda.every('0 2 * * *', AgendaJobName.INSERT_DAILY_FOLLOWERS, {}, { timezone: 'Europe/Paris' });
        await agenda.every('0 4 2-31 * *', AgendaJobName.UPDATE_LATEST_REVIEWS, {}, { timezone: 'Europe/Paris' }); // Do not run on the first day of the month as it is already handled by the update all reviews job
        await agenda.every(
            String(process.env.UPDATE_REVIEWS_CRON_RULE),
            AgendaJobName.UPDATE_ALL_REVIEWS,
            {},
            { timezone: 'Europe/Paris' }
        ); // fetch all reviews less frequently outside of production
        await agenda.every('0 21 * * 6', AgendaJobName.INSERT_WEEKLY_PLATFORM_RATINGS, {}, { timezone: 'Europe/Paris' }); // saturday 9pm
        // TODO: delete this line when feature toggle 'release-new-semantic-analysis' is removed
        await agenda.every('*/3 * * * *', AgendaJobName.START_REVIEWS_ANALYSES, {}, { timezone: 'Europe/Paris' });
        if (String(process.env.FETCH_WEEKLY_GEOSAMPLES) === 'true') {
            await agenda.every('* 2-5 * * 1', AgendaJobName.UPDATE_WEEKLY_GEOSAMPLES, {}, { timezone: 'Europe/Paris' }); // every minute from 2 to 5 am on Mondays
        }
        await agenda.every('0 16 * * *', AgendaJobName.SEND_DAILY_UNREAD_MESSAGES_NOTIFICATIONS, {}, { timezone: 'Europe/Paris' });

        await agenda.every('0 3 * * *', AgendaJobName.MATCH_REVIEW_TO_SCAN, {}, { timezone: 'Europe/Paris' });
        await agenda.every('0 2 1 * *', AgendaJobName.RESET_RESTAURANTS_AI_CREDITS, {}, { timezone: 'Europe/Paris' });
        await agenda.every('0 8 * * *', AgendaJobName.SEND_GIFT_EXPIRES_SOON_EMAILS, {}, { timezone: 'Europe/Paris' });
        await agenda.every('*/5 * * * *', AgendaJobName.SEND_INFORMATION_UPDATES, {}, { timezone: 'Europe/Paris' });
        await agenda.every('0 * * * *', AgendaJobName.SEND_WHEEL_OF_FORTUNE_LIVE_TOMORROW_EMAIL, {}, { timezone: 'Europe/Paris' });
        await agenda.every('0 1 * * *', AgendaJobName.INCREMENT_GIFT_STOCKS_FOR_EXPIRED_DRAWS, {}, { timezone: 'Europe/Paris' });
        await agenda.every('0 * * * *', AgendaJobName.UPDATE_WHEEL_OF_FORTUNE_TOTEMS_REDIRECTION, {}, { timezone: 'Europe/Paris' });
        await agenda.every('0 0 * * *', AgendaJobName.CLEAN_CATEGORIES, {}, { timezone: 'Europe/Paris' });
        await agenda.every('0 10 * * *', AgendaJobName.DAILY_SEND_UPCOMING_POSTS_SUGGESTION_EMAIL_NOTIFICATIONS, {
            timezone: 'Europe/Paris',
        });
        await agenda.every('0 9 * * *', AgendaJobName.DAILY_SEND_UPCOMING_HOLIDAYS_EMAIL_NOTIFICATIONS, {}, { timezone: 'Europe/Paris' });
        await agenda.every('0 6 * * *', AgendaJobName.DAILY_SEND_UPCOMING_POSTS_SUGGESTION_WEB_NOTIFICATIONS, {
            timezone: 'Europe/Paris',
        });
        await agenda.every('0 6 * * *', AgendaJobName.DAILY_SEND_UPCOMING_HOLIDAYS_WEB_NOTIFICATIONS, {}, { timezone: 'Europe/Paris' });
        await agenda.every('0 9 * * 2', AgendaJobName.WEEKLY_SEND_NOTIFICATIONS_SUMMARY, {}, { timezone: 'Europe/Paris' });
        await agenda.every('0 7 * * *', AgendaJobName.DAILY_CHECK_PLATFORM_CONNECTION_STATE, {}, { timezone: 'Europe/Paris' });
        await agenda.every('30 2 * * 1', AgendaJobName.WEEKLY_SQUAD_REACH_STATS_FOR_PRODUCT_MANAGERS, {}, { timezone: 'Europe/Paris' });
        await agenda.every('0 1 1 * *', AgendaJobName.MONTHLY_RETRY_YEXT_CANCELED_ADD_REQUESTS, {}, { timezone: 'Europe/Paris' });
        await agenda.every('0 3 1 * *', AgendaJobName.NOTIFY_EXPERIMENTATION_FEATURE_LIST, {}, { timezone: 'Europe/Paris' });
        await agenda.every('0 * * * *', AgendaJobName.EXTEND_SEVENROOMS_CREDENTIAL_EXPIRATION, {}, { timezone: 'Europe/Paris' });
        await agenda.every('0 * * * *', AgendaJobName.EXTEND_DOORDASH_CREDENTIAL, {}, { timezone: 'Europe/Paris' });
        await agenda.every('0 0 1 * *', AgendaJobName.TOPIC_PRUNING, {}, { timezone: 'Europe/Paris' });

        // Every 2 days at 6am
        await agenda.every('0 6 */2 * *', AgendaJobName.DEPLOY_ALL_STORE_LOCATORS, {}, { timezone: 'Europe/Paris' });

        await agenda.every('0 5 * * *', AgendaJobName.INITIALIZE_RESTAURANTS_THE_FORK_CLIENTS, {}, { timezone: 'Europe/Paris' });
    }

    await agenda.start();
}

if (process.env.NODE_ENV && !['test', 'local-tests'].includes(process.env.NODE_ENV) && Config.services.agenda.isConsumingJobs) {
    logger.info('Service is consuming jobs', { env: process.env.NODE_ENV });

    listenToAgendaJobs().catch((err) => {
        logger.error('Could not start agenda', err);
        void exitProcess(1);
    });
}

const _shouldCreateCronJobs = () => {
    return process.env.NODE_ENV !== 'production' || process.env.I_AM_A !== 'developer';
};
