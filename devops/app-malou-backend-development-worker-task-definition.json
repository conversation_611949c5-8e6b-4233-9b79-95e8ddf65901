{"containerDefinitions": [{"command": ["node", "dist/src/agenda-jobs/worker"], "dependsOn": [{"condition": "START", "containerName": "open-telemetry-collector"}], "environment": [{"name": "PUSHER_APP_ID", "value": "*******"}, {"name": "PUSHER_KEY", "value": "0acfec10831b8018a77d"}, {"name": "PUSHER_CLUSTER", "value": "eu"}, {"name": "IS_AGENDA_PROCESSOR", "value": "true"}, {"name": "GOOGLE_REVIEWS_SUBSCRIPTION_NAME", "value": "projects/malou-product/subscriptions/gmb_reviews-sub-development"}, {"name": "START_PUBSUB_SUBSCRIPTION", "value": "true"}, {"name": "BN_PROJECT_ID", "value": "malou-product"}, {"name": "BN_CLIENT_EMAIL", "value": "<EMAIL>"}, {"name": "BASE_URL", "value": "https://development.omni.malou.io"}, {"name": "BASE_API_URL", "value": "https://development.api.malou.io/api/v1"}, {"name": "FB_APP_ID", "value": "1377266962404541"}, {"name": "FB_REDIRECT_URI", "value": "https://development.api.malou.io/api/v1/facebook/oauth/callback/"}, {"name": "TIKTOK_CLIENT_ID", "value": "sbaw8qdfivdfhqub47"}, {"name": "TIKTOK_REDIRECT_URI", "value": "https://development.api.malou.io/api/v1/tiktok/oauth/callback"}, {"name": "FOURSQUARE_CLIENT_ID", "value": "************************************************"}, {"name": "FOURSQUARE_REDIRECT_URI", "value": ""}, {"name": "GMB_CLIENT_ID", "value": "712726183109-9e379c6motggo4rk92bil67v0n6obg77.apps.googleusercontent.com"}, {"name": "GMB_REDIRECT_URIS", "value": "https://development.api.malou.io/api/v1/google/oauth2callback"}, {"name": "NODE_ENV", "value": "development"}, {"name": "AWS_KEY", "value": "AKIAXP46YDWPGGPZDDA3"}, {"name": "AWS_BUCKET", "value": "malou-dev"}, {"name": "PLATFORMS_SCRAPPER_URL", "value": "https://8457xgi890.execute-api.eu-west-3.amazonaws.com/development/platforms-scrapper"}, {"name": "FB_API_VERSION", "value": "v20.0"}, {"name": "FIXED_IP_CALLER_URL", "value": "https://l86x12pjii.execute-api.eu-west-3.amazonaws.com/development/crawl/node/us-static"}, {"name": "START_SQS_CONSUMER", "value": "true"}, {"name": "SEND_EMAIL", "value": "true"}, {"name": "SCRAP_PAGES_JAUNES", "value": "false"}, {"name": "FETCH_RANKINGS", "value": "true"}, {"name": "UPDATE_REVIEWS_CRON_RULE", "value": "0 5 1 * *"}, {"name": "FETCH_WEEKLY_GEOSAMPLES", "value": "false"}, {"name": "ELASTICACHE_URI", "value": "ec-redis-dev.hnqdvp.ng.0001.euw3.cache.amazonaws.com"}, {"name": "ELASTICACHE_PORT", "value": "6379"}, {"name": "REVIEWS_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_collect_reviews_development"}, {"name": "PUBLISH_POST_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_publish_post_development"}, {"name": "SCRAPPER_API_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_scrapper_api_development"}, {"name": "REVIEW_BOOSTER_SNS_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_review_booster_sns_development"}, {"name": "PUPPETEER_SERVICE_ARN", "value": "arn:aws:lambda:eu-west-3:515192135070:function:puppeteerServiceDevelopment"}, {"name": "BM_PARTNER_NAME", "value": "<PERSON><PERSON>"}, {"name": "BM_PARTNER_EMAIL_ADDRESS", "value": "<EMAIL>"}, {"name": "BM_BRAND_CONTACT_NAME", "value": "<PERSON><PERSON>"}, {"name": "BM_BRAND_CONTACT_EMAIL_ADDRESS", "value": "<EMAIL>"}, {"name": "BM_BRAND_WEBSITE_URL", "value": "https://malou.io/"}, {"name": "BM_CLIENT_EMAIL", "value": "<EMAIL>"}, {"name": "AWS_MEDIA_CONVERT_ROLE", "value": "arn:aws:iam::515192135070:role/service-role/MediaConvert_Default_Role"}, {"name": "MEDIA_CONVERT_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_convert_media_development"}, {"name": "DAILY_REVIEWS_REPORTS_START_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_start_daily_reports_development"}, {"name": "DAILY_REVIEWS_REPORTS_SEND_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_send_daily_reviews_report_development"}, {"name": "PLATFORMS_SCRAPPER_FUNCTION_NAME", "value": "platformsScrapper"}, {"name": "V3_BASE_URL", "value": "https://v3.omni.malou.io"}, {"name": "FIREBASE_PROJECT_ID", "value": "malou-1555310857472"}, {"name": "FIREBASE_CLIENT_EMAIL", "value": "<EMAIL>"}, {"name": "TRANSLATOR_FUNCTION_NAME", "value": "serverlessTranslator"}, {"name": "CLOUDINARY_CLOUD_NAME", "value": "dtjj53vxe"}, {"name": "CLOUDINARY_API_KEY", "value": "989924112896137"}, {"name": "THUMBNAIL_GENERATOR_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_thumbnail_queue_development"}, {"name": "SLACK_ALERTS_WEBHOOK_URL", "value": "https://hooks.slack.com/triggers/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"}, {"name": "SLACK_APP_ALERTS_WEBHOOK_URL", "value": "*******************************************************************************"}, {"name": "KEYWORDS_SCORE_FUNCTION_NAME", "value": "keywordsScoreDevelopment"}, {"name": "WEEKLY_REVIEWS_REPORTS_START_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_start_weekly_reviews_reports_development"}, {"name": "WEEKLY_REVIEWS_REPORTS_SEND_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_send_weekly_reviews_report_development"}, {"name": "WEEKLY_PERFORMANCE_REPORTS_START_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_start_weekly_performance_reports_development"}, {"name": "WEEKLY_PERFORMANCE_REPORTS_SEND_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_send_weekly_performance_report_development"}, {"name": "MONTHLY_PERFORMANCE_REPORTS_START_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_start_monthly_performance_reports_development"}, {"name": "MONTHLY_PERFORMANCE_REPORTS_SEND_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_send_monthly_performance_report_development"}, {"name": "EXPERIMENTATION_APP_URL", "value": "https://experimentation.malou.io:3300"}, {"name": "EXPERIMENTATION_APP_CLIENT_KEY", "value": "sdk-Z2vFGxgYNB6bYbg6"}, {"name": "REVIEWS_SEMANTIC_ANALYSIS_OVERVIEW_FUNCTION_NAME", "value": "reviewsSemanticAnalysisOverviewDevelopment"}, {"name": "START_AGENDA_CONSUMER", "value": "true"}, {"name": "AI_TEXT_GENERATION_SERVICE_FUNCTION_NAME", "value": "aiTextGenerationDevelopment"}, {"name": "DAILY_SAVE_INSIGHTS_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_daily_save_insights_development"}, {"name": "CREATE_MESSAGE_QUEUES_DAILY_INSIGHTS_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_create_message_queues_daily_insights_development"}, {"name": "YEXT_API_BASE_URL", "value": "https://sbx-api.yextapis.com/v2"}, {"name": "YEXT_API_VERSION", "value": "20240424"}, {"name": "YEXT_KNOWLEDGE_ENGINE_STARTER_SKU", "value": "DEV-00010000"}, {"name": "YEXT_US_KNOWLEDGE_ENGINE_STARTER_SKU", "value": "DEV-00010000"}, {"name": "KEYWORDS_GENERATION_PROCESSING_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_keywords_generation_processing_development"}, {"name": "KEYWORDS_FAILED_GENERATION_PROCESSING_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_keywords_failed_generation_processing_development"}, {"name": "AI_HASHTAG_GEN_SERVICE_FUNCTION_NAME", "value": "serverlessHashtagGeneratorDevelopment"}, {"name": "TEXT_TRANSLATOR_FUNCTION_NAME", "value": "serverlessTextTranslatorDevelopment"}, {"name": "RETRY_EMPTY_TRANSLATIONS_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_retry_empty_translations_development"}, {"name": "SIMILAR_RESTAURANTS_FUNCTION_NAME", "value": "similarRestaurantsIdentificationDevelopment"}, {"name": "RESY_API_KEY", "value": "VbWk7s3L4KiK5fzlO7JD3Q5EYolJI7n5"}, {"name": "CREATE_MESSAGE_QUEUES_MONTHLY_SAVE_ROI_INSIGHTS_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_create_message_queues_monthly_save_roi_insights_development"}, {"name": "MONTHLY_ROI_SAVE_INSIGHTS_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_monthly_save_roi_insights_development"}, {"name": "CREATE_MESSAGES_MONTHLY_UPDATE_SIMILAR_RESTAURANTS_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_create_messages_monthly_update_similar_restaurants_development"}, {"name": "MONTHLY_UPDATE_SIMILAR_RESTAURANTS_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_monthly_update_similar_restaurants_development"}, {"name": "MONTHLY_CHECK_RESTAURANTS_ELIGIBILITY_TO_ACTIVATE_ROI", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_monthly_check_restaurants_eligibility_to_activate_roi_development"}, {"name": "REVIEWS_CATCH_UP_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_reviews_catch_up_development"}, {"name": "OPEN_TELEMETRY_COLLECTOR_HOST", "value": "open-telemetry-collector-service-development"}, {"name": "FETCH_KEYWORDS_VOLUME_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_fetch_keywords_volume_development"}, {"name": "OPEN_TELEMETRY_COLLECTOR_HOST", "value": "localhost"}, {"name": "OTEL_NODE_RESOURCE_DETECTORS", "value": "env,host"}, {"name": "RATINGS_CATCH_UP_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_ratings_catch_up_development"}, {"name": "DAILY_NEGATIVE_REVIEWS_EMAIL_NOTIFICATIONS_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_daily_nagative_reviews_email_notifications_development"}, {"name": "CREATE_NEW_REVIEWS_NOTIFICATION_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_send_new_reviews_notification_development"}, {"name": "CREATE_POST_ERROR_NOTIFICATION_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_create_post_error_notification_development"}, {"name": "CREATE_COMMENTS_NOTIFICATION_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_create_comments_notification_development"}, {"name": "CREATE_MENTIONS_NOTIFICATION_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_create_mentions_notification_development"}, {"name": "CREATE_MESSAGE_NOTIFICATION_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_create_message_notification_development"}, {"name": "SYNCHRONIZE_RECENT_POSTS_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_synchronize_recent_posts_development"}, {"name": "CREATE_PLATFORM_DISCONNECTED_NOTIFICATION_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_create_platform_disconnected_notification_development"}, {"name": "CREATE_INFO_UPDATE_ERROR_NOTIFICATION_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_create_info_update_error_notification_development"}, {"name": "UPDATE_REVIEW_RELEVANT_BRICKS_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_update_review_relevant_bricks_development"}, {"name": "AI_REVIEWS_FUNCTION_NAME", "value": "serverlessAiReviewsDevelopment"}, {"name": "AI_KEYWORDS_BREAKDOWN_SERVICE_FUNCTION_NAME", "value": "serverlessAiKeywordsBreakdownDevelopment"}, {"name": "FETCH_REVIEW_SEMANTIC_ANALYSIS_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_fetch_review_semantic_analysis_development"}, {"name": "FETCH_REVIEW_SEMANTIC_ANALYSIS_FIFO_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_fetch_review_semantic_analysis_development.fifo"}, {"name": "MONTHLY_SAVE_KEYWORD_SEARCH_IMPRESSIONS_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_monthly_save_keyword_search_impressions_development"}, {"name": "CREATE_MESSAGE_MONTHLY_SAVE_SEARCH_KEYWORD_IMPRESSIONS_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_create_message_monthly_save_keyword_search_impressions_development"}, {"name": "SEARCH_KEYWORDS_IMPRESSIONS_FUNCTION_NAME", "value": "searchKeywordsImpressionsDevelopment"}, {"name": "NEW_GMAPS_API_ROLLOUT", "value": "0.5"}, {"name": "AI_SEMANTIC_ANALYSIS_FUNCTION_NAME", "value": "serverlessAiSemanticAnalysisDevelopment"}, {"name": "AWS_MEDIA_CONVERT_VIDEO_PROGRESS_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_aws_media_convert_video_progress_development"}, {"name": "APPLE_BUSINESS_CONNECT_API_URL", "value": "https://businessconnect.apple.com"}, {"name": "APPLE_BUSINESS_CONNECT_COMPANY_ID", "value": "1501513140663465984"}, {"name": "APPLE_BUSINESS_CONNECT_CLIENT_ID", "value": "14d6723e-fed3-b800-1500-88f521ef2000"}, {"name": "PREVIOUS_REVIEWS_ANALYSIS_FIFO_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_previous_reviews_analysis_development.fifo"}, {"name": "AI_STORE_LOCATOR_CONTENT_GENERATION_SERVICE_FUNCTION_NAME", "value": "storeLocatorContentGeneratorDevelopment"}, {"name": "DAILY_REFRESH_POST_INSIGHTS_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_daily_refresh_post_insights_development"}, {"name": "THEFORK_CLIENT_ID", "value": "0mvUPa5Ay8pnFFyCFxS8n86oLjPOtmS0"}, {"name": "REFRESH_STALE_PLATFORM_REVIEWS_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_refresh_stale_platform_reviews_development"}, {"name": "GENERATE_PUBLIC_BUSINESS_ID_FIFO_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_generate_public_business_id_development.fifo"}, {"name": "SYNCHRONIZE_STORIES_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_synchronize_stories_development"}, {"name": "PLATFORM_UPDATE_SUGGESTION_NOTIFICATION_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_platform_update_suggestion_notification_development"}, {"name": "PUPPETEER_SERVICE_FUNCTION_NAME", "value": "puppeteerLambdaDevelopment"}], "image": "515192135070.dkr.ecr.eu-west-3.amazonaws.com/malou/app-malou-api:development", "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/app-malou-backend-worker", "awslogs-region": "eu-west-3", "awslogs-stream-prefix": "ecs"}}, "memoryReservation": 512, "name": "app-malou-api-worker", "secrets": [{"name": "PUSHER_SECRET", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app-malou-api/pusher_secret-lwAW00"}, {"name": "MAPSTR_BEARER_TOKEN", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:production/app-malou-api/mapstr_bearer_token-NQsM5h"}, {"name": "BN_PRIVATE_KEY", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app-malou-api/bn_private_key-ddIVxX"}, {"name": "PLATFORMS_SCRAPPER_AUTHORIZATION", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app_malou_api/platforms_scrapper_authorization-zX8Zme"}, {"name": "NODE_CRAWLER_API_KEY", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app_malou_api/node_crawler_api_key-4f48j2"}, {"name": "AWS_SECRET", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app-malou-api/aws_key-FiDiDr"}, {"name": "FB_CLIENT_SECRET", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app-malou-api/fb_client_secret-kJfVam"}, {"name": "TIKTOK_CLIENT_SECRET", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app-malou-api/tiktok_client_secret-IugTAS"}, {"name": "FOURSQUARE_CLIENT_SECRET", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app-malou-api/foursquare_client_secret-xo6wmF"}, {"name": "GMB_CLIENT_SECRET", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app-malou-api/gmb_client_secret-1eFCut"}, {"name": "MONGODB_URI", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app-malou-api/mongodb_uri-GXAvpF"}, {"name": "PASSPORT_SECRET", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app-malou-api/passport_secret-xcdX5y"}, {"name": "FB_APP_TOKEN", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app_malou_api/fb_app_token-QmVFwm"}, {"name": "PUPPETEER_SERVICE_AUTHORIZATION", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app-malou-api/puppeteer_service_authorization-1JR6Ko"}, {"name": "BM_PRIVATE_KEY", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app-malou-api/bm_private_key-RC3kOq"}, {"name": "GITHUB_APP_CLIENT_SECRET", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:shared/github_app_client_secret-r9TX7r"}, {"name": "GITHUB_APP_PRIVATE_KEY", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:shared/github_app_private_key-eqMAcS"}, {"name": "MONGODB_AGENDA_URI", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app-malou-api/mongodb_agenda_uri-COELyB"}, {"name": "SCRAPPER_PROXY_TOKEN", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:production/app-malou-api/scrapper_proxy_token-5yB8GQ"}, {"name": "ENCRYPT_SECRET", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app_malou_api/encrypt_secret-n7UJhm"}, {"name": "OPENAI_API_KEY", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app-malou-api/openai_api_key-fdAp5K"}, {"name": "FIREBASE_PRIVATE_KEY", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app-malou-api/firebase_private_key-VY8mSS"}, {"name": "TRANSLATOR_AUTHORIZATION", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app-malou-api/translator_authorization-Xc16jf"}, {"name": "CLOUDINARY_API_SECRET", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:production/app-malou-api/cloudinary_api_secret-8INqaM"}, {"name": "YEXT_API_KEY", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:staging/app-malou-api/yext_api_key-js35Dz"}, {"name": "KEYWORD_TOOL_API_KEY", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:production/app-malou-api/keyword_tool_api_key-aAY5JG"}, {"name": "BRIGHT_DATA_RESIDENTIAL_PROXY_PASSWORD", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:production/app-malou-api/bright_data_residential_proxy_password-dVJMvZ"}, {"name": "BRIGHT_DATA_RESIDENTIAL_PROXY_US_PASSWORD", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:production/app-malou-api/bright_data_residential_proxy_password_2-CDAm9g"}, {"name": "APPLE_BUSINESS_CONNECT_CLIENT_SECRET", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:production/app-malou-api/apple_business_connect_client_secret-fSTCfp"}, {"name": "BRIGHT_DATA_ISP_PROXY_PASSWORD", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:production/app-malou-api/bright_data_isp_proxy_password-HSTEKW"}, {"name": "YELP_API_KEY", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app-malou-api/yelp_api_key-zsF3AD"}, {"name": "SLACK_TECH_BOT_TOKEN", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:shared/slack_tech_bot_token-hDCH1C"}, {"name": "SLACK_TECH_BOT_SIGNING_SECRET", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:shared/slack_tech_bot_signing_secret-WyNGQ4"}, {"name": "THEFORK_CLIENT_SECRET", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app-malou-api/the_fork_client_secret-SBxQj5"}]}, {"environment": [{"name": "OTEL_RESOURCE_ATTRIBUTES", "value": "service.name=worker,environment=development,deployment.environment=development"}], "essential": true, "image": "amazon/aws-otel-collector", "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/open-telemetry-collector-development", "awslogs-region": "eu-west-3", "awslogs-stream-prefix": "ecs"}}, "name": "open-telemetry-collector", "secrets": [{"name": "AOT_CONFIG_CONTENT", "valueFrom": "otel-collector-config"}]}], "cpu": "256", "executionRoleArn": "arn:aws:iam::515192135070:role/ecsTaskExecutionRole", "family": "app-malou-backend-worker", "memory": "1024", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "taskRoleArn": "arn:aws:iam::515192135070:role/ECSTaskRole"}