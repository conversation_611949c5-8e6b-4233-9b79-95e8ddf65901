import { Text } from '@react-email/components';

import { BaseEmailProps } from '@malou-io/package-dto';
import { Locale } from '@malou-io/package-utils';

import { MalouLayout } from ':shared/components';
import { Translation } from ':shared/services';

interface AwsVerificationMailProps extends BaseEmailProps {}

export const AwsVerificationMailTemplate = ({ locale = Locale.FR, receiver = 'User' }: AwsVerificationMailProps) => {
    const translator = new Translation(locale).getTranslator();
    return (
        <MalouLayout context={{ locale, receiver }} showFooter={false} useBase64Logo={true}>
            <Text>{translator.review_booster.verification.content()}</Text>
        </MalouLayout>
    );
};

export default AwsVerificationMailTemplate;
