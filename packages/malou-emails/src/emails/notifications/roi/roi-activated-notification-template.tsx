import { RoiActivatedEmailNotificationProps } from '@malou-io/package-dto';
import { EmailPadding, Locale } from '@malou-io/package-utils';

import { MalouLayout } from ':shared/components';
import { MalouBasicHeader } from ':shared/components/layout/header';
import { Translation } from ':shared/services';
import { EmailMargin } from ':shared/types/report.enum';

import Content from './content';

export const RoiActivatedNotificationTemplate = (props: RoiActivatedEmailNotificationProps) => {
    const { locale, unsubscribeLink, trackingUrl } = props;
    const translator = new Translation(locale).getTranslator();

    return (
        <MalouLayout
            customHeader={
                <MalouBasicHeader
                    locale={locale}
                    dateTimeFormat="dd MMMM yyyy"
                    notificationTitle={translator.notifications.common.notification()}
                    trackingUrl={trackingUrl}
                    xcss="text-malou-color-state-success rounded bg-malou-color-state-success/[.15] font-medium"
                />
            }
            context={{ locale, unsubscribeLink, trackingUrl }}
            emailMarginY={EmailMargin.LOW}
            paddingX={EmailPadding.LOW}>
            <Content {...props} />
        </MalouLayout>
    );
};

RoiActivatedNotificationTemplate.defaultProps = {
    locale: Locale.EN,
    receiver: 'Jonquille',
    link: 'https://malou.io',
    restaurantNames: ['La bonne fourchette', 'Bon gueleton', 'Les amis des Messina'],
    isAtLeastOneRestaurantWithoutRoiSettings: true,
};

export default RoiActivatedNotificationTemplate;
