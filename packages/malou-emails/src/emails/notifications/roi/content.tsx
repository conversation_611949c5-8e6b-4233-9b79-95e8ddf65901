import { Container } from '@react-email/components';
import { Markdown } from '@react-email/markdown';

import { RoiActivatedEmailNotificationProps } from '@malou-io/package-dto';

import { Hello, PrimaryButton } from ':shared/components';
import { Translation } from ':shared/services';

export const Content = (props: RoiActivatedEmailNotificationProps) => {
    const { locale, receiver, link, restaurantNames, isAtLeastOneRestaurantWithoutRoiSettings } = props;
    const translator = new Translation(locale).getTranslator();

    return (
        <Container className="px-8 mb-8">
            <Hello locale={locale} receiver={receiver} xcss="text-malou-color-text-2" />
            <Markdown markdownCustomStyles={{ p: { color: '#4a5e73', fontSize: '14px', margin: '0px' } }}>
                {translator.notifications.roi.title()}
            </Markdown>
            <Markdown markdownCustomStyles={{ p: { color: '#4a5e73', fontSize: '14px', marginTop: '16px' } }}>
                {isAtLeastOneRestaurantWithoutRoiSettings
                    ? translator.notifications.roi.restaurants_without_roi_settings({
                          restaurantNames: restaurantNames.join(', '),
                      })
                    : translator.notifications.roi.restaurants_with_roi_settings()}
            </Markdown>

            <PrimaryButton link={link} text={translator.notifications.roi.see()} css="mt-8"></PrimaryButton>
        </Container>
    );
};

export default Content;
