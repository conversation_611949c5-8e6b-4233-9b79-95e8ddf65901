import { Button, Container, Text } from '@react-email/components';
import { Markdown } from '@react-email/markdown';

import { PostEventReminderEmailNotificationProps } from '@malou-io/package-dto';

import { TranslationFunctions } from ':i18n/i18n-types';
import { Hello, PrimaryButton } from ':shared/components';
import { Translation } from ':shared/services';

export const Content = (props: PostEventReminderEmailNotificationProps) => {
    const { locale, receiver, link, restaurantNames, eventEmoji, eventName } = props;
    const translator = new Translation(locale).getTranslator();

    return (
        <Container className="px-1 mb-8">
            <Hello locale={locale} receiver={receiver} />
            <TitleTemplate restaurantNames={restaurantNames} translator={translator} />
            <Text className="text-[12px] m-0  font-normal text-malou-color-text-2 ">
                {translator.notifications.event_post.description()}
            </Text>
            <Button
                className="rounded-[5px] mt-5 bg-malou-color-background-white border-[1px] mb-5 border-solid border-malou-color-background-dark px-2 py-4 text-center text-malou-color-text-1 text-[13px] font-semibold text-bk no-underline sm:text-[10px] "
                style={{ width: 'calc(100% - 1rem)' }}>
                {eventName} {eventEmoji}
            </Button>
            <PrimaryButton link={link} text={translator.notifications.event_post.create()}></PrimaryButton>
        </Container>
    );
};

const TitleTemplate = (props: { restaurantNames: string[]; translator: TranslationFunctions }): React.ReactElement => {
    const { restaurantNames, translator } = props;
    const maxRestaurantsInTitle = 8;

    const restaurantsCount = restaurantNames.length;

    if (restaurantsCount <= maxRestaurantsInTitle) {
        return (
            <Text className="text-[12px] m-0 font-bold text-malou-color-text-2 ">
                {translator.notifications.event_post.title({
                    restaurants: restaurantNames.join(', '),
                })}
            </Text>
        );
    }

    const otherRestaurantsCount = restaurantsCount - maxRestaurantsInTitle;
    const slicedRestaurantsNames = restaurantNames.slice(0, maxRestaurantsInTitle);

    return (
        <Text className="text-[12px] m-0 text-malou-color-text-2 ">
            <Markdown>
                {translator.notifications.event_post.many_restaurants_title({
                    restaurants: slicedRestaurantsNames.join(', '),
                    remainingRestaurantsCount: otherRestaurantsCount,
                })}
            </Markdown>
        </Text>
    );
};

export default Content;
