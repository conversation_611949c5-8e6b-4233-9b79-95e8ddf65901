import { PostEventReminderEmailNotificationProps } from '@malou-io/package-dto';
import { EmailPadding, Locale } from '@malou-io/package-utils';

import { MalouLayout } from ':shared/components';
import { MalouBasicHeader } from ':shared/components/layout/header';
import { Translation } from ':shared/services';
import { EmailMargin } from ':shared/types/report.enum';

import Content from './content';

export const PostEventReminderNotificationTemplate = (props: PostEventReminderEmailNotificationProps) => {
    const { locale, unsubscribeLink, trackingUrl } = props;
    const translator = new Translation(locale).getTranslator();
    return (
        <MalouLayout
            customHeader={
                <MalouBasicHeader
                    locale={locale}
                    dateTimeFormat="dd MMM yyyy"
                    notificationTitle={translator.notifications.common.notification()}
                    trackingUrl={trackingUrl}
                    xcss="text-malou-color-state-success rounded bg-malou-color-state-success/[.15] font-medium"
                />
            }
            context={{ locale, unsubscribeLink, trackingUrl }}
            emailMarginY={EmailMargin.LOW}
            paddingX={EmailPadding.LOW}>
            <Content {...props} />
        </MalouLayout>
    );
};

PostEventReminderNotificationTemplate.defaultProps = {
    locale: Locale.EN,
    receiver: 'Jonquille',
    eventEmoji: '🎉',
    eventName: 'Fête du travail',
    link: 'https://malou.io',
    unsubscribeLink: 'https://malou.io',
    restaurantNames: [
        'la bonne fourchette 1',
        'la bonne fourchette 2',
        'la bonne fourchette 3',
        'la bonne fourchette 4',
        'la bonne fourchette 5',
        'la bonne fourchette 6',
        'la bonne fourchette 7',
        'la bonne fourchette 8',
        'la bonne fourchette 9',
        'la bonne fourchette 10',
        'la bonne fourchette 11',
        // 'la bonne fourchette 12',
    ],
};

export default PostEventReminderNotificationTemplate;
