import { Button, Column, Container, Img, Row, Section, Text } from '@react-email/components';
import { DateTime } from 'luxon';

import { Locale } from '@malou-io/package-utils';

import { MALOU_LOGO, MALOU_LOGO_BASE64 } from ':shared/constants';
import { Translation } from ':shared/services';
import { MalouBasicHeaderProps } from ':shared/types';

/**
 *
 * A styled header for Malou emails.
 *
 * @param {MalouBasicHeaderProps} props
 * @returns {React.ReactNode}
 */
export const MalouBasicHeader = (props: MalouBasicHeaderProps) => {
    const { trackingUrl, locale, dateTimeFormat, notificationTitle, xcss, useBase64Logo } = props;
    const translate = new Translation(locale).getTranslator();
    return (
        <Container className="!max-w-[600px] border-b border-solid border-[#F2F2FF] px-7">
            <Section>
                <Row>
                    <Column className="pt-8 align-top" align="left">
                        {/** Tracking */}
                        {trackingUrl && <Img src={trackingUrl} width={1} className="collapse" height={1} />}
                        {/** */}
                        {useBase64Logo ? (
                            <Img src={MALOU_LOGO_BASE64} height="17" alt="Malou Logo" />
                        ) : (
                            <Img src={MALOU_LOGO} height="17" alt="Malou Logo" />
                        )}
                    </Column>
                    <Column className="py-5 text-right" align="right">
                        <Button
                            className={`text-malou-color-state-warn rounded bg-malou-color-state-warn/[.15]  px-3 py-2 text-center text-[10px] font-semibold  leading-[16px] no-underline ${xcss}`}>
                            {notificationTitle || translate.reports.common.notification()}
                        </Button>
                        <Text className="m-0 mt-2 text-[12px] italic leading-[20px] text-malou-color-text-2">
                            {getDate(locale, dateTimeFormat)}
                        </Text>
                    </Column>
                </Row>
            </Section>
        </Container>
    );
};

const getDate = (locale: Locale, dateTimeFormat: string | undefined): string => {
    const now = DateTime.now();

    if (locale === Locale.EN) {
        return now.toFormat('MMMM dd, yyyy', {
            locale,
        });
    }
    return now.toFormat(dateTimeFormat || 'dd MMMM yyyy', {
        locale,
    });
};

MalouBasicHeader.defaultProps = {
    trackingUrl: '',
};
