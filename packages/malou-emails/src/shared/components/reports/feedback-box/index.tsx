import { Column, Row, Text } from '@react-email/components';

import { Locale, ReportType } from '@malou-io/package-utils';

import { PrimaryButtonWithIcon } from ':shared/components/buttons';
import { BasicSection } from ':shared/components/reports/sections';
import {
    DAILY_REVIEWS_FR_FEEDBACK_LINK,
    DAILY_REVIEWS_OTHER_LANGS_FEEDBACK_LINK,
    Emoji,
    WEEKLY_PERFORMANCE_FR_FEEDBACK_LINK,
    WEEKLY_PERFORMANCE_OTHER_LANGS_FEEDBACK_LINK,
    WEEKLY_REVIEWS_FR_FEEDBACK_LINK,
    WEEKLY_REVIEWS_OTHER_LANGS_FEEDBACK_LINK,
} from ':shared/constants';
import { Translation } from ':shared/services/translation.service';
import { FeedbackBoxProps } from ':shared/types';
import { getEmoji } from ':shared/utils';

/**
 *
 * A component that contains a button to redirect for a feedback form on reports.
 *
 * @param {FeedbackBoxProps} props - ReportType and locale
 * @returns {React.ReactNode}
 */

export const FeedbackBox = (props: FeedbackBoxProps) => {
    const { locale, reportType } = props;
    const translator = new Translation(locale).getTranslator();
    return (
        <BasicSection xcss="pb-5">
            {/** Desktop */}
            <Row className="mb-5 mt-5 rounded-md bg-malou-color-text-1 px-5 py-1 sm:pb-3 visible malou-hidden">
                <Column className="w-[60%] sm:w-[45%]">
                    <Text className="text-[12px] !mb-0 font-bold text-malou-color-text-white">{translator.reports.feedback.title()}</Text>
                    <Text className="text-[10px] !mt-0 font-normal text-malou-color-text-white">
                        {translator.reports.feedback.subtitle()}
                    </Text>
                </Column>
                <Column align="right" className="w-[30%] pr-3">
                    <Row>
                        <Column>
                            <PrimaryButtonWithIcon
                                text={`${translator.reports.feedback.button_text()}`}
                                link={getButtonLink(reportType, locale)}
                                css="bg-malou-color-primary font-semibold"
                                icon={getEmoji(Emoji.EMAIL_WITH_HEART)}
                            />
                        </Column>
                        <Column></Column>
                    </Row>
                </Column>
            </Row>

            {/** Mobile */}
            <Row className="rounded-md bg-malou-color-text-1 px-5 py-1 sm:pb-5 hidden malou-visible">
                <Column align="center">
                    <Text className="text-[12px] !mb-0 font-bold text-malou-color-text-white">{translator.reports.feedback.title()}</Text>
                    <Text className="text-[10px] !mt-0 font-normal text-malou-color-text-white">
                        {translator.reports.feedback.subtitle()}
                    </Text>
                    <PrimaryButtonWithIcon
                        text={`${translator.reports.feedback.button_text()}`}
                        link={getButtonLink(reportType, locale)}
                        css="bg-malou-color-primary font-semibold"
                        icon={getEmoji(Emoji.EMAIL_WITH_HEART)}
                    />
                </Column>
            </Row>
        </BasicSection>
    );
};

const getButtonLink = (reportType: ReportType, locale: Locale) => {
    const feedbackLinks: {
        [Locale.FR]: {
            [key in ReportType]: string;
        };
        otherLangs: {
            [key in ReportType]: string;
        };
    } = {
        [Locale.FR]: {
            [ReportType.DAILY_REVIEWS]: DAILY_REVIEWS_FR_FEEDBACK_LINK,
            [ReportType.WEEKLY_REVIEWS]: WEEKLY_REVIEWS_FR_FEEDBACK_LINK,
            [ReportType.WEEKLY_PERFORMANCE]: WEEKLY_PERFORMANCE_FR_FEEDBACK_LINK,
            [ReportType.MONTHLY_PERFORMANCE]: '',
        },
        otherLangs: {
            [ReportType.DAILY_REVIEWS]: DAILY_REVIEWS_OTHER_LANGS_FEEDBACK_LINK,
            [ReportType.WEEKLY_REVIEWS]: WEEKLY_REVIEWS_OTHER_LANGS_FEEDBACK_LINK,
            [ReportType.WEEKLY_PERFORMANCE]: WEEKLY_PERFORMANCE_OTHER_LANGS_FEEDBACK_LINK,
            [ReportType.MONTHLY_PERFORMANCE]: '',
        },
    };

    if (locale === Locale.FR) {
        return feedbackLinks[Locale.FR][reportType];
    }
    return feedbackLinks.otherLangs[reportType];
};

FeedbackBox.defaultProps = {
    locale: Locale.FR,
    ReportType: ReportType.DAILY_REVIEWS,
};
