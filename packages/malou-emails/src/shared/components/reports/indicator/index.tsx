import { Column, Img, Row, Text } from '@react-email/components';

import { GrowthStatsProps } from '@malou-io/package-dto';
import { GrowthVariation, Locale } from '@malou-io/package-utils';

import { Emoji } from ':shared/constants';
import { GrowthIndicatorProps } from ':shared/types';
import { VariationIcon } from ':shared/types/report.enum';
import { formatNumber, getEmoji, getImage } from ':shared/utils';

/**
 *
 * A component that shows the variation of reviews, rate and type.
 *
 * @param {GrowthIndicatorProps} props - rate, variation and type (percentage/value)
 * @returns {React.ReactNode}
 */

export const GrowthIndicator = (props: GrowthIndicatorProps) => {
    const { variation, flipped, locale } = props;

    return (
        <Row className={` ${getGrowthIndicatorBackground(variation)}   mx-0 w-fit rounded-lg px-2 py-1 text-center`}>
            <Column>
                <Img className="mx-1 my-0" src={getGrowthIcon(variation, flipped)} alt="arrow" height="12" />
            </Column>
            <Column>
                <Text className={` ${getTextColor(variation)} p !my-0 !py-0 text-[10px] font-normal sm:text-[10px]`}>
                    {formatNumber({ value: props.rate, isPercentage: props.isPercentage }, locale)}
                </Text>
            </Column>
        </Row>
    );
};

const getGrowthIndicatorBackground = (growth: GrowthVariation): string =>
    growth === GrowthVariation.UP ? 'bg-malou-color-state-success/[.15]' : 'bg-malou-color-state-error/[.15]';

const getTextColor = (growth: GrowthVariation): string =>
    growth === GrowthVariation.UP ? 'text-malou-color-state-success' : 'text-malou-color-state-error';

const getGrowthIcon = (growth: GrowthVariation, flipped: boolean = false): string =>
    growth === GrowthVariation.UP
        ? flipped
            ? getImage(VariationIcon.UP_FLIPPED, true)
            : getImage(VariationIcon.UP, true)
        : flipped
          ? getImage(VariationIcon.DOWN_FLIPPED, true)
          : getImage(VariationIcon.DOWN, true);

GrowthIndicator.defaultProps = {
    isPercentage: false,
};

/**
 *
 * A component that shows the variation of reviews (Arrow type).
 *
 * @param {GrowthStatsProps} props - rate, variation and type (percentage/value)
 * @returns {React.ReactNode}
 */

export const ArrowGrowthIndicator = (growth: GrowthStatsProps, locale: Locale): React.ReactNode => {
    const iconSrc = growth.variation === GrowthVariation.UP ? getEmoji(Emoji.UP_ARROW) : getEmoji(Emoji.DOWN_ARROW);
    const formattedGrowth = formatNumber({ value: growth.rate, isPercentage: growth.isPercentage }, locale);
    return (
        <span className="font-bold">
            {`(`}
            <Img
                src={iconSrc}
                alt="arrow"
                height="12"
                width="12"
                style={{
                    display: 'inline',
                    verticalAlign: 'text-bottom',
                    marginRight: '2px',
                    marginLeft: '2px',
                    marginBottom: '2px',
                }}
            />
            {formattedGrowth}
            {`)`}
        </span>
    );
};
