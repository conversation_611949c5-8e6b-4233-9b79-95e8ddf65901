import { Emoji, EMOJIS_LINK, ICONS_LINK, MALOU_LOGO, PLATFORMS_LOGOS_LINK } from ':shared/constants/constants';

/**
 *
 * A function to get the public resource link from its name.
 *
 * @param {string} name - Image full name
 * @param {boolean} isIcon - Icon type (<PERSON><PERSON> logo)
 * @param {boolean} isDummy - Dummy data (<PERSON><PERSON> logo)
 * @returns {string} - Public resource link
 */
export const getImage = (name: string, isIcon: boolean = false, isDummy: boolean = false): string =>
    isDummy ? MALOU_LOGO : `${isIcon ? ICONS_LINK : PLATFORMS_LOGOS_LINK}/${name}.png`;

/**
 *
 * A function to get the public resource emoji link from its name.
 *
 * @param {string} name - Image full name
 * @returns {string} - Public resource link
 */
export const getEmoji = (name: Emoji): string => `${EMOJIS_LINK}/${name}.png`;
