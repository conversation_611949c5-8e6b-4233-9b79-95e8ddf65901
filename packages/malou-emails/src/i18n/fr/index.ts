import type { BaseTranslation } from '../i18n-types';

const fr = {
    common: {
        hello: '<PERSON><PERSON><PERSON> {name: string},',
        simple_hello: '<PERSON><PERSON><PERSON>,',
        unsubscribe: '<PERSON> d<PERSON>abonner',
        goodbye: 'À très bientô<PERSON>,',
        thank_you: '<PERSON><PERSON><PERSON>!',
        langs: {
            af: 'Afrikaans',
            am: '<PERSON>harique',
            ar: 'Arabe',
            az: '<PERSON><PERSON><PERSON><PERSON>',
            be: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
            bg: 'Bulgare',
            bn: 'Bengali',
            bs: 'Bosniaque',
            ca: 'Catalan',
            ceb: 'Cebuano',
            co: 'Corse',
            cs: 'Tchèque',
            cy: 'G<PERSON><PERSON>',
            da: 'Danois',
            de: 'Allemand',
            el: 'Grec',
            en: 'Anglais',
            eo: 'Espéranto',
            es: 'Espagnol',
            et: 'Estonien',
            eu: 'Basque',
            fa: 'Persan',
            fi: 'Finnois',
            fr: 'Français',
            fy: 'Frison',
            ga: 'Irlandais',
            gd: '<PERSON><PERSON><PERSON><PERSON> écossais',
            gl: 'Galicien',
            gu: 'Gujarati',
            ha: 'Ha<PERSON><PERSON>',
            haw: 'Hawaïen',
            he: 'Hébreu',
            hi: 'Hindi',
            hmn: 'Hmong',
            hr: 'Croate',
            ht: 'Créole haïtien',
            hu: 'Hongrois',
            hy: 'Arménien',
            id: 'Indonésien',
            ig: 'Igbo',
            is: 'Islandais',
            it: 'Italien',
            iw: 'Hébreu',
            ja: 'Japonais',
            jw: 'Javanais',
            ka: 'Géorgien',
            kk: 'Kazakh',
            km: 'Khmer',
            kn: 'Kannada',
            ko: 'Coréen',
            ku: 'Kurde (kurmanji)',
            ky: 'Kirghize',
            la: 'Latin',
            lb: 'Luxembourgeois',
            lo: 'Laotien',
            lt: 'Lituanien',
            lv: 'Letton',
            mg: 'Malgache',
            mi: 'Maori',
            mk: 'Macédonien',
            ml: 'Malayalam',
            mn: 'Mongol',
            mr: 'Marathi',
            ms: 'Malais',
            mt: 'Maltais',
            my: 'Birman (birmanie)',
            ne: 'Népalais',
            nl: 'Néerlandais',
            no: 'Norvégien',
            ny: 'Chichewa',
            or: 'Odia',
            pa: 'Pendjabi',
            pl: 'Polonais',
            ps: 'Pachto',
            pt: 'Portugais',
            ro: 'Roumain',
            ru: 'Russe',
            sd: 'Sindhi',
            si: 'Cinghalais',
            sk: 'Slovaque',
            sl: 'Slovène',
            sm: 'Samoan',
            sn: 'Shona',
            so: 'Somali',
            sq: 'Albanais',
            sr: 'Serbe',
            st: 'Sesotho',
            su: 'Soundanais',
            sv: 'Suédois',
            sw: 'Swahili',
            ta: 'Tamoul',
            te: 'Télougou',
            tg: 'Tadjik',
            th: 'Thaï',
            tl: 'Filipino',
            tr: 'Turc',
            ug: 'Ouïghour',
            uk: 'Ukrainien',
            undetermined: 'Indéterminé',
            ur: 'Ourdou',
            uz: 'Ouzbek',
            vi: 'Vietnamien',
            xh: 'Xhosa',
            yi: 'Yiddish',
            yo: 'Yoruba',
            'zh-CN': 'Chinois simplifié',
            'zh-TW': 'Chinois traditionnel',
            zu: 'Zoulou',
        },
    },
    user: {
        password_reset: {
            change: 'Réinitialiser',
            title: 'Vous avez demandé à changer votre mot de passe.',
            content: 'Cliquez sur le lien ci-dessous afin de saisir votre nouveau mot de passe:',
            warning: "Attention ce lien n'est valable qu'une seule fois et que dans l'heure qui suit !",
        },
        account_confirmation: {
            title: 'Un compte MalouApp a été créé pour vous. Cliquez ici pour compléter votre inscription.',
            cta: 'Compléter votre inscription',
            warning: "Attention ce lien n'est valable qu'une seule fois et que dans l'heure qui suit !",
        },
        wrong_platform_access: {
            content: "Les accès que vous avez renseignés pour la connexion à {platformName: string} n'ont pas pu être vérifiés.",
            cta: 'Renseigner de nouveaux accès',
            thanks: 'Nous restons à votre disposition si besoin !',
        },
    },
    feedback: {
        post: 'post',
        story: 'story',
        user_commented:
            "{name: string} a ajouté une nouvelle remarque sur votre {object: string} {postStatus: string} pour le {date: string} à {time: string} sur {fullPlatformKeys: string} de l'établissement {restaurantName: string}.",
        check_feedback: 'Répondre à la remarque',
        user_closed_feedback_thread:
            "{name: string} a fermé la discussion sur votre post {postStatus: string} pour le {date: string} à {time: string} sur {fullPlatformKeys: string} de l'établissement {restaurantName: string}.",
        check_post: 'Consulter le post',
        wish_to_unsubscribe: 'Vous ne voulez plus recevoir de mail concernant les retours ?',
        unsubscribe: 'Désabonnez-vous',
        user_opened_feedback_thread:
            "{name: string} a réouvert la discussion sur votre post {postStatus: string} pour le {date: string} à {time: string} sur {fullPlatformKeys: string} de l'établissement {restaurantName: string}.",
    },
    permissions: {
        revoked_connection: {
            title: "Il semble que la connexion {platformName: string} sur l'établissement {restaurantName: string} soit rompue.",
            title_multiple_restaurants: 'Il semble que la connexion {platformName: string} soit rompue sur : {restaurantNames: string}',
            content: 'Les fonctionnalités liées à ces plateformes risquent de ne plus fonctionner correctement.',
            cta: 'Rétablir la connexion',
            disconnect_platform_cta: 'Déconnecter cette plateforme pour ne plus recevoir cet e-mail',
        },
    },
    review_booster: {
        verification: {
            content:
                'Veuillez cliquer sur le lien ci-dessous pour valider votre adresse avec laquelle envoyer des e-mails depuis la MalouApp (campagnes, réponse aux avis privés).',
        },
        client_review_booster: {
            title: 'Votre avis sur : {restaurantName: string} !',
            no_more_messages: 'Vous ne voulez plus recevoir des messages de {restaurantName: string} ?',
            content:
                'Nous espérons que vous avez apprécié votre expérience chez nous et nous vous serions reconnaissants de nous faire part de votre avis ! Cela ne prend que quelques secondes, et il vous suffit simplement de cliquer sur le lien ci-dessous.',
            content2:
                ' Il sera lu avec la grand attention, que ce soir pour féliciter nos équipes ou pour prendre en compte vos éventuelles remarqes et vous apporter pleine satisfaction lors de votre prochaine visite.',
        },
    },
    posts: {
        expired_location: {
            content: 'Vous aviez programmé un post pour le {publicationDate: string} sur {platformName: string}.',
            content2:
                "Le post a bien été publié, cependant le lieu que vous aviez tagué sur le post était introuvable, le post a par conséquent été publié sans lieu associé. Nous recommandons de taguer des lieux sur vos posts afin d'améliorer votre référencement local.",
            cta: 'Consulter le post',
            content3: 'Rendez-vous sur la',
            content4: 'pour vérifier vos prochains posts programmés.',
        },
        error_publication: {
            title: 'Une erreur est survenue lors de la publication de votre post.',
            storyTitle: 'Une erreur est survenue lors de la publication de votre story.',
            storyContent: 'Votre story n’a pas pu être publiée sur {fullPlatformKey: string} pour {restaurantName: string}.',
            storyContentWithDateTime:
                'Votre story du {date: string} à {time: string} n’a pas pu être publiée sur {fullPlatformKey: string} pour {restaurantName: string}.',
            content: "Le post n'a pas pu être publié sur votre compte {fullPlatformKey: string} pour {restaurantName: string}.",
            contentWithDateTime:
                "Le post du {date: string} à {time: string} n'a pas pu être publié sur votre compte {fullPlatformKey: string} pour {restaurantName: string}.",
        },
    },
    ai: {
        api_hard_limit: {
            content:
                "Vous disposez de {hardLimit: number} utilisations de l'IA par mois pour votre établissement {restaurantName: string} ! Si vous avez besoin de plus de crédits, discutez-en avec votre contact privilégié chez Malou.",
        },
    },
    reports: {
        common: {
            notification: 'Notification',
            concerned_restaurants_number: {
                one: '1 établissement',
                multiple: '{data: number} établissements',
            },
            global_focus: {
                one: 'Focus global sur 1 établissement',
                multiple: 'Focus global sur {data: number} établissements',
            },
            daily_reviews_report: 'Rapport Quotidien des Avis',
            weekly_reviews_report: 'Rapport Hebdomadaire des Avis',
            weekly_performance_report: {
                normal: 'Rapport Hebdomadaire',
                grouped: 'Rapport Hebdomadaire Groupé',
            },
            monthly_performance_report: {
                normal: 'Rapport Mensuel de Performance',
                grouped: 'Rapport Mensuel Groupé',
            },
            previous_period: 'Période précédente : {data: number} avis',
            to_this_time: ' note à ce jour',
        },
        events: {
            title: { one: '1 événement dans les prochains jours', multiple: '{data: number} événements dans les prochains jours' },
            holiday_reminder: {
                one: "N'oubliez pas de mettre à jour vos horaires d'ouverture pour le {date: string} qui est férié.", // 15 janvier for example
                multiple:
                    "N'oubliez pas de mettre à jour vos horaires d'ouverture pour les {dates: string} et {lastDate: string} qui sont fériés.", // 15 et 21 janvier or 28 janvier et 2 fevrier
                subtitle: 'Remontez dans les recherches en confirmant à Google que vous êtes ouverts',
            },
            holiday: 'Jour férié',
        },
        reviews: {
            title: 'Avis',
            no_daily_reviews: "Vous n'avez reçu aucun avis hier.",
            no_weekly_reviews: "Vous n'avez reçu aucun avis la semaine dernière.",
            best_restaurant: {
                one: "L'établissement qui a récolté les meilleurs avis",
                multiple: 'Vos {data: number} établissements qui ont récolté les meilleurs avis',
            },
            worst_restaurant: {
                one: "L'établissement qui a récolté les moins bons avis",
                multiple: 'Vos {data: number} établissements qui ont récolté les moins bons avis',
            },
            answer_client_reviews_text: 'Améliorez votre référencement et vos relations client en répondant rapidement à vos avis.',
            low_reviews_description: '(dont {totalLowReviews: number} avis, 1,2 ou 3 étoiles)',
            not_answered_title: 'Avis non répondus',
            not_answered: { one: '(1 non répondu)', multiple: '({data: number} non répondus)' },
            average: 'moyenne',
            semantic_analysis: 'Analyse sémantique des avis par catégorie',
            sentiments: { positive: 'Sentiments positifs', negative: 'Sentiments négatifs' },
            monthly: {
                gained: "Vous avez gagné plus d'avis que le mois dernier",
                lost: "Vous avez gagné moins d'avis que le mois dernier",
                same: "Vous avez gagné autant d'avis que le mois dernier",
                none: "Vous n'avez pas reçu d'avis le mois dernier",
            },
            weekly: {
                gained: "Vous avez gagné plus d'avis que la semaine dernière",
                lost: "Vous avez gagné moins d'avis que la semaine dernière",
                same: "Vous avez gagné autant d'avis que la semaine dernière",
                none: "Vous n'avez pas reçu d'avis la semaine dernière",
            },
            received: { one: '1 avis', multiple: '{data: number} avis' },
            pending: '{data: number} avis sont en attente de réponse',
            total_received: 'sur {data: number} avis reçus',
            all_answered: 'Tous vos avis ont été répondus',
        },
        booster: {
            totems: 'Totems',
            notice_try: "Avez-vous testé les totems pour booster votre récolte d'avis ?",
            notice_use: "Utilisez-vous les totems pour booster votre récolte d'avis ?",
            with: {
                totems: 'aux totems',
                wof: 'aux roues de la fortune',
                both: 'aux boosters',
            },
            reviews_gained: 'Vous avez récupéré {reviewsNumber: number} avis en plus grâce {type: string}',
            best_restaurant: "L'établissement qui a reçu le plus d'avis grâce {type: string} ",
            worst_restaurant: "L'établissement qui a reçu le moins d'avis grâce {type: string} ",
        },
        platforms: {
            disappointed_client: {
                one: 'Votre client le plus déçu sur {platformName: string}',
                many: 'Vos {clientsCount: number} clients les plus déçus sur {platformName: string}',
            },
            best_reviews: {
                one: 'Votre meilleur avis {platformName: string}',
                many: 'Vos {clientsCount: number} meilleurs avis {platformName: string}',
            },
            manage_requests_text: "Assurez-vous de répondre à toutes les demandes de réservation ou d'information.",
            subscribers: 'Abonnés',
            impressions: 'Impressions',
            engagement: {
                title: 'Engagement',
                rate: {
                    up: "Votre taux d'engagement sur {socialType: string} a augmenté",
                    down: "Votre taux d'engagement sur {socialType: string} a baissé",
                    stable: "Votre taux d'engagement sur {socialType: string} est stable",
                },
                best_restaurant: "L'établissement qui a eu le meilleur taux d'engagement",
                worst_restaurant: "L'établissement qui a eu le taux d'engagement le plus bas",
            },
            messages: {
                title: 'Messages reçus la semaine dernière',
                not_answered: {
                    one: 'Vous avez 1 message non répondu',
                    multiple: 'Vous avez {data: number} messages non répondus',
                },
            },
            publications: {
                posted_this_week: 'Posts publiés cette semaine',
                weekly_best: {
                    post: {
                        one: 'Le post qui a fait le plus réagir cette semaine',
                        multiple: 'Le post qui a fait le plus réagir cette semaine parmi vos {data: number} publications',
                    },
                    reel: {
                        one: 'Le reel qui a fait le plus réagir cette semaine',
                        multiple: 'Le reel qui a fait le plus réagir cette semaine parmi vos {data: number} publications',
                    },
                },
                monthly_best: {
                    post: {
                        one: 'Le post qui a fait le plus réagir ce mois-ci',
                        multiple: 'Le post qui a fait le plus réagir ce mois-ci parmi vos {data: number} publications',
                    },
                    reel: {
                        one: 'Le reel qui a fait le plus réagir ce mois-ci',
                        multiple: 'Le reel qui a fait le plus réagir ce mois-ci parmi vos {data: number} publications',
                    },
                },
            },
            google: {
                best_restaurant: "L'établissement qui est le plus apparu sur Google",
                worst_restaurant: "L'établissement qui est le moins apparu sur Google",
                visibility: 'Apparitions de vos établissements sur Google',
                conversion_rate_title: 'Taux de conversion',
                conversion_rate_sub_text: 'Visiteurs qui effectuent une action',
                actions: 'Actions',
                positioning_improvement: 'Mots-clés où votre positionnement a évolué',
                conversion_rate: {
                    up: "Votre nombre d'actions Google a augmenté",
                    down: "Votre nombre d'actions Google a baissé",
                    stable: "Votre nombre d'actions Google est stable",
                },
            },
        },
        average_rating: {
            title: 'Note moyenne',
            long_title: 'Note moyenne de vos avis',
            description: '(sur toutes vos plateformes)',
            long_title_variant: 'moyenne des avis reçus',
        },
        rank: {
            first: '1er',
            second: '2ème',
            third: '3ème',
            other: '{rank: number}ème',
        },
        buttons: {
            answer_short: 'Répondre',
            answer_reviews: 'Répondre à mes avis',
            check_reviews: 'Voir les avis',
            more_details: 'Voir le détail',
            update_time: 'Modifier mes horaires',
            create_post: 'Créer un post',
            learn_more: 'En savoir plus',
        },
        feedback: {
            title: 'Que pensez-vous de ce rapport ?',
            subtitle: "Donnez-nous votre avis pour l'améliorer selon vos besoins !",
            button_text: 'Donner mon avis',
        },
    },
    wheels_of_fortune: {
        empty_stock: {
            edit_stocks: 'Modifier mes stocks',
            your_gift_stock_is_empty:
                'Votre cadeau "{giftName: string}" est en rupture de stock sur votre roue de la fortune de l\'établissement "{restaurantName: string}". Si vous souhaitez que vos clients continuent d\'en bénéficier, modifiez vos stocks dans les options avancées.',
        },
        gift_expires_soon: {
            your_giveaway_expires_soon: 'Votre coupon cadeau pour <b>{gift: string} expire bientôt !</b>',
            meet_at_your_restaurant: "Vous l'avez gagné dans l'établissement <b>{restaurantName: string}, {restaurantAddress: string}.</b>",
            conditions: "Des conditions s'appliquent : {conditions: string}.",
            expiration_warning: 'Pour rappel, votre cadeau expire le {date: string}.',
            access_gift: 'Accéder à mon cadeau',
            subject_to_purchase: "La récupération de votre cadeau est <b>soumise à condition d'achat</b>.",
        },
        retrieve_gift: {
            you_have_won:
                "Bravo, vous avez gagné <b>{gift: string}</b> lors de votre visite dans l'établissement <b>{businessName: string}, {businessAddress: string}</b>.",
            conditions: "Des conditions s'appliquent : {conditions: string}.",
            dates_reminder:
                'Pour rappel, votre cadeau est disponible à partir du {retrievalStartDate: string} et expire le {retrievalEndDate: string}.',
            pick_up_my_gift: 'Récupérer mon cadeau',
            subject_to_purchase: "La récupération de votre cadeau est <b>soumise à condition d'achat</b>.",
        },
        wof_live_tomorrow: {
            scheduled_wof_live_tomorrow:
                'La roue de la fortune que vous avez programmée pour <b>{restaurantNames: string}</b> sera <b>activée demain</b>. Vos clients auront donc accès à votre roue via le lien et le QR code.',
            see_my_wof: 'Voir ma roue de la fortune',
            totems: 'Vos totems sont également redirigés vers votre roue :',
        },
    },
    reviews: {
        download_reviews: {
            title: 'Export des Avis',
            no_reviews_to_download: 'Vous n’avez pas reçu d’avis sur vos établissements.',
            reviews: '({reviewsCount: number} Avis)',
        },
        modified: 'Modifié',
        translated_from: '*Traduit de la langue : {language: string}*',
        intelligent_subjects: {
            notificationTitle: 'Sujet sensible',
            text: 'Bonjour {receiver}, un sujet sensible a été détecté dans un avis sur ',
            primary_button: "Répondre à l'avis",
        },
    },
    platforms: {
        mapstr_reminder: {
            title: 'Vous avez fait la demande de connexion entre Malou et Mapstr Premium.',
            description:
                'Si vous êtes clients Mapstr Premium, votre clé de connexion Malou est maintenant disponible dans votre dashboard Mapstr, page "Publications", et vous pouvez finaliser la connexion.',
            primary_button: 'Finaliser la connexion Mapstr Premium',
        },
    },
    restaurant_diagnostic: {
        title: 'Diagnostic',
        text: 'Votre diagnostic de visibilité en ligne pour {restaurantName: string } est prêt !',
        check_diagnostic: 'Voir mon diagnostic complet',
    },
    notifications: {
        common: {
            notification: 'Notification',
            diagnostic: 'Diagnostic',
        },
        holidays: {
            title: 'Le <b>{date: string}</b> aura lieu un évènement important :',
            subtitle: 'Indiquez à vos clients si vous êtes ouvert. Cela aidera à <b>améliorer votre position sur Google!</b>',
            confirm_hours: 'Confirmer mes horaires',
        },
        reviews: {
            negative_review: {
                title: 'Bientôt <b>72 heures que cet avis négatif est sans réponse</b> sur <b>{restaurantName: string}.</b>',
                title_with_multiple_reviews:
                    'Vous avez <b>{unansweredReviewsCount: number} avis négatifs en attente de réponse</b> sur <b>{restaurantName: string}.</b>',
                warning_text: '{platformName: string} favorise les établissements qui répondent rapidement aux avis.',
                other_restaurants_reminder:
                    '<b>{reviewsCount: number} autres avis négatifs en attente de réponse</b> sur {restaurantsName: string}.',
                reply_to_reviews: 'Répondre à vos avis',
                remaining_restaurant: '1 autre avis négatif en attente de réponse sur {restaurantsCount: number} établissements',
                remaining_restaurants:
                    '{reviewsCount: number} autres avis négatifs en attente de réponse sur {restaurantsCount: number} établissements',
            },
        },
        event_post: {
            title: 'Vous n’avez pas posté sur {restaurants: string} depuis un moment.',
            many_restaurants_title:
                '<strong>Vous n’avez pas posté sur {restaurants: string}, et {remainingRestaurantsCount: number} autres établissements</strong> depuis un moment.',
            description: 'Un nouvel évènement approche, pourquoi ne pas prévenir vos clients de ce que vous prévoyez pour l’occasion ?',
            create: 'Créer un post',
        },
        roi: {
            title: 'Que le temps passe vite, voilà plus de 4 mois que vous travaillez avec Malou!',
            restaurants_with_roi_settings:
                'Nous avons estimé <strong> vos gains grâce au marketing </strong> pendant ce temps, qu’en dites-vous ?',
            restaurants_without_roi_settings:
                "Souhaitez-vous découvrir <strong> vos gains grâce au marketing </strong> effectués jusqu'ici pour {restaurantNames: string}? Remplissez vos informations sans plus attendre.",
            see: 'Voir l’estimation de mes gains',
        },
        summary: {
            main_text:
                'Des choses se sont passées depuis votre dernière connexion ! Voici quelques notifications que vous auriez pu rater :',
            notifications_link_text: 'Voir les autres notifications',
            see: 'Voir',
            special_hour: {
                text: 'Indiquez à vos clients si vous êtes <strong> ouvert le {date: string} </strong> pour tous vos établissements <strong> ({eventName: string}). </strong>',
            },
            post_suggestion: {
                text: 'Un évènement approche : {name: string} le {date: string}.',
            },
            reviews: {
                title: '{restaurantCount: number} de vos établissements ont reçu des avis',
                text: 'Vous avez reçu {count: number} {{nouvel|nouveaux}} avis',
            },
            comments: {
                multiple_restaurant_title:
                    '{restaurantCount: number} de vos établissements ont reçu des commentaires sur les réseaux sociaux',
                multiple_comments_title: '{restaurantName: string} a reçu {commentCount: number} commentaires sur les réseaux sociaux',
                single_comment_title: '{restaurantName: string} a reçu un commentaire sur les réseaux sociaux',
                single_comment_text: '@{authorDisplayName: string} : {text: string}',
            },
            mentions: {
                multiple_restaurant_title: '{restaurantCount: number} de vos établissements ont été mentionnés',
                single_mention_title: '{restaurantName: string} a été mentionné',
                mention_text_with_author: '@{authorDisplayName: string} : {text: string}',
            },
            messages: {
                multiple_restaurant_title: '{restaurantCount: number} de vos établissements ont reçu des messages',
                multiple_message_title: '{restaurantName: string} a reçu {messageCount: number} messages',
                single_message_title: '{restaurantName: string} a reçu un messsage',
                single_message_text: '{senderName: string} : {text: string}',
            },
            post_error: {
                error_occured: 'Une erreur est survenue',
                text: "Votre post n'a pas pu être publié sur {restaurantName: string}",
            },
            info_error: {
                text: 'Erreur lors de la mise à jour de vos informations sur {restaurantName: string}',
            },
        },
        platform_update_suggestion: {
            main_text:
                'Des utilisateurs de {platformName: string} ont proposé des modifications sur votre fiche <strong>{restaurantName: string}.</strong>',
            main_text_multiple:
                'Des utilisateurs de {platformName: string} ont proposé des modifications sur vos fiches : <strong>{restaurantNames: string}.</strong>',
            sub_text_1: "Nous vous recommandons de vérifier l'exactitude de vos informations et de traiter la suggestion rapidement.",
            sub_text_2: 'Il se peut que Google mette à jour vos informations à votre place.',
            cta: 'Vérifier mes informations',
        },
    },
} satisfies BaseTranslation;

export default fr;
