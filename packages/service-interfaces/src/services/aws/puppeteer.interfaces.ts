export enum PuppeteerLambdaEventName {
    INSIGHTS_PDF = 'insights-pdf',
    DELIVEROO_PLATFORM_RATING = 'deliveroo-platform-rating',
}

// Insights PDF
interface PuppeteerInsightsPdfPayload {
    jwtToken: string;
    baseUrl: string;
    callBackUrl: string;
    pdfParams: string;
    awsBucketName: string;
    awsBucketPath: string;
}

// Deliveroo Platform Rating
interface PuppeteerDeliverooPlatformRatingPayload {
    drnId: string;
    accessToken: string;
}

export interface PuppeteerDeliverooPlatformRatingResponse {
    data?: {
        CurrentRating?: {
            branch_drn_id: string;
            average_rating: number;
            rating_count: number;
            rating_breakdown: {
                one_star_count: number;
                two_star_count: number;
                three_star_count: number;
                four_star_count: number;
                five_star_count: number;
            };
        }[];
        Message?: string;
    };
    success: boolean;
    errorMessage?: string;
}

interface PuppeteerPayload extends Record<PuppeteerLambdaEventName, any> {
    [PuppeteerLambdaEventName.INSIGHTS_PDF]: PuppeteerInsightsPdfPayload;
    [PuppeteerLambdaEventName.DELIVEROO_PLATFORM_RATING]: PuppeteerDeliverooPlatformRatingPayload;
}

export interface PuppeteerLambdaEvent<T extends PuppeteerLambdaEventName = PuppeteerLambdaEventName> {
    eventType: T;
    payload: PuppeteerPayload[T];
}
