export enum CarouselAspectRatio {
    '4:5' = '4:5',
    '1:1' = '1:1',
    '16:9' = '16:9',
}

export function carouselAspectRatioToNumber(carouselAspectRatio: CarouselAspectRatio): number {
    switch (carouselAspectRatio) {
        case CarouselAspectRatio['4:5']:
            return 4 / 5;
        case CarouselAspectRatio['1:1']:
            return 1;
        case CarouselAspectRatio['16:9']:
            return 16 / 9;
        default:
            throw new Error(`Unknown carousel aspect ratio: ${carouselAspectRatio}`);
    }
}

export function numberToCarouselAspectRatio(aspectRatio: number): CarouselAspectRatio | undefined {
    switch (aspectRatio) {
        case 4 / 5:
            return CarouselAspectRatio['4:5'];
        case 1:
            return CarouselAspectRatio['1:1'];
        case 16 / 9:
            return CarouselAspectRatio['16:9'];
        default:
            return undefined;
    }
}
