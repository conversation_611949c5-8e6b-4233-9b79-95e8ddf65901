import { PublicationType } from '../../posts';
import { MediaCropComputer } from './media-crop-computer';

describe('MediaCropComputer', () => {
    describe('computePreferredAspectRatioFor', () => {
        describe('for POST', () => {
            describe('should return 1:1 ratio (square)', () => {
                it('should return 1 for exact square (1:1)', () => {
                    const result = MediaCropComputer.computePreferredAspectRatioFor({
                        publicationType: PublicationType.POST,
                        originalAspectRatio: 1,
                    });
                    expect(result).toBe(1);
                });

                it('should return 1 for near-square (1.005:1)', () => {
                    const result = MediaCropComputer.computePreferredAspectRatioFor({
                        publicationType: PublicationType.POST,
                        originalAspectRatio: 1.005,
                    });
                    expect(result).toBe(1);
                });

                it('should return 1 for near-square (0.995:1)', () => {
                    const result = MediaCropComputer.computePreferredAspectRatioFor({
                        publicationType: PublicationType.POST,
                        originalAspectRatio: 0.995,
                    });
                    expect(result).toBe(1);
                });
            });

            describe('should return 4:5 ratio (portrait)', () => {
                it('should return 0.8 for exact portrait (4:5)', () => {
                    const result = MediaCropComputer.computePreferredAspectRatioFor({
                        publicationType: PublicationType.POST,
                        originalAspectRatio: 4 / 5,
                    });
                    expect(result).toBe(4 / 5);
                });

                it('should return 0.8 for tall portrait (9:16)', () => {
                    const result = MediaCropComputer.computePreferredAspectRatioFor({
                        publicationType: PublicationType.POST,
                        originalAspectRatio: 9 / 16,
                    });
                    expect(result).toBe(4 / 5);
                });

                it('should return 0.8 for very tall portrait (3:4)', () => {
                    const result = MediaCropComputer.computePreferredAspectRatioFor({
                        publicationType: PublicationType.POST,
                        originalAspectRatio: 3 / 4,
                    });
                    expect(result).toBe(4 / 5);
                });

                it('should return 0.8 for slightly portrait (0.85:1)', () => {
                    const result = MediaCropComputer.computePreferredAspectRatioFor({
                        publicationType: PublicationType.POST,
                        originalAspectRatio: 0.85,
                    });
                    expect(result).toBe(4 / 5);
                });
            });

            describe('should return 16:9 ratio (landscape)', () => {
                it('should return ~1.778 for exact landscape (16:9)', () => {
                    const result = MediaCropComputer.computePreferredAspectRatioFor({
                        publicationType: PublicationType.POST,
                        originalAspectRatio: 16 / 9,
                    });
                    expect(result).toBe(16 / 9);
                });

                it('should return ~1.778 for wide landscape (2:1)', () => {
                    const result = MediaCropComputer.computePreferredAspectRatioFor({
                        publicationType: PublicationType.POST,
                        originalAspectRatio: 2,
                    });
                    expect(result).toBe(16 / 9);
                });

                it('should return ~1.778 for ultra-wide landscape (21:9)', () => {
                    const result = MediaCropComputer.computePreferredAspectRatioFor({
                        publicationType: PublicationType.POST,
                        originalAspectRatio: 21 / 9,
                    });
                    expect(result).toBe(16 / 9);
                });

                it('should return ~1.778 for slightly landscape (1.5:1)', () => {
                    const result = MediaCropComputer.computePreferredAspectRatioFor({
                        publicationType: PublicationType.POST,
                        originalAspectRatio: 1.5,
                    });
                    expect(result).toBe(16 / 9);
                });
            });
        });

        describe('for REEL', () => {
            it('should return 9:16 ratio (~0.5625)', () => {
                const result = MediaCropComputer.computePreferredAspectRatioFor({
                    publicationType: PublicationType.REEL,
                });
                expect(result).toBe(9 / 16);
            });
        });

        describe('for STORY', () => {
            it('should return 9:16 ratio (~0.5625)', () => {
                const result = MediaCropComputer.computePreferredAspectRatioFor({
                    publicationType: PublicationType.STORY,
                });
                expect(result).toBe(9 / 16);
            });
        });
    });
    describe('computeArea', () => {
        describe('with targetAspectRatio 4/5 (0.8)', () => {
            const targetAspectRatio = 4 / 5;

            it('should return full area when original aspect ratio equals target (4:5)', () => {
                const result = MediaCropComputer.computeArea({
                    originalAspectRatio: 4 / 5,
                    targetAspectRatio,
                });
                expect(result).toEqual({
                    left: 0,
                    top: 0,
                    width: 1,
                    height: 1,
                });
            });

            it('should crop horizontally when original is wider (landscape 16:9 to portrait 4:5)', () => {
                const result = MediaCropComputer.computeArea({
                    originalAspectRatio: 16 / 9,
                    targetAspectRatio,
                });
                expect(result.top).toBe(0);
                expect(result.height).toBe(1);
                expect(result.width).toBeCloseTo(0.45, 2);
                expect(result.left).toBeCloseTo(0.275, 2);
            });

            it('should crop horizontally when original is square (1:1 to portrait 4:5)', () => {
                const result = MediaCropComputer.computeArea({
                    originalAspectRatio: 1,
                    targetAspectRatio,
                });
                expect(result.top).toBe(0);
                expect(result.height).toBe(1);
                expect(result.width).toBe(0.8);
                expect(result.left).toBeCloseTo(0.1, 5);
            });

            it('should crop vertically when original is taller (9:16 to 4:5)', () => {
                const result = MediaCropComputer.computeArea({
                    originalAspectRatio: 9 / 16,
                    targetAspectRatio,
                });
                expect(result.left).toBe(0);
                expect(result.width).toBe(1);
                expect(result.height).toBeCloseTo(0.703125, 5);
                expect(result.top).toBeCloseTo(0.1484375, 5);
            });

            it('should crop vertically when original is very tall (3:4 to 4:5)', () => {
                const result = MediaCropComputer.computeArea({
                    originalAspectRatio: 3 / 4,
                    targetAspectRatio,
                });
                expect(result.left).toBe(0);
                expect(result.width).toBe(1);
                expect(result.height).toBeCloseTo(0.9375, 5);
                expect(result.top).toBeCloseTo(0.03125, 5);
            });

            it('should crop horizontally when original is ultra-wide (21:9 to 4:5)', () => {
                const result = MediaCropComputer.computeArea({
                    originalAspectRatio: 21 / 9,
                    targetAspectRatio,
                });
                expect(result.top).toBe(0);
                expect(result.height).toBe(1);
                expect(result.width).toBeCloseTo(0.3428571, 5);
                expect(result.left).toBeCloseTo(0.3285714, 5);
            });
        });

        describe('with targetAspectRatio 1/1 (1.0)', () => {
            const targetAspectRatio = 1;

            it('should return full area when original aspect ratio equals target (1:1)', () => {
                const result = MediaCropComputer.computeArea({
                    originalAspectRatio: 1,
                    targetAspectRatio,
                });
                expect(result).toEqual({
                    left: 0,
                    top: 0,
                    width: 1,
                    height: 1,
                });
            });

            it('should crop horizontally when original is landscape (16:9 to 1:1)', () => {
                const result = MediaCropComputer.computeArea({
                    originalAspectRatio: 16 / 9,
                    targetAspectRatio,
                });
                expect(result.top).toBe(0);
                expect(result.height).toBe(1);
                expect(result.width).toBeCloseTo(0.5625, 5);
                expect(result.left).toBeCloseTo(0.21875, 5);
            });

            it('should crop horizontally when original is wide landscape (2:1 to 1:1)', () => {
                const result = MediaCropComputer.computeArea({
                    originalAspectRatio: 2,
                    targetAspectRatio,
                });
                expect(result.top).toBe(0);
                expect(result.height).toBe(1);
                expect(result.width).toBe(0.5);
                expect(result.left).toBe(0.25);
            });

            it('should crop vertically when original is portrait (4:5 to 1:1)', () => {
                const result = MediaCropComputer.computeArea({
                    originalAspectRatio: 4 / 5,
                    targetAspectRatio,
                });
                expect(result.left).toBe(0);
                expect(result.width).toBe(1);
                expect(result.height).toBe(0.8);
                expect(result.top).toBeCloseTo(0.1, 5);
            });

            it('should crop vertically when original is tall portrait (9:16 to 1:1)', () => {
                const result = MediaCropComputer.computeArea({
                    originalAspectRatio: 9 / 16,
                    targetAspectRatio,
                });
                expect(result.left).toBe(0);
                expect(result.width).toBe(1);
                expect(result.height).toBe(0.5625);
                expect(result.top).toBe(0.21875);
            });

            it('should crop horizontally when original is ultra-wide (21:9 to 1:1)', () => {
                const result = MediaCropComputer.computeArea({
                    originalAspectRatio: 21 / 9,
                    targetAspectRatio,
                });
                expect(result.top).toBe(0);
                expect(result.height).toBe(1);
                expect(result.width).toBeCloseTo(0.4285714, 5);
                expect(result.left).toBeCloseTo(0.2857143, 5);
            });
        });

        describe('with targetAspectRatio 16/9 (1.777...)', () => {
            const targetAspectRatio = 16 / 9;

            it('should return full area when original aspect ratio equals target (16:9)', () => {
                const result = MediaCropComputer.computeArea({
                    originalAspectRatio: 16 / 9,
                    targetAspectRatio,
                });
                expect(result).toEqual({
                    left: 0,
                    top: 0,
                    width: 1,
                    height: 1,
                });
            });

            it('should crop horizontally when original is ultra-wide (21:9 to 16:9)', () => {
                const result = MediaCropComputer.computeArea({
                    originalAspectRatio: 21 / 9,
                    targetAspectRatio,
                });
                expect(result.top).toBe(0);
                expect(result.height).toBe(1);
                expect(result.width).toBeCloseTo(0.7619048, 5);
                expect(result.left).toBeCloseTo(0.1190476, 5);
            });

            it('should crop horizontally when original is very wide (2:1 to 16:9)', () => {
                const result = MediaCropComputer.computeArea({
                    originalAspectRatio: 2,
                    targetAspectRatio,
                });
                expect(result.top).toBe(0);
                expect(result.height).toBe(1);
                expect(result.width).toBeCloseTo(0.8888889, 5);
                expect(result.left).toBeCloseTo(0.0555556, 5);
            });

            it('should crop vertically when original is square (1:1 to 16:9)', () => {
                const result = MediaCropComputer.computeArea({
                    originalAspectRatio: 1,
                    targetAspectRatio,
                });
                expect(result.left).toBe(0);
                expect(result.width).toBe(1);
                expect(result.height).toBeCloseTo(0.5625, 5);
                expect(result.top).toBeCloseTo(0.21875, 5);
            });

            it('should crop vertically when original is portrait (4:5 to 16:9)', () => {
                const result = MediaCropComputer.computeArea({
                    originalAspectRatio: 4 / 5,
                    targetAspectRatio,
                });
                expect(result.left).toBe(0);
                expect(result.width).toBe(1);
                expect(result.height).toBeCloseTo(0.45, 5);
                expect(result.top).toBeCloseTo(0.275, 5);
            });

            it('should crop vertically when original is tall portrait (9:16 to 16:9)', () => {
                const result = MediaCropComputer.computeArea({
                    originalAspectRatio: 9 / 16,
                    targetAspectRatio,
                });
                expect(result.left).toBe(0);
                expect(result.width).toBe(1);
                expect(result.height).toBeCloseTo(0.31640625, 5);
                expect(result.top).toBeCloseTo(0.341796875, 5);
            });
        });

        describe('with targetAspectRatio 9/16 (0.5625)', () => {
            const targetAspectRatio = 9 / 16;

            it('should return full area when original aspect ratio equals target (9:16)', () => {
                const result = MediaCropComputer.computeArea({
                    originalAspectRatio: 9 / 16,
                    targetAspectRatio,
                });
                expect(result).toEqual({
                    left: 0,
                    top: 0,
                    width: 1,
                    height: 1,
                });
            });

            it('should crop horizontally when original is landscape (16:9 to 9:16)', () => {
                const result = MediaCropComputer.computeArea({
                    originalAspectRatio: 16 / 9,
                    targetAspectRatio,
                });
                expect(result.top).toBe(0);
                expect(result.height).toBe(1);
                expect(result.width).toBeCloseTo(0.31640625, 5);
                expect(result.left).toBeCloseTo(0.341796875, 5);
            });

            it('should crop horizontally when original is square (1:1 to 9:16)', () => {
                const result = MediaCropComputer.computeArea({
                    originalAspectRatio: 1,
                    targetAspectRatio,
                });
                expect(result.top).toBe(0);
                expect(result.height).toBe(1);
                expect(result.width).toBe(0.5625);
                expect(result.left).toBe(0.21875);
            });

            it('should crop horizontally when original is slightly portrait (4:5 to 9:16)', () => {
                const result = MediaCropComputer.computeArea({
                    originalAspectRatio: 4 / 5,
                    targetAspectRatio,
                });
                expect(result.top).toBe(0);
                expect(result.height).toBe(1);
                expect(result.width).toBeCloseTo(0.703125, 5);
                expect(result.left).toBeCloseTo(0.1484375, 5);
            });

            it('should crop horizontally when original is very tall (3:4 to 9:16)', () => {
                const result = MediaCropComputer.computeArea({
                    originalAspectRatio: 3 / 4,
                    targetAspectRatio,
                });
                expect(result.top).toBe(0);
                expect(result.height).toBe(1);
                expect(result.width).toBe(0.75);
                expect(result.left).toBe(0.125);
            });

            it('should crop vertically when original is extremely tall (1:2 to 9:16)', () => {
                const result = MediaCropComputer.computeArea({
                    originalAspectRatio: 0.5,
                    targetAspectRatio,
                });
                expect(result.left).toBe(0);
                expect(result.width).toBe(1);
                expect(result.height).toBeCloseTo(0.8888889, 5);
                expect(result.top).toBeCloseTo(0.0555556, 5);
            });

            it('should crop horizontally when original is ultra-wide (21:9 to 9:16)', () => {
                const result = MediaCropComputer.computeArea({
                    originalAspectRatio: 21 / 9,
                    targetAspectRatio,
                });
                expect(result.top).toBe(0);
                expect(result.height).toBe(1);
                expect(result.width).toBeCloseTo(0.2410714, 5);
                expect(result.left).toBeCloseTo(0.3794643, 5);
            });
        });

        describe('edge cases', () => {
            it('should handle very small aspect ratios (1:10)', () => {
                const result = MediaCropComputer.computeArea({
                    originalAspectRatio: 0.1,
                    targetAspectRatio: 9 / 16,
                });
                expect(result.left).toBe(0);
                expect(result.width).toBe(1);
                expect(result.height).toBeCloseTo(0.1777778, 5);
                expect(result.top).toBeCloseTo(0.4111111, 5);
            });

            it('should handle very large aspect ratios (10:1)', () => {
                const result = MediaCropComputer.computeArea({
                    originalAspectRatio: 10,
                    targetAspectRatio: 16 / 9,
                });
                expect(result.top).toBe(0);
                expect(result.height).toBe(1);
                expect(result.width).toBeCloseTo(0.1777778, 5);
                expect(result.left).toBeCloseTo(0.4111111, 5);
            });

            it('should handle identical aspect ratios with floating point precision', () => {
                const result = MediaCropComputer.computeArea({
                    originalAspectRatio: 1.7777777777777777,
                    targetAspectRatio: 16 / 9,
                });
                expect(result.left).toBeCloseTo(0, 5);
                expect(result.top).toBeCloseTo(0, 5);
                expect(result.width).toBeCloseTo(1, 5);
                expect(result.height).toBeCloseTo(1, 5);
            });

            it('should ensure left + width <= 1 for horizontal crops', () => {
                const result = MediaCropComputer.computeArea({
                    originalAspectRatio: 2.5,
                    targetAspectRatio: 1,
                });
                expect(result.left + result.width).toBeLessThanOrEqual(1);
            });

            it('should ensure top + height <= 1 for vertical crops', () => {
                const result = MediaCropComputer.computeArea({
                    originalAspectRatio: 0.4,
                    targetAspectRatio: 1,
                });
                expect(result.top + result.height).toBeLessThanOrEqual(1);
            });

            it('should center crop horizontally', () => {
                const result = MediaCropComputer.computeArea({
                    originalAspectRatio: 2,
                    targetAspectRatio: 1,
                });
                // For horizontal crop, left should equal the right margin
                const rightMargin = 1 - (result.left + result.width);
                expect(result.left).toBeCloseTo(rightMargin, 10);
            });

            it('should center crop vertically', () => {
                const result = MediaCropComputer.computeArea({
                    originalAspectRatio: 0.5,
                    targetAspectRatio: 1,
                });
                // For vertical crop, top should equal the bottom margin
                const bottomMargin = 1 - (result.top + result.height);
                expect(result.top).toBeCloseTo(bottomMargin, 10);
            });
        });
    });
});
