import { minBy } from 'lodash';

import { PublicationType } from '../../posts';

const ASPECT_RATIOS_FOR_POST = {
    portrait: 4 / 5,
    landscape: 16 / 9,
    square: 1,
};
const ASPECT_RATIO_FOR_REEL = 9 / 16;
const ASPECT_RATIO_FOR_STORY = 9 / 16;

interface Area {
    left: number;
    top: number;
    width: number;
    height: number;
}

export class MediaCropComputer {
    static computePreferredAspectRatioFor(
        params:
            | { publicationType: PublicationType.POST; originalAspectRatio: number }
            | { publicationType: PublicationType.REEL | PublicationType.STORY }
    ): number {
        switch (params.publicationType) {
            case PublicationType.POST:
                return MediaCropComputer._computePreferredAspectRatioForPost(params.originalAspectRatio);
            case PublicationType.REEL:
                return ASPECT_RATIO_FOR_REEL;
            case PublicationType.STORY:
                return ASPECT_RATIO_FOR_STORY;
            default:
                return 1;
        }
    }

    private static _computePreferredAspectRatioForPost(originalAspectRatio: number): number {
        const squareTolerance = 0.01;
        const isCloseToSquare = Math.abs(ASPECT_RATIOS_FOR_POST.square - originalAspectRatio) < squareTolerance;
        if (isCloseToSquare) {
            return 1;
        }

        const { portrait, landscape } = ASPECT_RATIOS_FOR_POST;
        const preferredAspectRatio = minBy([portrait, landscape], (e) => Math.abs(originalAspectRatio - e));

        if (!preferredAspectRatio) {
            throw new Error('[TransformDataComputerService.computePreferredAspectRatioForPost] preferredAspectRatio is null');
        }

        return preferredAspectRatio;
    }

    static computeArea({ originalAspectRatio, targetAspectRatio }: { originalAspectRatio: number; targetAspectRatio: number }): Area {
        let left: number;
        let top: number;
        let width: number;
        let height: number;
        if (originalAspectRatio > targetAspectRatio) {
            top = 0;
            height = 1;
            width = targetAspectRatio / originalAspectRatio;
            left = (1 - width) / 2;
        } else if (originalAspectRatio < targetAspectRatio) {
            left = 0;
            width = 1;
            height = originalAspectRatio / targetAspectRatio;
            top = (1 - height) / 2;
        } else {
            top = 0;
            left = 0;
            width = 1;
            height = 1;
        }
        return {
            left,
            top,
            width,
            height,
        };
    }
}
