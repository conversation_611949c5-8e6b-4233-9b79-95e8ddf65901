import { Locale } from '../../constants';
import { MalouAttributesEnum } from '../attributes';

export const DEFAULT_IMAGE_DESCRIPTION = 'should_generate_image_description';

export const DEFAULT_PLACEHOLDER_IMAGE_URL = 'https://malou-production.s3.eu-west-3.amazonaws.com/assets/placeholder.png';

export const DEFAULT_PIN_IMAGE_URL = 'https://malou-production.s3.eu-west-3.amazonaws.com/store-locator/shared/pin.png';

export enum StoreLocatorLanguage {
    EN = 'en',
    FR = 'fr',
    IT = 'it',
    ES = 'es',
    UNDETERMINED = 'undetermined', // Used when the primary language is not set or is unknown
}

export enum StoreLocatorPageStatus {
    DRAFT = 'draft',
    PUBLISHED = 'published',
}

export enum StoreLocatorRestaurantPageElementIds {
    INFORMATION_WRAPPER = 'information-wrapper',
    INFORMATION_TITLE = 'information-title',
    INFORMATION_ICONS = 'information-icons',
    INFORMATION_OPENING_SOON_BANNER = 'information-opening-soon-banner',
    INFORMATION_BANNER = 'information-banner',
    INFORMATION_BANNER_CTA_1 = 'information-banner-cta-1',
    INFORMATION_BANNER_CTA_2 = 'information-banner-cta-2',
    INFORMATION_BANNER_CTA_ICON = 'information-banner-cta-icon',
    INFORMATION_HOURS_STATUS_HIGHLIGHT = 'information-hours-highlight',
    GALLERY_WRAPPER = 'gallery-wrapper',
    GALLERY_TITLE = 'gallery-title',
    GALLERY_PICTURE = 'gallery-picture',
    REVIEWS_WRAPPER = 'reviews-wrapper',
    REVIEWS_TITLE = 'reviews-title',
    REVIEWS_CTA = 'reviews-cta',
    CALL_TO_ACTIONS_WRAPPER = 'call-to-actions-wrapper',
    CALL_TO_ACTIONS_TITLE = 'call-to-actions-title',
    CALL_TO_ACTIONS_CTA = 'call-to-actions-cta',
    SOCIAL_NETWORKS_WRAPPER = 'social-networks-wrapper',
    SOCIAL_NETWORKS_TITLE = 'social-networks-title',
    SOCIAL_NETWORKS_PROFILE = 'social-networks-profile',
    SOCIAL_NETWORKS_PROFILE_NAME = 'social-networks-profile-name',
    DESCRIPTIONS_BLOCK_EVEN = 'descriptions-block-even',
    DESCRIPTIONS_BLOCK_TITLE_EVEN = 'descriptions-block-title-even',
    DESCRIPTIONS_BLOCK_UNEVEN = 'descriptions-block-uneven',
    DESCRIPTIONS_BLOCK_TITLE_UNEVEN = 'descriptions-block-title-uneven',
    DESCRIPTIONS_BLOCK_SUBTITLE = 'descriptions-block-subtitle',
    FAQ_WRAPPER = 'faq-wrapper',
    FAQ_TITLE = 'faq-title',
    FAQ_ITEM = 'faq-item',
    FAQ_ITEM_QUESTION = 'faq-item-question',
    FAQ_ITEM_ANSWER = 'faq-item-answer',
    FAQ_ICON_WRAPPER = 'faq-icon-wrapper',
    FAQ_ICON = 'faq-icon',
}

export enum GenerateStoreLocatorContentType {
    RESTAURANT_PAGE_URL_GENERATION = 'restaurant_page_url_generation',
    H1_TITLE_GENERATION = 'h1_title_generation',
    HEAD_META_DESCRIPTION_GENERATION = 'head_meta_description_generation',
    HEAD_META_TWITTER_DESCRIPTION_GENERATION = 'head_meta_twitter_description_generation',
    GALLERY_BLOCK_TITLE_GENERATION = 'gallery_block_title_generation',
    GALLERY_BLOCK_SUBTITLE_GENERATION = 'gallery_block_subtitle_generation',
    DESCRIPTION_BLOCK_GENERATION = 'description_block_generation',
    DESCRIPTION_BLOCK_TITLE_GENERATION = 'description_block_title_generation',
    DESCRIPTION_BLOCK_SUBTITLE_GENERATION = 'description_block_subtitle_generation',
    DESCRIPTION_BLOCK_CONTENT_GENERATION = 'description_block_content_generation',
    ORGANIZATION_KEYWORDS_GENERATION = 'organization_keywords_generation',
    REVIEWS_BLOCK_TITLE_GENERATION = 'reviews_block_title_generation',
    SOCIAL_MEDIA_BLOCK_TITLE_GENERATION = 'social_media_block_title_generation',
    CTA_BLOCK_TITLE_GENERATION = 'cta_block_title_generation',
    FAQ_BLOCK_GENERATION = 'faq_block_generation',
    FAQ_BLOCK_QUESTION_GENERATION = 'faq_block_question_generation',
    FAQ_BLOCK_ANSWER_GENERATION = 'faq_block_answer_generation',
    FAQ_BLOCK_SINGULAR_GENERATION = 'faq_block_singular_generation',

    // Optimization
    H1_TITLE_OPTIMIZATION = 'h1_title_optimization',
    HEAD_META_DESCRIPTION_OPTIMIZATION = 'head_meta_description_optimization',
    GALLERY_BLOCK_TITLE_OPTIMIZATION = 'gallery_block_title_optimization',
    GALLERY_BLOCK_SUBTITLE_OPTIMIZATION = 'gallery_block_subtitle_optimization',
    DESCRIPTION_BLOCK_TITLE_OPTIMIZATION = 'description_block_title_optimization',
    DESCRIPTION_BLOCK_SUBTITLE_OPTIMIZATION = 'description_block_subtitle_optimization',
    DESCRIPTION_BLOCK_CONTENT_OPTIMIZATION = 'description_block_content_optimization',
    SOCIAL_MEDIA_BLOCK_TITLE_OPTIMIZATION = 'social_media_block_title_optimization',
    CTA_BLOCK_TITLE_OPTIMIZATION = 'cta_block_title_optimization',
    REVIEWS_BLOCK_TITLE_OPTIMIZATION = 'reviews_block_title_optimization',
    FAQ_BLOCK_QUESTION_OPTIMIZATION = 'faq_block_question_optimization',
    FAQ_BLOCK_ANSWER_OPTIMIZATION = 'faq_block_answer_optimization',

    // Map
    MAP_BLOCK_TITLE_GENERATION = 'map_block_title_generation',
    MAP_DESCRIPTION_GENERATION = 'map_description_generation',
    MAP_TWITTER_DESCRIPTION_GENERATION = 'map_twitter_description_generation',
    MAP_KEYWORDS_GENERATION = 'map_keywords_generation',
}

export enum StoreLocatorAiSettingsLanguageStyle {
    FORMAL = 'formal',
    INFORMAL = 'informal',
}

export enum StoreLocatorAiSettingsDefaultTone {
    SOPHISTICATED = 'sophisticated',
    NOSTALGIC = 'nostalgic',
    IMPACTFUL = 'impactful',
    HUMORISTIC = 'humoristic',
    ENGAGING = 'engaging',
    INFORMATIVE = 'informative',
    FRIENDLY = 'friendly',
    INSPIRING = 'inspiring',
}

export enum StoreLocatorCentralizationPageElementIds {
    MAP_AND_STORE_LIST_WRAPPER = 'map-and-store-list-wrapper',
    STORE_LIST_SEARCH_WRAPPER = 'store-list-search-wrapper',
    STORE_LIST_SEARCH_INPUT = 'store-list-search-input',
    STORE_LIST_SEARCH_ICON = 'store-list-search-icon',
    STORE_LIST_SEARCH_BUTTON = 'store-list-search-button',
    STORE_LIST_ITEMS_WRAPPER = 'store-list-items-wrapper',
    STORE_LIST_ITEM = 'store-list-item',
    STORE_LIST_ITEM_TITLE = 'store-list-item-title',
    STORE_LIST_ITEM_DISTANCE_BLOCK = 'store-list-item-distance-block',
    MAP_POPUP_WRAPPER = 'map-popup-wrapper',
    MAP_POPUP_TITLE = 'map-popup-title',
    MAP_MARKER_GROUP = 'map-marker-group',
    MAP_PAGE_STORE_NOT_OPEN_YET = 'map-page-store-not-open-yet',
    MAP_PAGE_ICONS = 'map-page-icons',
}

export enum StoreLocatorCommonElementIds {
    WHITE_MARK_WRAPPER = 'white-mark-wrapper',
    WHITE_MARK_LOGO = 'white-mark-logo',
}

export enum StoreLocatorFaqBlockQuestionType {
    LOCATION_QUESTION = 'location_question',
    OPENING_HOURS_QUESTION = 'opening_hours_question',
    DELIVERY_TAKEOUT_QUESTION = 'delivery_takeout_question',
    RESERVATION_QUESTION = 'reservation_question',
    PARKING_QUESTION = 'parking_question',
    SPECIALTY_QUESTION = 'specialty_question',
    DIETARY_OPTIONS_QUESTION = 'dietary_options_question',
    PROMOTIONS_EVENTS_QUESTION = 'promotions_events_question',
}

export const RELEVANT_ATTRIBUTES = [
    {
        attributeId: MalouAttributesEnum.SERVES_ORGANIC,
        priority: 1,
        fr: 'bio',
        en: 'organic',
    },
    {
        attributeId: MalouAttributesEnum.SERVES_VEGAN,
        priority: 2,
        fr: 'vegan friendly',
        en: 'vegan friendly',
    },
    {
        attributeId: MalouAttributesEnum.HAS_TAKEOUT,
        priority: 3,
        fr: 'à emporter',
        en: 'takeout',
    },
    {
        attributeId: MalouAttributesEnum.HAS_ALL_YOU_CAN_EAT_ALWAYS,
        priority: 4,
        fr: 'à volonté',
        en: 'all you can eat',
    },
    {
        attributeId: MalouAttributesEnum.SERVES_VEGETARIAN,
        priority: 5,
        fr: 'végétarien friendly',
        en: 'vegetarian friendly',
    },
    {
        attributeId: MalouAttributesEnum.SERVES_BRUNCH,
        priority: 6,
        fr: 'brunch',
        en: 'brunch',
    },
    {
        attributeId: MalouAttributesEnum.HAS_CHILDRENS_MENU,
        priority: 7,
        fr: 'menu enfant',
        en: "children's menu",
    },
    {
        attributeId: MalouAttributesEnum.HAS_LIVE_MUSIC,
        priority: 8,
        fr: 'concert',
        en: 'live music',
    },
    {
        attributeId: MalouAttributesEnum.HAS_DELIVERY,
        priority: 9,
        fr: 'livraison',
        en: 'delivery',
    },
    {
        attributeId: MalouAttributesEnum.HAS_SEATING_OUTDOORS,
        priority: 10,
        fr: 'terrasse',
        en: 'terrace',
    },
    {
        attributeId: MalouAttributesEnum.SERVES_HALAL_FOOD,
        priority: 11,
        fr: 'halal',
        en: 'halal',
    },
    {
        attributeId: MalouAttributesEnum.FREE_WI_FI,
        priority: 12,
        fr: 'wifi',
        en: 'wifi',
    },
    {
        attributeId: MalouAttributesEnum.PAID_WI_FI,
        priority: 13,
        fr: 'wifi',
        en: 'wifi',
    },
    {
        attributeId: MalouAttributesEnum.HAS_CURBSIDE_PICKUP,
        priority: 14,
        fr: 'drive',
        en: 'drive through',
    },
    {
        attributeId: MalouAttributesEnum.HAS_DRIVE_THROUGH,
        priority: 15,
        fr: 'drive',
        en: 'drive through',
    },
    {
        attributeId: MalouAttributesEnum.HAS_CABARET,
        priority: 16,
        fr: 'cabaret',
        en: 'cabaret',
    },
    {
        attributeId: MalouAttributesEnum.ACCEPTS_RESERVATIONS,
        priority: 17,
        fr: 'réservation possible',
        en: 'booking available',
    },
    {
        attributeId: MalouAttributesEnum.SERVES_HAPPY_HOUR_DRINKS,
        priority: 18,
        fr: 'happy hour',
        en: 'happy hour',
    },
    {
        attributeId: MalouAttributesEnum.SERVES_HAPPY_HOUR_FOOD,
        priority: 19,
        fr: 'happy hour',
        en: 'happy hour',
    },
    {
        attributeId: MalouAttributesEnum.HAS_ALL_YOU_CAN_DRINK,
        priority: 20,
        fr: 'boisson à volonté',
        en: 'unlimited drink',
    },
    {
        attributeId: MalouAttributesEnum.HAS_LIVE_PERFORMANCES,
        priority: 21,
        fr: 'spectacles',
        en: 'shows',
    },
    {
        attributeId: MalouAttributesEnum.SERVES_BREAKFAST,
        priority: 22,
        fr: 'petit déjeuner',
        en: 'breakfast',
    },
    {
        attributeId: MalouAttributesEnum.SERVES_LATE_NIGHT_FOOD,
        priority: 23,
        fr: 'service tard le soir',
        en: 'late night service',
    },
    {
        attributeId: MalouAttributesEnum.ALLOWS_DOGS_INSIDE,
        priority: 24,
        fr: 'animaux acceptés',
        en: 'pets allowed',
    },
    {
        attributeId: MalouAttributesEnum.SUITABLE_FOR_WATCHING_SPORTS,
        priority: 25,
        fr: 'diffuse le sport',
        en: 'broadcasts sports',
    },
    {
        attributeId: MalouAttributesEnum.SERVES_SMALL_PLATES,
        priority: 26,
        fr: 'assiettes à partager',
        en: 'sharing plates',
    },
    {
        attributeId: MalouAttributesEnum.HAS_AIR_CONDITIONING,
        priority: 27,
        fr: 'climatisation',
        en: 'air conditioning',
    },
    {
        attributeId: MalouAttributesEnum.HAS_CHILD_CARE,
        priority: 28,
        fr: 'kids friendly',
        en: 'kids friendly',
    },
    {
        attributeId: MalouAttributesEnum.HAS_PRIVATE_DINING_ROOM,
        priority: 29,
        fr: 'salle privée',
        en: 'private room',
    },
];

export enum StoreLocatorJobStatus {
    PENDING = 'pending',
    CANCELLED = 'cancelled',
    SUCCEEDED = 'succeeded',
    FAILED = 'failed',
}

export enum StoreLocatorJobType {
    CONTENT_GENERATION = 'content_generation',
    PUBLICATION = 'publication',
}

export const getDefaultReviewAvatar = (reviewerName?: string): { initials: string; color: string } => {
    const colors = [
        '#8B5E3C', // Warm Brown
        '#556B2F', // Dark Olive Green
        '#1F487E', // Deep Blue
        '#622569', // Dark Purple
        '#9A3B3B', // Muted Red
        '#2C3E50', // Charcoal Blue
        '#4E342E', // Dark Wood
        '#5D6D7E', // Steel Gray
        '#3E2723', // Espresso
        '#34495E', // Navy Gray
    ];
    const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const randomLetter = alphabet[Math.floor(Math.random() * alphabet.length)];

    const initials = reviewerName?.split(' ')?.[0]?.charAt(0)?.toUpperCase() || randomLetter;
    const color = colors[initials.charCodeAt(0) % colors.length];

    return {
        initials,
        color,
    };
};

export function mapStoreLocatorLanguageToMalouLocale(storeLocatorLanguage: StoreLocatorLanguage): Locale {
    return (
        {
            [StoreLocatorLanguage.FR]: Locale.FR,
            [StoreLocatorLanguage.EN]: Locale.EN,
            [StoreLocatorLanguage.IT]: Locale.IT,
            [StoreLocatorLanguage.ES]: Locale.ES,
        }[storeLocatorLanguage] || Locale.FR
    );
}

export function formatHourInLocaleTime(lang: StoreLocatorLanguage, timeString: string | null | undefined): string {
    if (!timeString) {
        return '';
    }
    const [hours, minutes] = timeString.split(':').map(Number);
    const date = new Date();
    date.setHours(hours, minutes, 0, 0);

    switch (lang) {
        case StoreLocatorLanguage.FR:
            return `${hours}h${minutes.toString().padStart(2, '0')}`;
        default:
            return new Intl.DateTimeFormat(mapStoreLocatorLanguageToMalouLocale(lang), {
                hour: 'numeric',
                minute: 'numeric',
            }).format(date);
    }
}
