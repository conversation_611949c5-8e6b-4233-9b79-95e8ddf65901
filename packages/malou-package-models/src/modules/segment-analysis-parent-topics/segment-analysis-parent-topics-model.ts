import { FromSchema } from 'json-schema-to-ts';
import mongoose from 'mongoose';

import { DESERIALIZE_OPTIONS } from ':core/mongoose-json-schema/definitions/constants';
import { createMongooseSchemaFromJSONSchema } from ':core/mongoose-json-schema/definitions/jsonschema-to-mongoose';

import { segmentAnalysisParentTopicsJSONSchema } from './segment-analysis-parent-topics-schema';

const segmentAnalysisParentTopicsSchema = createMongooseSchemaFromJSONSchema(segmentAnalysisParentTopicsJSONSchema);

segmentAnalysisParentTopicsSchema.virtual('translations', {
    ref: 'Translations',
    localField: 'translationsId',
    foreignField: '_id',
    justOne: true,
});

segmentAnalysisParentTopicsSchema.index({ restaurantId: 1, name: 1, category: 1, subcategory: 1, sentiment: 1 }, { unique: true });

export type ISegmentAnalysisParentTopics = FromSchema<
    typeof segmentAnalysisParentTopicsJSONSchema,
    {
        keepDefaultedPropertiesOptional: true;
        deserialize: DESERIALIZE_OPTIONS;
    }
>;

export const SegmentAnalysisParentTopicModel = mongoose.model<ISegmentAnalysisParentTopics>(
    segmentAnalysisParentTopicsJSONSchema.title,
    segmentAnalysisParentTopicsSchema
);
