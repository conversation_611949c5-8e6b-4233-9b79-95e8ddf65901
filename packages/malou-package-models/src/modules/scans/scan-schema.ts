import { NfcType, ScanPlatformKey } from '@malou-io/package-utils';

import { JSONSchemaExtraProps } from ':core/mongoose-json-schema/definitions/types';

export const scanJSONSchema = {
    $schema: 'http://json-schema.org/draft-06/schema#',
    title: 'Scans',
    type: 'object',
    additionalProperties: false,
    properties: {
        _id: {
            type: 'string',
            format: 'objectId',
        },
        nfcId: {
            type: 'string',
            format: 'objectId',
        },
        scannedAt: {
            type: 'string',
            format: 'date-time',
        },
        nfcSnapshot: {
            $ref: '#/definitions/NFCSnapshot',
        },
        redirectedAt: {
            type: 'string',
            format: 'date-time',
        },
        createdAt: {
            type: 'string',
            format: 'date-time',
        },
        updatedAt: {
            type: 'string',
            format: 'date-time',
        },
        isCheckedForMatchingReview: {
            type: 'boolean',
        },
        // TOD<PERSON> <PERSON> delete after cleanup
        matchedReviewId: {
            type: 'string',
            format: 'objectId',
        },
        matchedReviewSocialId: {
            type: 'string',
        },
        starClicked: {
            type: 'integer',
        },
    },
    required: ['_id', 'createdAt', 'nfcId', 'nfcSnapshot', 'scannedAt', 'updatedAt'],
    definitions: {
        NFCSnapshot: {
            type: 'object',
            additionalProperties: false,
            properties: {
                chipName: {
                    type: 'string',
                },
                restaurantId: {
                    type: 'string',
                    format: 'objectId',
                    ref: 'Restaurant',
                },
                active: {
                    type: 'boolean',
                },
                platformKey: {
                    enum: Object.values(ScanPlatformKey),
                },
                redirectionLink: {
                    type: 'string',
                },
                starsRedirected: {
                    type: 'array',
                    items: {
                        type: 'integer',
                    },
                },
                _id: {
                    type: 'string',
                    format: 'objectId',
                },
                createdAt: {
                    type: 'string',
                    format: 'date-time',
                },
                updatedAt: {
                    type: 'string',
                    format: 'date-time',
                },
                name: {
                    type: 'string',
                },
                notes: {
                    type: 'string',
                    nullable: true,
                },
                type: {
                    enum: Object.values(NfcType),
                },
                askForEmail: {
                    type: 'boolean',
                },
            },
            required: [
                '_id',
                'active',
                'createdAt',
                'platformKey',
                'redirectionLink',
                'restaurantId',
                'starsRedirected',
                'updatedAt',
                'type',
            ],
            title: 'NFCSnapshot',
        },
    },
} as const satisfies JSONSchemaExtraProps;
