import { NfcsPlatformKey, NfcType, urlWithLocalHostRegex } from '@malou-io/package-utils';

import { JSONSchemaExtraProps } from ':core/mongoose-json-schema/definitions/types';

export const nfcJSONSchema = {
    $schema: 'http://json-schema.org/draft-06/schema#',
    title: 'Nfcs',
    type: 'object',
    additionalProperties: false,
    properties: {
        _id: {
            type: 'string',
            format: 'objectId',
        },
        chipName: {
            type: 'string',
            nullable: true,
        },
        restaurantId: {
            type: 'string',
            format: 'objectId',
        },
        platformKey: {
            enum: Object.values(NfcsPlatformKey),
        },
        redirectionLink: {
            type: 'string',
            format: 'uri',
            match: urlWithLocalHostRegex,
        },
        active: {
            type: 'boolean',
        },
        createdAt: {
            type: 'string',
            format: 'date-time',
        },
        updatedAt: {
            type: 'string',
            format: 'date-time',
        },
        starsRedirected: {
            type: 'array',
            items: {
                type: 'integer',
                minimum: 1,
                maximum: 5,
            },
        },
        notes: {
            type: 'string',
            nullable: true,
        },
        type: {
            enum: Object.values(NfcType),
        },
        askForEmail: {
            type: 'boolean',
        },
    },
    required: ['_id', 'active', 'createdAt', 'restaurantId', 'platformKey', 'redirectionLink', 'starsRedirected', 'updatedAt', 'type'],
    definitions: {},
} as const satisfies JSONSchemaExtraProps;
