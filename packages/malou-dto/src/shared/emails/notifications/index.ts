import { z } from 'zod';

import { Locale } from '@malou-io/package-utils';

import { basicReviewValidator } from '../reviews';

const baseEmailValidator = z.object({
    locale: z.nativeEnum(Locale),
    receiver: z.string().optional(),
    trackingUrl: z.string().optional(),
    link: z.string(),
    unsubscribeLink: z.string().optional(),
});

type BaseEmailProps = z.infer<typeof baseEmailValidator>;

// ----------------------------------------------------------

export const reviewReminderEmailNotificationValidator = z.object({
    review: basicReviewValidator,
    restaurantName: z.string(),
    unansweredReviewsCount: z.number(),
    otherRestaurantsReviewCount: z.number(),
    otherRestaurantNames: z.array(z.string()),
});

export type ReviewReminderEmailNotificationProps = z.infer<typeof reviewReminderEmailNotificationValidator> & BaseEmailProps;

// ----------------------------------------------------------

export const specialHourReminderEmailNotificationValidator = z.object({
    eventStartDate: z.date(),
    eventEmoji: z.string(),
    eventName: z.string(),
});

export type SpecialHourReminderEmailNotificationProps = z.infer<typeof specialHourReminderEmailNotificationValidator> & BaseEmailProps;

// ----------------------------------------------------------

export const postEventReminderEmailNotificationValidator = z.object({
    eventEmoji: z.string().optional(),
    eventName: z.string(),
    restaurantNames: z.array(z.string()),
});

export type PostEventReminderEmailNotificationProps = z.infer<typeof postEventReminderEmailNotificationValidator> & BaseEmailProps;

// ----------------------------------------------------------

export const roiActivatedEmailNotificationValidator = z.object({
    restaurantNames: z.array(z.string()),
    isAtLeastOneRestaurantWithoutRoiSettings: z.boolean(),
});

export type RoiActivatedEmailNotificationProps = z.infer<typeof roiActivatedEmailNotificationValidator> & BaseEmailProps;

// ----------------------------------------------------------
