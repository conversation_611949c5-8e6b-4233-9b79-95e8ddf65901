import { z } from 'zod';

import { objectIdValidator } from '../utils';

export const duplicateMediaForPublicationParamsValidator = z.object({
    mediaId: objectIdValidator,
});
export type DuplicateMediaForPublicationParamsDto = z.infer<typeof duplicateMediaForPublicationParamsValidator>;

export const duplicateMediaForPublicationBodyValidator = z.object({
    restaurantIds: z.array(objectIdValidator),
});
export type DuplicateMediaForPublicationBodyDto = z.infer<typeof duplicateMediaForPublicationBodyValidator>;

export type DuplicateMediaForPublicationResponseDto = { duplicatedMediaId: string; restaurantId: string }[];
