import { z } from 'zod';

import { MediaType } from '@malou-io/package-utils';

import { objectIdValidator } from '../utils/validators';

export const getMediaForEditionPathValidator = z.object({
    mediaId: objectIdValidator,
});

export type GetMediaForEditionPathDto = z.infer<typeof getMediaForEditionPathValidator>;

export type GetMediaForEditionResponseDto = {
    id: string;
    url: string;
    thumbnail1024OutsideUrl: string;
    thumbnail256OutsideUrl: string;
    aspectRatio: number;
    aiDescription?: string | null;
    name?: string;
} & (
    | {
          type: MediaType.PHOTO;
      }
    | {
          type: MediaType.VIDEO;
          videoUrl: string;
          /** the duration of the video in seconds */
          duration?: number | undefined;
          timelinePreviewFrames256hUrls?: string[];
      }
);
