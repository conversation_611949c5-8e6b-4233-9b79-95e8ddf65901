import { z } from 'zod';

import { NfcStar, NfcType, PlatformKey, ScanPlatformKey } from '@malou-io/package-utils';

import { NfcDto } from '../nfc';
import type { ReviewResponseDto } from '../review';
import { TimestampeableDto } from '../utils';
import { objectIdValidator } from '../utils/validators';

export interface ScanDto extends TimestampeableDto {
    id: string;
    nfcId: string;
    scannedAt: string;
    nfcSnapshot?: NfcSnapshotDto;
    redirectedAt?: string;
    starClicked?: NfcStar;
    matchedReviewSocialId?: string;
    matchedReview?: ReviewResponseDto;
}

interface LightScanDto {
    id: string;
    nfcId: string;
    nfcSnapshot: Pick<NfcSnapshotDto, 'name' | 'chipName' | 'restaurantId' | 'platformKey'> & {
        isRedirectingToWof: boolean;
    };
}

interface LightScanWithReviewRatingAndKeyDto extends LightScanDto {
    matchedReview: {
        id: string;
        restaurantId: string;
        rating?: number;
        key: PlatformKey;
    } | null;
    starClicked?: number;
    scannedAt: string;
}

export interface ScanForAggregatedInsightsDto {
    scansEvolution: number;
    wofScansEvolution: number;
    scans: LightScanWithReviewRatingAndKeyDto[];
    estimatedReviewCountPerRestaurant: {
        [restaurantId: string]: {
            estimatedReviewCount: number;
            estimatedReviewCountDifferenceWithPreviousPeriod: number;
        };
    };
    totemReviewsPerRestaurant: {
        reviewCountDifferenceWithPreviousPeriod: number;
        privateReviewCountDifferenceWithPreviousPeriod: number;
        reviewsPerPlatform: {
            key: PlatformKey;
            reviewsCount: {
                count: number;
                restaurantId: string;
            }[];
        }[];
        privateReviewsPerRating: {
            rating: number;
            reviewsCount: {
                count: number;
                restaurantId: string;
            }[];
        }[];
    };
}

export interface LightReviewForInsightDto {
    id: string;
    rating?: number;
    key: PlatformKey;
    restaurantId: string;
}

export interface ScanForRestaurantInsightsDto {
    scans: LightScanWithReviewRatingAndKeyDto[];
    previousScans: LightScanWithReviewRatingAndKeyDto[];
}

export interface ScanWithNfcDto extends ScanDto {
    nfc?: NfcDto;
}

export interface NfcSnapshotDto extends TimestampeableDto {
    id: string;
    chipName?: string;
    restaurantId: string;
    active: boolean;
    name?: string | null;
    platformKey: ScanPlatformKey;
    redirectionLink: string;
    notes?: string | null;
    starsRedirected: NfcStar[];
    type: NfcType;
    askForEmail?: boolean;
}

export const nfcSnapshotDtoValidator = z.object({
    id: objectIdValidator,
    chipName: z.string().optional(),
    restaurantId: objectIdValidator,
    active: z.boolean(),
    name: z.string().nullish(),
    platformKey: z.nativeEnum(ScanPlatformKey),
    redirectionLink: z.string().url(),
    notes: z.string().nullish(),
    starsRedirected: z.array(z.nativeEnum(NfcStar)),
    type: z.nativeEnum(NfcType),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
    askForEmail: z.boolean().optional(),
});
