import { z } from 'zod';

import { PlatformKey, PostPublicationStatus, RecurrentStoryFrequency } from '@malou-io/package-utils';

import { userTagsListValidator } from '../posts-v2';
import { objectIdValidator } from '../utils/validators';

export const updateStoryParamsValidator = z.object({
    storyId: objectIdValidator,
});

export type UpdateStoryParamsDto = z.infer<typeof updateStoryParamsValidator>;

export const updateStoryBodyValidator = z.object({
    platformKeys: z.array(z.nativeEnum(PlatformKey)).optional(),
    published: z.nativeEnum(PostPublicationStatus).optional(),
    plannedPublicationDate: z.string().datetime().optional(),
    medias: z
        .array(
            z.object({
                uploadedMediaId: objectIdValidator,
                editedMediaId: objectIdValidator.nullish(),
                serializedPinturaEditorOptions: z.string().nullish(),
                alternativeText: z.string().nullish(),
            })
        )
        .optional(),
    userTagsList: userTagsListValidator.optional(),
    recurrentStoryFrequency: z.nativeEnum(RecurrentStoryFrequency).optional(),
    feedbacksId: objectIdValidator.optional(),
});

export type UpdateStoryBodyDto = z.infer<typeof updateStoryBodyValidator>;
