import z from 'zod';

import { PlatformKey, PostPublicationStatus, RecurrentStoryFrequency } from '@malou-io/package-utils';

import { GetStoryMediaForEditionResponseDto } from '../media/get-story-media-for-edition.dto';
import { UserTagsList } from '../posts';
import { postFeedbacksValidator } from '../posts-v2';
import { objectIdValidator } from '../utils';

export const getStoryForEditionParamsValidator = z.object({
    storyId: objectIdValidator,
});

export type GetStoryForEditionParamsDto = z.infer<typeof getStoryForEditionParamsValidator>;

export interface GetStoryForEditionResponseDto {
    id: string;
    platformKeys: PlatformKey[];
    published: PostPublicationStatus;
    isPublishing: boolean;
    plannedPublicationDate?: string;
    medias: {
        uploadedMedia: GetStoryMediaForEditionResponseDto;
        editedMedia?: GetStoryMediaForEditionResponseDto | null;
        serializedPinturaEditorOptions?: string | null;
        alternativeText?: string | null;
    }[];
    recurrentStoryFrequency?: RecurrentStoryFrequency;
    feedbacks?: z.infer<typeof postFeedbacksValidator>;
    userTagsList: (UserTagsList[] | null)[];
}
