import { PuppeteerLambdaEventName } from '@malou-io/package-service-interfaces';

import { handler } from '..';

// Always comment unsused event payloads to avoid build errors
// const _insightsPdfEvent_1 = {
//     eventType: PuppeteerLambdaEventName.INSIGHTS_PDF,
//     payload: {
//         jwtToken:
//             'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJfaWQiOiI1ZmUzMWRjYzIzZDEyMTE5NjZkMzFjYjQiLCJleHAiOjE3Njg1ODU3NzAuNDkzLCJpYXQiOjE3MzcwNDk3NzB9.tQetPAOQdMO4nx7CWriQI43gWV9iOss-AoG3_SXh98U',
//         baseUrl: 'http://staging.omni.malou.io',
//         callBackUrl: 'statistics-pdf/6650bb8f24db48c14d5302eb/social-networks',
//         pdfParams:
//             '{"displayedCharts":["community","engagement","post_insights","reel_insights","story_insights"],"chartOptions":{"community":{"viewBy":"DAY","hiddenDatasetIndexes":[]},"engagement":{"viewBy":"DAY","hiddenDatasetIndexes":[]},"post_insights":{"tableSortOptions":{"active":"createdAt","direction":"desc"}},"reel_insights":{},"story_insights":{}},"dates":{"startDate":"2025-07-15T22:00:00.000Z","endDate":"2025-07-23T21:59:59.999Z"},"platformKeys":["facebook","instagram"],"timeZone":"Europe/Paris","comparisonPeriod":"previous_period"}',
//         awsBucketName: 'malou-staging',
//         awsBucketPath: `insights-pdf/6650bb8f24db48c14d5302eb/${Date.now()}.pdf`,
//     },
// };

const _deliverooPlatformRatingEvent = {
    eventType: PuppeteerLambdaEventName.DELIVEROO_PLATFORM_RATING,
    payload: {
        drnId: 'a63d885b-aa51-4d99-877e-1bf1bbcb6df9',
        accessToken: 'get-from-deliveroo-network-tab',
    },
};

console.log('Invoking Lambda function with event:', _deliverooPlatformRatingEvent);

async function invoke() {
    const result = await handler(_deliverooPlatformRatingEvent);
    console.log('Lambda function invoked successfully:', result.data);
}

invoke().catch((error) => {
    console.error('Error invoking Lambda function:', error);
});
