import pp from 'puppeteer-core';
import { addExtra } from 'puppeteer-extra';
import StealthPlugin from 'puppeteer-extra-plugin-stealth';

import {
    PuppeteerDeliverooPlatformRatingResponse,
    PuppeteerLambdaEvent,
    PuppeteerLambdaEventName,
} from '@malou-io/package-service-interfaces';

export async function handler(
    event: PuppeteerLambdaEvent<PuppeteerLambdaEventName.DELIVEROO_PLATFORM_RATING>
): Promise<PuppeteerDeliverooPlatformRatingResponse> {
    try {
        const puppeteer = addExtra(pp);
        puppeteer.use(StealthPlugin());

        const loginUrl = 'https://partner-hub.deliveroo.com/login';
        const { drnId, accessToken } = event.payload;

        const browser = await puppeteer.launch({
            headless: true,
            dumpio: true,
            timeout: 60000,
            executablePath: '/opt/chrome/linux-137.0.7151.70/chrome-linux64/chrome',
            args: [
                '--no-sandbox',
                '--disable-gpu',
                '--disable-dev-shm-usage',
                '--disable-dev-tools',
                '--no-zygote',
                '--remote-debugging-port=9222',
            ],
        });

        const page = await browser.newPage();

        await page.goto(loginUrl, { waitUntil: 'networkidle2' });

        const response = await page.evaluate(
            async ([drnIdParam, tokenParam]: [string, string]) => {
                const loginResponse = await fetch('https://partner-hub.deliveroo.com/api-gw/reviews/v2/current_rating', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        Authorization: `Bearer ${tokenParam}`,
                        accept: 'application/json',
                        'User-Agent':
                            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36',
                        Connection: 'keep-alive',
                    },
                    body: JSON.stringify({
                        branchDrnIds: [drnIdParam],
                    }),
                });

                return await loginResponse.json();
            },
            [drnId, accessToken]
        );

        await page.close();
        await browser.close();

        if (response?.Message?.match(/User is not authorized/)) {
            throw new Error('Deliveroo token is invalid or expired.');
        }

        return { success: !!response?.CurrentRating?.length, data: response };
    } catch (error) {
        return { success: false, errorMessage: (error as Error).message };
    }
}
