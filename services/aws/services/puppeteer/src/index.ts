import { PuppeteerLambdaEvent, PuppeteerLambdaEventName } from '@malou-io/package-service-interfaces';

import { handler as deliverooPlatformRatingHandler } from './deliveroo-platform-rating/handler';
import { handler as insightsPdfHandler } from './insights-pdf/handler';

export const handler = async (event: PuppeteerLambdaEvent) => {
    switch (event.eventType) {
        case PuppeteerLambdaEventName.INSIGHTS_PDF:
            return insightsPdfHandler(event as PuppeteerLambdaEvent<PuppeteerLambdaEventName.INSIGHTS_PDF>);
        case PuppeteerLambdaEventName.DELIVEROO_PLATFORM_RATING:
            return deliverooPlatformRatingHandler(event as PuppeteerLambdaEvent<PuppeteerLambdaEventName.DELIVEROO_PLATFORM_RATING>);
        default:
            console.error(`Unsupported event type: ${event.eventType}`, JSON.stringify(event));
            throw new Error(`Unsupported event type: ${event.eventType}`);
    }
};
